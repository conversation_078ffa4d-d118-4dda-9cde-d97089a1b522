# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Environment variables and sensitive files
.env
.env.*
!.env.example
backend/.env
backend/.env.*
!backend/.env.example

# Database files
*.db
*.sqlite
*.sqlite3
xhs_notes.db
backend/xhs_notes.db

# Logs
*.log
backend/backend_debug.log
backend/logs/
logs/

# Backup files
backup_*/
*.backup
backend/backup_*/

# Upload directories (user data)
uploads/
backend/uploads/

# Node.js (Frontend)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
.pnpm-debug.log*

# Frontend build
frontend/dist/
frontend/dist-ssr/
frontend/coverage/

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
*.tmp
*.temp
新建*

# Temporary files
*.tmp
*.temp
demo.html
test-*.html

# Configuration backups
*.config.backup
vite.config.*.backup

# Runtime files
*.pid
*.sock
