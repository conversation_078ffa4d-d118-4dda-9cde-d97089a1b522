# 更新日志

本文档记录了小红书笔记管理系统的所有重要更新和变更。

## [2.8.0] - 2025-08-03

### ✨ 新功能

#### 笔记管理页面优化
- 📝 **编号显示**: 笔记列表前面增加序号显示（1. 2. 3. ...）
- 🔍 **内容状态指示**: 新增图标显示笔记是否包含内容、封面图、笔记图、视频
  - 📝 蓝色笔记图标：表示有内容
  - 🖼️ 绿色照片图标：表示有封面图
  - 📷 橙色相机图标：表示有笔记图片
  - 🎥 紫色视频图标：表示有视频
- 🎨 **用户体验**: 鼠标悬停显示图标含义，直观了解笔记内容状态

#### 文件上传限制优化
- 🎥 **视频上传**: 视频文件上传限制从10MB提升至500MB
- 🖼️ **图片上传**: 图片文件保持10MB限制不变
- 🔧 **智能验证**: 前端根据文件类型动态显示不同的大小限制提示

### 🔧 技术改进
- 📁 **配置统一**: 更新所有配置文件和部署文档中的文件大小限制
- 🛠️ **代码优化**: 优化文件上传组件，支持不同文件类型的差异化处理
- 📚 **文档更新**: 更新相关文档和部署指南

## [2.7.1] - 2025-07-30

### 🐛 Bug修复

#### 商品搜索功能修复
- 🔍 **搜索功能修复**: 修复笔记管理页面商品搜索功能不生效的问题
- 🔧 **前端优化**: 商品搜索时同时触发笔记重新加载，确保搜索结果立即显示
- 🔗 **API增强**: 后端API支持在商品标题和简称中搜索，扩展搜索范围
- 📊 **搜索逻辑**: 将商品搜索关键词合并到笔记搜索参数中，实现跨表搜索

#### TypeScript构建错误修复
- 🛠️ **类型修复**: 修复van-tag组件size属性的TypeScript类型错误
- 🎨 **样式优化**: 通过CSS控制商品标记大小，保持视觉效果一致
- ✅ **构建成功**: 确保npm run build命令能够成功执行

### 🔧 技术改进

#### 搜索功能增强
- 🔍 **实时搜索**: 商品搜索支持实时筛选相关笔记
- 🎯 **精确匹配**: 支持商品名称的模糊搜索和部分匹配
- 🔄 **组合搜索**: 支持笔记内容搜索和商品搜索同时使用

#### 代码质量提升
- 📝 **类型安全**: 修复TypeScript类型检查错误
- 🎨 **样式统一**: 优化标记组件的样式实现方式
- 🔧 **构建优化**: 确保生产环境构建流程正常

### 📁 修改文件列表
- `frontend/src/views/NoteManagementView.vue` - 修复商品搜索功能和类型错误
- `backend/app/api/v1/endpoints/notes_download.py` - 扩展API搜索范围

## [2.7.0] - 2025-07-30

### ✨ 新增功能

#### 首页快捷操作优化
- 🏠 **笔记管理入口**: 在首页快捷操作区域新增"笔记管理"按钮
- 🎨 **图标优化**: 使用更合适的图标和渐变色彩搭配
- ⚡ **快速访问**: 一键跳转到笔记管理页面，提升操作效率

#### 笔记管理页面增强
- 🔍 **商品搜索功能**: 新增商品搜索框，支持按商品名称快速筛选笔记
- 🏷️ **标记显示优化**:
  - 重新设计标记布局，采用垂直排列方式
  - 为使用状态标记添加图标（时钟图标表示未使用，成功图标表示已使用）
  - 新增商品名称标记，显示笔记关联的商品信息
  - 改进标记的视觉层次和间距设计
- 📝 **标题显示优化**:
  - 支持标题换行显示，最多显示2行
  - 添加智能换行处理，确保长标题能完整显示
  - 优化文字排版和行高设置
- 📐 **卡片布局调整**: 增加卡片高度（从180px增加到200px）以容纳更多内容信息

### 🔧 技术改进

#### 数据源优化
- 🔗 **API接口升级**: 使用 `/api/v1/notes-download/preview` 接口，支持获取包含商品信息的笔记数据
- 🚀 **搜索功能增强**: 商品搜索支持实时筛选，提升用户体验

#### 配置修复
- 🔧 **代理配置修复**: 修复前端开发环境代理端口配置问题（从9000改为8000）
- 🌐 **网络连接优化**: 确保前后端通信正常，提升系统稳定性

### 📁 修改文件列表
- `frontend/src/views/HomeView.vue` - 添加笔记管理快捷操作
- `frontend/src/views/NoteManagementView.vue` - 增强笔记管理功能
- `frontend/vite.config.ts` - 修复代理端口配置
- `backend/create_test_user.py` (新增) - 测试用户创建脚本
- `backend/check_users.py` (新增) - 用户查看脚本
- `README.md` - 更新版本信息和功能说明

### 🎯 用户体验提升
- ✅ **操作便捷性**: 首页新增笔记管理入口，减少导航层级
- ✅ **信息展示**: 笔记卡片显示更多有用信息（商品名称、状态图标）
- ✅ **搜索效率**: 支持按商品名称搜索，快速定位相关笔记
- ✅ **视觉体验**: 优化标记布局和标题显示，提升界面美观度

## [2.6.2] - 2025-01-30

### 🐛 Bug修复

#### 剪贴板复制功能修复
- 📋 **复制功能修复**: 修复了笔记详情页面复制按钮失败的问题
- 🔧 **兼容性增强**: 实现了现代剪贴板API和传统方法的自动切换
- 🌐 **浏览器兼容**: 支持HTTP环境和各种浏览器版本的剪贴板操作
- 🛠️ **错误处理**: 完善的错误捕获和用户友好的提示信息

### 🔧 技术改进

#### 剪贴板工具库
- 📦 **通用工具**: 创建了 `src/utils/clipboard.ts` 通用剪贴板工具
- 🔄 **自动降级**: 现代API失败时自动使用 `document.execCommand` 降级方案
- 🔍 **环境检测**: 自动检测浏览器兼容性和安全上下文
- 📱 **移动端支持**: 优化了移动设备的复制体验

#### 前端组件优化
- 🎯 **NoteDetailView**: 使用新的剪贴板工具，简化复制逻辑
- 🎯 **ProductDownloadDetailView**: 同步更新复制功能实现
- ✅ **空值检查**: 添加了复制内容的空值验证
- 🧹 **代码清理**: 移除了重复的复制函数实现

### 📁 修改文件列表
- `frontend/src/utils/clipboard.ts` (新增)
- `frontend/src/views/NoteDetailView.vue`
- `frontend/src/views/ProductDownloadDetailView.vue`
- `docs/troubleshooting.md` (新增)

### ✅ 验证结果
- ✅ **HTTP环境**: 在HTTP环境下复制功能正常工作
- ✅ **HTTPS环境**: 在HTTPS环境下使用现代API
- ✅ **浏览器兼容**: 支持Chrome、Firefox、Safari、Edge等主流浏览器
- ✅ **错误处理**: 复制失败时显示友好的错误提示

## [2.6.1] - 2025-07-28

### 🐛 Bug修复

#### 图片显示问题修复
- 🖼️ **图片路径修复**: 修复了笔记详情页面和编辑页面图片无法正常显示的问题
- 🔗 **URL路径统一**: 统一了前后端图片URL路径处理逻辑
- 📁 **静态文件服务**: 优化了静态文件服务的路径映射机制
- 🌐 **端口统一**: 修复了前端图片URL错误使用8000端口的问题，统一使用3000端口

### 🔧 技术改进

#### 后端优化
- 🛠️ **deserialize_files函数重构**: 更新了所有API端点中的文件路径处理函数
- 📂 **路径前缀自动添加**: 自动为相对路径添加`/uploads/`前缀，生成完整的静态文件URL
- 🔄 **向下兼容**: 保持对已有完整URL的兼容性，避免重复添加前缀
- 📊 **影响范围**: 修复了notes、products、notes_download、products_download、recycle_bin等所有相关API

#### 前端优化
- 🎨 **FileUpload组件优化**: 改进了图片URL构建逻辑，正确处理带前缀的文件路径
- 🌐 **端口修正**: 修复了组件中硬编码的8000端口，改为正确的3000端口
- 🔍 **路径检测**: 添加了智能路径检测，避免重复添加`/uploads/`前缀
- 📱 **显示一致性**: 确保编辑页面和详情页面的图片显示一致性

### 📁 修改文件列表
- `backend/app/api/v1/endpoints/notes_download.py`
- `backend/app/api/v1/endpoints/products_download.py`
- `backend/app/api/v1/endpoints/notes.py`
- `backend/app/api/v1/endpoints/products.py`
- `backend/app/api/v1/endpoints/recycle_bin.py`
- `frontend/src/components/FileUpload.vue`

### ✅ 验证结果
- ✅ **笔记详情页面**: 图片正常显示
- ✅ **笔记编辑页面**: 图片正常显示
- ✅ **URL格式正确**: `http://localhost:3000/uploads/images/xxx.png`
- ✅ **兼容性保证**: 对已有数据和新上传文件都能正确处理

## [2.6.0] - 2025-07-27

### ✨ 新增功能

#### 卡片布局全面优化
- 🎨 **笔记管理页面优化**: 重新设计为紧凑的网格卡片布局，支持横向排布多个卡片
- 📱 **响应式网格**: 智能网格布局，桌面端一行显示4-5个卡片，移动端自适应
- 🖼️ **笔记下载页面优化**: 优化卡片尺寸，缩略图从80px缩小到60px，整体更紧凑美观
- ✨ **视觉统一**: 统一的圆角8px、阴影效果、悬停动画，提升整体视觉体验

#### 笔记管理卡片优化
- 📏 **尺寸优化**: 卡片高度固定为180px，宽度最小280px，确保整齐排列
- 📝 **内容精简**: 标题单行显示，内容限制3行，按钮尺寸更小
- 🎯 **交互提升**: 悬停效果增强，边框变色、阴影加深、轻微上移
- 📱 **响应式设计**: 不同屏幕尺寸显示不同数量的卡片

#### 笔记下载卡片优化
- 🖼️ **缩略图优化**: 从80x80px缩小到60x60px，图标从24px缩小到18px
- 📝 **文字优化**: 标题从16px减少到14px，描述从14px减少到12px
- 🏷️ **标签优化**: 状态标签字体从12px减少到10px，内边距减少
- 📸 **图片预览优化**: 小图标从32x32px缩小到24x24px，间距从6px减少到3px

### 🔧 技术改进

#### 前端组件优化
- 🎨 **NoteManagementView**: 从垂直列表改为网格布局，使用CSS Grid实现响应式排布
- 🎴 **NotePreviewCard**: 全面优化卡片尺寸和内容布局，提升视觉密度
- 📐 **响应式断点**: 优化不同屏幕尺寸的网格列数和间距设置
- ⚡ **性能优化**: 减少DOM元素大小，提升渲染性能

#### CSS架构优化
- 🎯 **网格系统**: 使用`grid-template-columns: repeat(auto-fill, minmax(280px, 1fr))`
- 📱 **响应式设计**: 完善的移动端、平板端、桌面端适配
- 🎨 **视觉层次**: 优化字体大小层次，提升可读性
- ✨ **动画效果**: 统一的悬停动画和过渡效果

### 🎯 用户体验提升
- 📊 **信息密度**: 同屏显示更多内容，提升浏览效率
- 🎨 **视觉美观**: 更紧凑的布局，更精致的细节设计
- 📱 **设备适配**: 完美适配各种屏幕尺寸，从手机到大屏显示器
- ⚡ **操作效率**: 更小的按钮和更紧凑的布局，提升操作效率

## [2.5.0] - 2025-07-27

### ✨ 新增功能

#### 商品管理卡片布局优化
- 🎨 **现代化设计**: 重新设计商品管理页面，采用横向卡片布局
- 📋 **完整信息展示**: 显示商品缩略图、标题、价格、笔记数量、归属用户、创建时间
- 🖼️ **图片预览**: 显示前4张图片，超出显示"+N"
- 📱 **响应式设计**: PC端横向布局，手机端纵向布局，完美适配各种屏幕
- ✨ **交互优化**: 悬停动画、图片预览、确认删除对话框

#### 笔记管理功能
- 📝 **独立管理页面**: 全新的笔记管理页面，提供完整的笔记管理功能
- 🔍 **搜索和筛选**: 支持标题内容搜索、使用状态筛选、关联商品筛选
- 📊 **统计信息**: 实时显示总笔记数、未使用数、已使用数、使用率
- 🎴 **笔记卡片**: 显示笔记缩略图、标题、内容预览、状态标签、关联商品
- ⚙️ **操作功能**: 编辑、详情、状态切换、删除等完整操作
- 🧭 **导航集成**: 在侧边栏菜单中添加笔记管理入口

#### 设计系统统一
- 🎨 **卡片设计**: 统一的圆角12px、阴影效果、悬停动画
- 🌈 **颜色方案**: 价格标签绿色、状态标签蓝色/红色、商品标签绿色
- 📐 **响应式布局**: CSS Grid网格系统，自适应屏幕尺寸
- 👤 **用户体验**: 加载状态、空状态提示、错误处理、操作确认

### 🔧 技术改进

#### 后端API扩展
- 🆕 **新增API**: `GET /api/v1/notes/` 获取笔记列表，支持搜索、筛选、分页
- 📈 **统计API**: `GET /api/v1/notes/stats` 获取笔记统计信息，支持筛选条件
- 🔐 **权限控制**: 用户权限控制，通过Product关联检查权限
- 🗄️ **数据库优化**: 修复字段错误，使用正确的deleted_at字段

#### 前端组件优化
- 🧩 **新增组件**: ProductManageCard、NoteManageCard、NoteManagementView
- 🎯 **响应式网格**: CSS Grid `repeat(auto-fit, minmax(400px, 1fr))`
- 🖼️ **图片懒加载**: van-image组件自带懒加载
- 📄 **分页加载**: 支持无限滚动分页

### 🐛 Bug修复
- 🎯 **菜单高亮**: 修复商品下载页面时商品管理菜单错误高亮
- 🔗 **详情跳转**: 修复商品卡片详情按钮跳转路径错误
- 🚫 **界面优化**: 移除商品卡片中的特点分析显示
- 🔌 **API修复**: 修复笔记管理页面API调用404错误

## [1.6.0] - 2025-07-26

### ✨ 新增功能

#### 商品用户关联管理
- 👥 **用户指定**: 管理员创建商品时可以指定归属用户
- 🔄 **用户变更**: 管理员可以修改商品的归属用户
- 👀 **用户显示**: 商品列表和详情页显示归属用户信息
- 🔐 **权限控制**: 普通用户只能管理自己的商品

#### 菜单栏快捷操作
- ⚡ **快捷按钮**: 左侧菜单新增创建商品、创建笔记、批量创建笔记按钮
- 🎨 **视觉区分**: 快捷操作按钮采用特殊样式，易于识别
- 📱 **响应式**: 完美适配桌面端和移动端
- 🔗 **智能跳转**: 创建笔记功能支持先选择商品再创建

#### 批量创建界面优化
- 📏 **大尺寸窗口**: 默认800x600像素，最大可达屏幕90%
- 🖱️ **拖拽调整**: 支持鼠标拖拽调整对话框大小
- 📊 **行号显示**: 左侧显示行号，方便查看笔记数量
- 🚫 **无数量限制**: 移除50个标题的限制，支持无限制创建
- 💻 **代码编辑器体验**: 类似代码编辑器的界面设计

### 🔧 技术改进

#### 后端API增强
- 🛠️ **用户列表API**: 新增获取用户列表的管理员专用接口
- 📊 **商品信息扩展**: 商品API返回包含用户信息的完整数据
- 🔄 **关联查询优化**: 使用JOIN查询提高数据获取效率
- 🚫 **限制移除**: 批量创建API移除数量限制

#### 前端组件优化
- 🎛️ **用户选择器**: 新增用户选择组件，支持搜索和筛选
- 📱 **独立页面**: 创建独立的笔记创建和批量创建页面
- 🖱️ **拖拽交互**: 实现原生JavaScript拖拽调整功能
- 📏 **响应式布局**: 对话框大小自适应屏幕尺寸

#### 数据模型优化
- 🔗 **关联查询**: 优化商品和用户的关联查询性能
- 📊 **数据完整性**: 确保商品用户关联的数据一致性
- 🔄 **向下兼容**: 保持与现有数据的完全兼容

### 🎯 用户体验提升
- 🚀 **操作效率**: 快捷按钮减少导航点击次数
- 👀 **信息透明**: 清晰显示商品归属用户信息
- 💪 **批量处理**: 支持大规模笔记批量创建
- 🎨 **界面友好**: 更大的操作空间和更清晰的视觉反馈

## [1.5.0] - 2025-07-26

### ✨ 新增功能

#### 批量创建笔记功能
- 📝 **批量创建**: 支持为单个商品一次性创建多个笔记
- 📋 **多行输入**: 用户可在文本框中输入多个标题，每行一个
- 🔢 **数量限制**: 单次最多支持创建50个笔记
- 🎯 **智能处理**: 自动过滤空行、去除重复标题
- 📊 **实时反馈**: 显示标题数量和重复检测结果

#### 用户体验优化
- 🎨 **直观界面**: 在笔记管理页面添加"批量创建"按钮
- 💬 **友好提示**: 清晰的输入提示和操作指导
- ⚡ **即时验证**: 实时检查输入格式和数量限制
- 🔄 **自动刷新**: 创建完成后自动刷新笔记列表

### 🔧 技术实现

#### 后端API
- 🛠️ **新增端点**: `POST /api/v1/products/{product_id}/notes/batch`
- 🔐 **权限控制**: 只有商品所有者或管理员可以批量创建
- 📊 **数据验证**: 完整的输入验证和错误处理
- 🗃️ **批量操作**: 高效的数据库批量插入操作

#### 前端组件
- 🎛️ **对话框组件**: 专用的批量创建对话框
- 📝 **多行文本框**: 支持大量文本输入的文本域
- 🔍 **实时分析**: 动态分析输入内容并显示统计信息
- 🎨 **响应式设计**: 完美适配桌面端和移动端

#### 数据处理
- 🧹 **智能清理**: 自动去除空行和多余空格
- 🔄 **去重算法**: 保持顺序的重复标题去除
- 📏 **长度限制**: 防止过长标题影响系统性能
- 🛡️ **安全验证**: 防止恶意输入和SQL注入

### 🧪 质量保证

#### 测试覆盖
- ✅ **功能测试**: 完整的批量创建功能测试
- ✅ **边界测试**: 空输入、重复标题、超限等边界条件
- ✅ **权限测试**: 不同用户角色的权限验证
- ✅ **性能测试**: 大量数据的处理性能验证

#### 错误处理
- 🚨 **输入验证**: 完善的前端和后端输入验证
- 💬 **友好提示**: 清晰的错误信息和操作建议
- 🔄 **失败恢复**: 部分失败时的优雅处理
- 📝 **日志记录**: 详细的操作日志和错误追踪

### 🎯 业务价值
- ⚡ **效率提升**: 大幅提高内容创作的初始化效率
- 🎯 **批量操作**: 满足用户批量处理的实际需求
- 🔄 **工作流优化**: 简化从商品到笔记的创建流程
- 📈 **用户体验**: 提供更便捷的内容管理工具

## [1.4.0] - 2025-07-26

### ✨ 新增功能

#### 提示词管理重构
- 🔧 **统一管理**: 移除系统/个人提示词的区分，所有提示词统一由管理员管理
- 👀 **公共访问**: 普通用户可以查看和使用所有提示词，但不能编辑
- 🗂️ **简化界面**: 重构提示词列表页面，移除标签页设计
- 📊 **增强信息**: 显示提示词创建时间、更新时间等元数据

#### 用户管理系统
- 👥 **完整CRUD**: 管理员可以创建、查看、编辑、删除用户
- 🔍 **搜索筛选**: 支持按用户名搜索，按角色筛选
- 📈 **用户统计**: 显示用户的商品数量、笔记数量等统计信息
- 🛡️ **安全控制**: 防止管理员删除自己或修改自己的角色

#### 权限控制增强
- 🔐 **细化权限**: 更精确的API权限控制
- 🎯 **角色区分**: 管理员和普通用户的功能明确分离
- 🚫 **访问限制**: 普通用户无法访问管理功能

### 🔧 技术改进

#### 数据库优化
- 📊 **表结构更新**: 重构prompts表，移除prompt_type字段
- 🕒 **时间戳**: 添加created_at、updated_at字段
- 🔄 **数据迁移**: 完整的数据库迁移脚本，保留历史数据
- 💾 **备份机制**: 迁移前自动备份数据库

#### API重构
- 🛠️ **简化接口**: 提示词API去除复杂的权限逻辑
- 🆕 **新增端点**: 用户管理相关的完整API
- 📝 **文档更新**: API文档自动更新

#### 前端优化
- 🎨 **界面统一**: 用户管理页面采用统一的设计风格
- 📱 **响应式**: 完善的移动端适配
- ⚡ **性能提升**: 优化列表加载和搜索性能

### 🎯 用户体验提升
- 🎛️ **管理便捷**: 管理员可以方便地管理用户和提示词
- 🔍 **查找高效**: 强化的搜索和筛选功能
- 📊 **信息丰富**: 详细的用户统计和提示词元数据
- 🛡️ **操作安全**: 重要操作需要确认，防止误操作

## [1.3.0] - 2025-07-26

### ✨ 新增功能

#### 左侧导航栏布局
- 🎨 **现代化布局**: 将导航菜单移至页面左侧，采用现代化侧边栏设计
- 📱 **响应式设计**: 桌面端固定侧边栏，移动端抽屉式菜单
- 🔄 **智能收缩**: 支持菜单展开/收缩，节省屏幕空间
- 👤 **用户信息**: 侧边栏顶部显示当前用户信息和角色标识
- 🎯 **当前页面指示**: 清晰的当前页面高亮显示

#### 统一页面设计
- 📋 **标准化头部**: 统一的页面标题和操作按钮布局
- 🎨 **通用样式**: 创建common.css统一页面样式规范
- 📱 **移动端优化**: 完善的移动端页面头部设计
- 🔧 **组件化**: 可复用的页面布局组件

#### 权限控制增强
- 🔐 **菜单权限**: 根据用户角色显示不同的菜单项
- 🛡️ **路由守卫**: 增强的管理员权限验证
- 👥 **角色标识**: 清晰的用户角色显示

### 🔧 技术改进
- 🏗️ **AppLayout组件**: 新增主布局组件，统一管理页面结构
- 📱 **响应式断点**: 768px断点的完善移动端适配
- 🎨 **CSS变量**: 使用CSS变量支持主题定制
- ⚡ **性能优化**: 条件渲染和GPU加速动画

### 🎨 用户体验优化
- 🖱️ **交互反馈**: 丰富的悬停和激活状态视觉反馈
- 🔄 **平滑动画**: 菜单展开/收缩的流畅过渡效果
- 📐 **空间利用**: 收缩菜单释放更多内容显示空间
- 🎯 **操作效率**: 一键切换页面，减少导航点击次数

## [1.2.1] - 2025-07-26

### ✨ 新增功能

#### 粘贴上传系统
- 📋 **剪贴板粘贴**: 支持直接粘贴剪贴板中的图片和视频文件
- 🖱️ **智能焦点**: 点击上传区域获得焦点，显示"可粘贴文件"提示
- 📸 **截图支持**: 支持截图后直接粘贴上传
- 🌐 **网页图片**: 支持从网页复制图片后粘贴上传
- 📁 **文件复制**: 支持复制本地文件后粘贴上传

#### 拖拽上传系统
- 🎯 **拖拽上传**: 支持从文件管理器拖拽文件到上传区域
- 🎨 **视觉反馈**: 拖拽时显示绿色边框提示
- 📦 **批量拖拽**: 支持同时拖拽多个文件上传

#### 用户体验优化
- 💡 **操作提示**: 上传区域显示多种上传方式说明
- 🎨 **状态指示**: 焦点、拖拽、上传等状态的视觉反馈
- 📱 **移动端适配**: 优化移动端的粘贴和触摸操作
- ⌨️ **键盘支持**: 完整的键盘快捷键支持

### 🔧 技术改进
- 🧩 **事件处理**: 完善的paste、dragover、drop事件处理
- 🔒 **文件验证**: 增强的文件类型和大小验证
- 🚀 **性能优化**: 优化文件处理和上传流程
- 📋 **剪贴板API**: 使用现代浏览器剪贴板API

### 📱 兼容性
- ✅ **Chrome 76+**: 完整支持所有功能
- ✅ **Firefox 70+**: 完整支持所有功能
- ✅ **Safari 13+**: 完整支持所有功能
- ✅ **Edge 79+**: 完整支持所有功能

## [1.2.0] - 2025-07-26

### ✨ 新增功能

#### 文件上传系统
- 🖼️ **图片上传**: 支持商品主图、详情图和笔记图片上传
- 🎥 **视频上传**: 支持笔记视频文件上传
- 👁️ **文件预览**: 上传后即时预览功能
- 🗑️ **文件删除**: 可删除已上传的文件

#### 回收站管理
- 🗂️ **软删除机制**: 笔记删除后进入回收站
- 🔄 **恢复功能**: 管理员可恢复已删除的笔记
- 💀 **彻底删除**: 管理员可永久删除笔记
- 🔍 **搜索功能**: 在回收站中搜索已删除的笔记

#### 个人提示词管理
- ✍️ **创建提示词**: 用户可创建个人提示词
- ✏️ **编辑提示词**: 修改已有的个人提示词
- 🗑️ **删除提示词**: 删除不需要的提示词
- 📋 **变量支持**: 支持商品和笔记变量占位符

#### 用户体验优化
- 📋 **一键复制**: 笔记标题、正文、标签一键复制到剪贴板
- 🔐 **权限控制**: 路由级别的管理员权限验证
- 📱 **移动端优化**: 更好的移动端操作体验

### 🔧 技术改进
- 🧩 **组件化**: 新增FileUpload通用文件上传组件
- 🛣️ **路由优化**: 重构API路由配置，修复笔记创建问题
- 🧪 **测试完善**: 新增完整的功能集成测试
- 🔒 **安全增强**: 完善的权限控制和数据验证

### 🐛 修复问题
- 🔧 修复笔记创建API路由配置错误
- 🔧 修复文件上传路径显示问题
- 🔧 优化错误处理和用户提示

## [1.1.0] - 2025-07-22

### 🎨 PC端布局优化

这个版本主要解决了PC端界面布局问题，提升了桌面端用户体验。

### ✨ 新增功能

#### 响应式设计优化
- **PC端全屏显示**: 移除了宽度限制，登录页面和主页在PC端能够充分利用屏幕空间
- **智能布局切换**: 根据屏幕尺寸自动调整布局方式
  - 桌面端 (≥768px): 容器最大宽度1200-1400px，居中显示
  - 移动端 (<768px): 保持原有的单列布局
- **网格布局优化**:
  - 快捷操作区域：桌面端4列网格，移动端2列网格
  - 统计和活动区域：桌面端并排显示，移动端垂直排列

#### 登录页面改进
- **全屏背景**: 消除PC端左右空白区域
- **背景装饰增强**: 添加多层次渐变和装饰元素
- **响应式缩放**: 桌面端内容适当放大，提升视觉效果

#### 主页布局优化
- **容器宽度管理**: 桌面端使用合理的最大宽度限制
- **内容区域重构**: 统计和活动区域在桌面端使用2列网格布局
- **间距优化**: 不同屏幕尺寸使用不同的间距设置

### 🔧 技术改进

#### CSS架构优化
- 移除了 `main.css` 中的全局宽度限制
- 添加了完善的响应式断点设计
- 使用CSS Grid和Flexbox实现灵活布局
- 优化了移动端和桌面端的样式隔离

#### 代码质量提升
- 改进了CSS选择器的特异性管理
- 添加了更详细的响应式注释
- 统一了断点使用标准

### 🐛 问题修复
- 修复了PC端登录页面显示过窄的问题
- 修复了主页在桌面端布局不合理的问题
- 解决了容器宽度限制导致的空白区域问题

### 📱 兼容性
- 保持了移动端的完美显示效果
- 确保了各种屏幕尺寸的良好适配
- 测试通过了主流浏览器的兼容性

---

## [1.0.0] - 2025-07-22

### 🎉 首次发布

这是小红书笔记管理系统的首个正式版本，包含完整的核心功能。

### ✨ 新增功能

#### 后端功能
- **用户认证系统**: JWT Token认证，支持管理员和普通用户角色
- **商品管理**: 完整的CRUD操作，支持搜索、分页、排序
- **笔记管理**: 基于商品的笔记管理，支持状态管理和软删除
- **AI提示词管理**: 系统和个人提示词库，支持变量占位符
- **文件上传**: 支持图片和视频文件上传
- **回收站功能**: 软删除机制，支持恢复和彻底删除
- **操作日志**: 记录笔记的所有操作历史
- **API文档**: 自动生成的Swagger/OpenAPI文档

#### 前端功能
- **现代化UI**: 使用Vant 4组件库，移动端优先设计
- **用户认证**: 登录页面，自动token管理，路由守卫
- **商品管理界面**: 商品列表、创建/编辑表单，支持搜索和分页
- **笔记管理界面**: 笔记列表、创建/编辑表单，状态管理
- **AI提示词界面**: 系统和个人提示词展示，一键复制功能
- **个人资料页面**: 用户信息展示，退出登录
- **响应式设计**: 完美适配移动端和桌面端

### 🛠️ 技术实现

#### 后端技术栈
- Python 3.11 + FastAPI
- SQLAlchemy 2.0 (异步ORM)
- Pydantic (数据验证)
- JWT认证
- SQLite数据库
- Uvicorn ASGI服务器

#### 前端技术栈
- Vue 3 + TypeScript
- Vite 5.4.0 (构建工具)
- Vant 4 (移动端UI组件库)
- Pinia (状态管理)
- Vue Router 4 (路由管理)
- Axios (HTTP客户端)

### 📊 项目统计
- **后端API端点**: 30+ 个RESTful接口
- **前端页面**: 8个核心页面
- **数据模型**: 5个核心实体模型
- **代码文件**: 100+ 个文件
- **代码行数**: 3500+ 行

### 🔧 开发工具
- **数据库初始化脚本**: 自动创建表结构和默认数据
- **API测试脚本**: 验证后端API功能
- **集成测试脚本**: 验证前后端联调
- **开发服务器**: 支持热重载的开发环境

### 📝 文档
- **README.md**: 完整的项目介绍和使用指南
- **API文档**: Swagger/OpenAPI自动生成的接口文档
- **开发计划**: 详细的开发阶段规划
- **PRD文档**: 产品需求文档

### 🎯 默认配置
- **默认管理员账户**: admin / admin123
- **系统提示词**: 预置3个小红书相关的AI提示词
- **数据库**: SQLite (开发环境)
- **文件上传**: 支持常见图片和视频格式

### 🌐 部署信息
- **前端开发服务器**: http://localhost:3000
- **后端API服务器**: http://localhost:8000
- **API文档地址**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/health

### 🔄 已知问题
- 前端代理在某些环境下可能需要额外配置
- Vue DevTools插件在Node.js 18环境下存在兼容性问题（已禁用）

### 📈 下一步计划
- 添加更多AI提示词模板
- 实现文件上传的前端界面
- 添加数据导出功能
- 优化移动端用户体验
- 添加单元测试和E2E测试

---

## 版本说明

版本号格式：`主版本号.次版本号.修订号`

- **主版本号**: 不兼容的API修改
- **次版本号**: 向下兼容的功能性新增
- **修订号**: 向下兼容的问题修正
