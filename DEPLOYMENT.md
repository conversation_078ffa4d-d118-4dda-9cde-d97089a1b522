# 部署指南

本文档详细介绍了小红书笔记管理系统的部署方法。

## 📚 详细部署指南

根据您的操作系统选择对应的详细部署指南：

- **[Windows 生产环境部署指南](docs/windows-production-deployment.md)** - 适用于Windows Server和Windows桌面系统
- **[Linux 生产环境部署指南](docs/linux-production-deployment.md)** - 适用于Ubuntu、CentOS、Debian等Linux发行版

这些指南包含完整的生产环境部署步骤，包括系统服务配置、SSL证书、监控、备份等。

## 🏗️ 部署架构

```text
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Nginx/Apache  │    │   Frontend      │    │   Backend       │
│   (反向代理)     │───▶│   (Vue 3)       │───▶│   (FastAPI)     │
│   Port: 80/443  │    │   Port: 3000    │    │   Port: 8000    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                       │
                                               ┌─────────────────┐
                                               │   Database      │
                                               │   (SQLite/MySQL)│
                                               └─────────────────┘
```

## 🚀 生产环境部署

### 1. 服务器要求

**最低配置**:
- CPU: 1核
- 内存: 1GB
- 存储: 10GB
- 操作系统: Ubuntu 20.04+ / CentOS 7+

**推荐配置**:
- CPU: 2核
- 内存: 2GB
- 存储: 20GB
- 操作系统: Ubuntu 22.04 LTS

### 2. 环境准备

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Python 3.11
sudo apt install python3.11 python3.11-pip python3.11-venv -y

# 安装Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install nodejs -y

# 安装Nginx
sudo apt install nginx -y

# 安装Git
sudo apt install git -y
```

### 3. 项目部署

```bash
# 创建部署目录
sudo mkdir -p /var/www/xhs_notes_manager
sudo chown $USER:$USER /var/www/xhs_notes_manager

# 克隆项目
cd /var/www/xhs_notes_manager
git clone https://gitee.com/bin1874/xhs_notes_manager.git .

# 后端部署
cd backend
python3.11 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
pip install gunicorn

# 初始化数据库
python init_db.py

# 前端构建
cd ../frontend
npm install
npm run build
```

### 4. 后端服务配置

创建Gunicorn配置文件 `backend/gunicorn.conf.py`:

```python
# Gunicorn配置
bind = "127.0.0.1:8000"
workers = 2
worker_class = "uvicorn.workers.UvicornWorker"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100
timeout = 30
keepalive = 2
preload_app = True
```

创建systemd服务文件 `/etc/systemd/system/xhs-backend.service`:

```ini
[Unit]
Description=XHS Notes Manager Backend
After=network.target

[Service]
Type=notify
User=www-data
Group=www-data
WorkingDirectory=/var/www/xhs_notes_manager/backend
Environment=PATH=/var/www/xhs_notes_manager/backend/venv/bin
ExecStart=/var/www/xhs_notes_manager/backend/venv/bin/gunicorn -c gunicorn.conf.py app.main:app
ExecReload=/bin/kill -s HUP $MAINPID
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
```

启动后端服务:

```bash
sudo systemctl daemon-reload
sudo systemctl enable xhs-backend
sudo systemctl start xhs-backend
sudo systemctl status xhs-backend
```

### 5. Nginx配置

创建Nginx配置文件 `/etc/nginx/sites-available/xhs-notes-manager`:

```nginx
server {
    listen 80;
    server_name your-domain.com;  # 替换为你的域名
    
    # 前端静态文件
    location / {
        root /var/www/xhs_notes_manager/frontend/dist;
        try_files $uri $uri/ /index.html;
        
        # 缓存静态资源
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # 后端API代理
    location /api/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 文件上传代理
    location /uploads/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # API文档代理
    location /docs {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 健康检查
    location /health {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

启用站点:

```bash
sudo ln -s /etc/nginx/sites-available/xhs-notes-manager /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 6. SSL证书配置 (可选)

使用Let's Encrypt免费SSL证书:

```bash
sudo apt install certbot python3-certbot-nginx -y
sudo certbot --nginx -d your-domain.com
```

## 🐳 Docker部署

### 1. 后端Dockerfile

创建 `backend/Dockerfile`:

```dockerfile
FROM python:3.11-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 8000

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 2. 前端Dockerfile

创建 `frontend/Dockerfile`:

```dockerfile
FROM node:18-alpine as build

WORKDIR /app
COPY package*.json ./
RUN npm ci

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=build /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
```

### 3. Docker Compose

创建 `docker-compose.yml`:

```yaml
version: '3.8'

services:
  backend:
    build: ./backend
    ports:
      - "8000:8000"
    volumes:
      - ./backend/uploads:/app/uploads
      - ./backend/xhs_notes.db:/app/xhs_notes.db
    environment:
      - DATABASE_URL=sqlite+aiosqlite:///./xhs_notes.db

  frontend:
    build: ./frontend
    ports:
      - "80:80"
    depends_on:
      - backend
```

部署命令:

```bash
docker-compose up -d
```

## 🔧 配置说明

### 环境变量配置

生产环境需要修改 `backend/.env`:

```env
# 数据库配置
DATABASE_URL=sqlite+aiosqlite:///./xhs_notes.db

# 安全配置 - 请修改为随机字符串
SECRET_KEY=your-production-secret-key-here-change-this

# JWT配置
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=43200

# 文件上传配置
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=524288000
ALLOWED_IMAGE_EXTENSIONS=jpg,jpeg,png,gif,webp
ALLOWED_VIDEO_EXTENSIONS=mp4,mov,avi

# CORS配置
ALLOWED_ORIGINS=https://your-domain.com

# 生产环境配置
DEBUG=false
LOG_LEVEL=INFO
```

### 数据库配置

**SQLite (默认)**:
```env
DATABASE_URL=sqlite+aiosqlite:///./xhs_notes.db
```

**MySQL**:
```env
DATABASE_URL=mysql+aiomysql://username:password@localhost/xhs_notes
```

**PostgreSQL**:
```env
DATABASE_URL=postgresql+asyncpg://username:password@localhost/xhs_notes
```

## 🔍 监控和维护

### 日志管理

```bash
# 查看后端服务日志
sudo journalctl -u xhs-backend -f

# 查看Nginx日志
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
```

### 备份策略

```bash
# 数据库备份
cp /var/www/xhs_notes_manager/backend/xhs_notes.db /backup/xhs_notes_$(date +%Y%m%d_%H%M%S).db

# 上传文件备份
tar -czf /backup/uploads_$(date +%Y%m%d_%H%M%S).tar.gz /var/www/xhs_notes_manager/backend/uploads/
```

### 更新部署

```bash
cd /var/www/xhs_notes_manager
git pull origin master

# 更新后端
cd backend
source venv/bin/activate
pip install -r requirements.txt
sudo systemctl restart xhs-backend

# 更新前端
cd ../frontend
npm install
npm run build
sudo systemctl reload nginx
```

## 🚨 故障排除

### 常见问题

1. **后端服务无法启动**
   - 检查Python版本和依赖
   - 查看systemd日志: `sudo journalctl -u xhs-backend`

2. **前端页面无法访问**
   - 检查Nginx配置
   - 确认前端构建成功

3. **API请求失败**
   - 检查CORS配置
   - 确认代理配置正确

4. **数据库连接失败**
   - 检查数据库文件权限
   - 确认DATABASE_URL配置正确

### 性能优化

1. **启用Gzip压缩**
2. **配置静态文件缓存**
3. **使用CDN加速**
4. **数据库索引优化**
5. **启用HTTP/2**

## 📞 技术支持

如遇到部署问题，请：
1. 查看相关日志文件
2. 检查配置文件
3. 提交Issue到项目仓库
