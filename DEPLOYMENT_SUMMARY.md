# Ubuntu 24 + Nginx + MySQL 部署文档总结

## 📋 部署配置信息

### 服务器环境
- **操作系统**: Ubuntu 24.04 LTS
- **服务器名称**: ben
- **项目目录**: `/home/<USER>/xhs_notes_manager`

### 数据库配置
- **数据库类型**: MySQL 8.0+
- **数据库名**: xhsnote
- **数据库用户**: xhsnote_user
- **数据库密码**: dhwdw6789jhgffHGF

### 服务配置
- **Web服务器**: Nginx
- **应用服务器**: Gunicorn + Uvicorn
- **进程管理**: systemd
- **前端端口**: 8080 (Nginx，避免与现有服务冲突)
- **后端端口**: 9000 (内部，避免与现有服务冲突)

## 🔧 最新修复记录

### 2025-07-28: TypeScript类型错误修复
**问题**: Ubuntu部署时前端构建失败，出现大量TypeScript类型错误

**修复内容**:
1. **Vant组件属性类型错误**:
   - TagSize: `"small"`, `"mini"` → `"medium"` (Vant只支持`'large'`/`'medium'`)
   - ButtonType: `"info"` → `"default"` (Vant不支持`'info'`类型)

2. **函数参数类型不匹配**:
   - FileUpload beforeRead: `File` → `File | File[]` (匹配UploaderBeforeRead类型)

3. **可选属性访问问题**:
   - vite.config.debug.ts: 添加`proxyRes.statusCode`空值检查
   - NoteManageCard: 使用类型断言安全访问`img.url`属性

4. **对象属性补充**:
   - ProductFormView: 添加`owner_id`属性到form对象

**涉及文件**: 13个前端组件和配置文件
**结果**: 构建成功，所有TypeScript类型错误已解决

**文档更新**: 在`docs/bug-fix-experience.md`中新增TypeScript类型错误修复章节

### 2025-07-28: 端口配置更新 (避免冲突)
**问题**: 服务器上已有其他服务占用默认端口80和8000

**更新内容**:
1. **端口重新分配**:
   - 前端Nginx端口: 80 → 8080
   - 后端FastAPI端口: 8000 → 9000
   - MySQL端口: 3306 (保持不变)

2. **更新的配置文件**:
   - `scripts/ubuntu24-deploy.sh` - 自动部署脚本
   - `docs/ubuntu24-nginx-mysql-deployment.md` - 详细部署文档
   - `UBUNTU24_QUICK_START.md` - 快速开始指南
   - `DEPLOYMENT_SUMMARY.md` - 部署总结文档

3. **访问地址变更**:
   - 前端界面: `http://ben:8080`
   - API文档: `http://ben:9000/docs`
   - 健康检查: `http://ben:8080/health`

**影响**: 避免与现有服务的端口冲突，确保部署顺利进行

## 📁 创建的部署文件

### 1. 详细部署文档
- **文件**: `docs/ubuntu24-nginx-mysql-deployment.md`
- **内容**: 完整的手动部署步骤，包含所有配置细节

### 2. 自动部署脚本
- **文件**: `scripts/ubuntu24-deploy.sh`
- **功能**: 一键自动部署整个系统
- **特点**: 
  - 自动检测和安装依赖
  - 自动配置数据库
  - 自动配置Nginx和systemd服务
  - 彩色输出和详细日志

### 3. 环境检查脚本
- **文件**: `scripts/pre-deploy-check.sh`
- **功能**: 部署前环境检查
- **检查项目**:
  - 系统版本和权限
  - 硬件资源（内存、磁盘、CPU）
  - 网络连接
  - 端口占用
  - 必要命令和软件
  - 项目目录和文件

### 4. 快速开始指南
- **文件**: `UBUNTU24_QUICK_START.md`
- **内容**: 简化的部署步骤和常用命令

## 🚀 部署流程

### 方法1: 自动部署（推荐）

```bash
# 1. 准备项目代码
sudo mkdir -p /home/<USER>
sudo chown $USER:$USER /home/<USER>
cp -r /path/to/xhs_notes_manager /home/<USER>/

# 2. 运行环境检查
cd /home/<USER>/xhs_notes_manager
chmod +x scripts/pre-deploy-check.sh
./scripts/pre-deploy-check.sh

# 3. 自动部署
chmod +x scripts/ubuntu24-deploy.sh
./scripts/ubuntu24-deploy.sh
```

### 方法2: 手动部署

按照 `docs/ubuntu24-nginx-mysql-deployment.md` 文档中的详细步骤进行手动部署。

## 🔧 部署后管理

### 服务管理命令

```bash
# 启动/停止/重启后端服务
sudo systemctl start xhs-backend
sudo systemctl stop xhs-backend
sudo systemctl restart xhs-backend

# 查看服务状态
sudo systemctl status xhs-backend
sudo systemctl status nginx
sudo systemctl status mysql

# 查看日志
sudo journalctl -u xhs-backend -f
sudo tail -f /var/log/nginx/xhs-notes-manager.error.log
```

### 管理脚本

部署完成后会创建以下管理脚本：

```bash
# 启动所有服务
/home/<USER>/xhs_notes_manager/scripts/start.sh

# 停止所有服务
/home/<USER>/xhs_notes_manager/scripts/stop.sh

# 重启所有服务
/home/<USER>/xhs_notes_manager/scripts/restart.sh

# 查看系统状态
/home/<USER>/xhs_notes_manager/scripts/status.sh

# 查看日志
/home/<USER>/xhs_notes_manager/scripts/logs.sh [backend|nginx|mysql]

# 数据备份
/home/<USER>/xhs_notes_manager/scripts/backup.sh

# 系统维护
/home/<USER>/xhs_notes_manager/scripts/maintenance.sh
```

## 🌐 访问信息

### 应用访问地址
- **前端界面**: http://ben
- **API文档**: http://ben/docs
- **健康检查**: http://ben/health

### 默认管理员账户
- **用户名**: admin
- **密码**: admin123

## 🔒 安全配置

### 防火墙规则
```bash
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable
```

### SSL证书（可选）
```bash
# 安装Certbot
sudo apt install -y certbot python3-certbot-nginx

# 获取免费SSL证书
sudo certbot --nginx -d your-domain.com
```

## 📊 监控和维护

### 自动化任务

部署脚本会自动设置以下定时任务：

1. **每日备份** (凌晨2点)
   ```bash
   0 2 * * * /home/<USER>/xhs_notes_manager/scripts/backup.sh
   ```

2. **每周维护** (周日凌晨3点)
   ```bash
   0 3 * * 0 /home/<USER>/xhs_notes_manager/scripts/maintenance.sh
   ```

### 日志轮转

系统会自动管理日志文件：
- 应用日志保留30天
- Nginx日志保留30天
- 备份文件保留7天

## 🚨 故障排除

### 常见问题及解决方案

1. **服务启动失败**
   - 检查日志: `sudo journalctl -u xhs-backend -l`
   - 检查配置: `sudo nginx -t`
   - 检查权限: `ls -la /home/<USER>/xhs_notes_manager`

2. **数据库连接失败**
   - 测试连接: `mysql -u xhsnote_user -p'dhwdw6789jhgffHGF' xhsnote`
   - 检查服务: `sudo systemctl status mysql`

3. **网络访问问题**
   - 检查防火墙: `sudo ufw status`
   - 检查端口: `sudo netstat -tlnp | grep :80`
   - 检查Nginx: `sudo nginx -t && sudo systemctl reload nginx`

### 紧急恢复

```bash
# 从备份恢复数据库
mysql -u xhsnote_user -p'dhwdw6789jhgffHGF' xhsnote < /home/<USER>/backups/backup_database.sql

# 恢复配置文件
tar -xzf /home/<USER>/backups/backup_config.tar.gz -C /

# 重启服务
sudo systemctl restart xhs-backend nginx
```

## 📋 部署检查清单

部署完成后，请确认以下项目：

- [ ] 所有服务正常运行
- [ ] 前端页面可以访问
- [ ] API文档可以访问
- [ ] 管理员账户可以登录
- [ ] 文件上传功能正常
- [ ] 数据库连接正常
- [ ] 防火墙规则配置正确
- [ ] 备份脚本配置正确
- [ ] 日志记录正常

## 🎯 性能优化建议

### 系统级优化
1. 调整MySQL配置以适应服务器资源
2. 配置Nginx缓存策略
3. 启用Gzip压缩
4. 配置静态文件缓存

### 应用级优化
1. 配置合适的Gunicorn worker数量
2. 启用数据库连接池
3. 配置Redis缓存（可选）
4. 启用API响应缓存

## 📞 技术支持

如果在部署过程中遇到问题：

1. 首先运行环境检查脚本
2. 查看相关日志文件
3. 参考故障排除部分
4. 收集以下信息：
   - 系统版本: `lsb_release -a`
   - 服务状态: `sudo systemctl status xhs-backend nginx mysql`
   - 错误日志: `sudo journalctl -u xhs-backend --no-pager -l`
   - 网络状态: `sudo netstat -tlnp`

---

## 📝 部署文档文件列表

1. `docs/ubuntu24-nginx-mysql-deployment.md` - 详细部署文档
2. `scripts/ubuntu24-deploy.sh` - 自动部署脚本
3. `scripts/pre-deploy-check.sh` - 环境检查脚本
4. `UBUNTU24_QUICK_START.md` - 快速开始指南
5. `DEPLOYMENT_SUMMARY.md` - 本文档（部署总结）

**部署文档创建完成！** 🎉

现在你有了完整的Ubuntu 24 + Nginx + MySQL生产环境部署方案。
