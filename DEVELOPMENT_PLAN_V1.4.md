# 开发计划 v1.4.0

## 🎯 需求概述

根据用户反馈，需要对系统进行以下优化：

1. **提示词管理重构**：移除系统/个人提示词的区分，统一由管理员管理所有提示词
2. **用户管理功能**：管理员需要能够增删改查普通用户

## 📋 详细开发计划

### 阶段1: 提示词管理系统重构

#### 1.1 后端数据模型调整
- [ ] 修改 `Prompt` 模型，移除 `prompt_type` 字段
- [ ] 移除 `PromptType` 枚举
- [ ] 保留 `owner_id` 字段用于记录创建者（可选）
- [ ] 创建数据库迁移脚本

#### 1.2 后端API重构
- [ ] 修改 `/api/v1/prompts/` 接口，仅管理员可访问
- [ ] 简化提示词的增删改查逻辑
- [ ] 移除个人提示词相关的权限检查
- [ ] 更新API文档和响应模型

#### 1.3 前端界面重构
- [ ] 重构 `PromptListView.vue`，移除标签页设计
- [ ] 统一提示词列表展示
- [ ] 仅管理员可见提示词管理功能
- [ ] 更新路由和导航菜单

#### 1.4 数据迁移
- [ ] 将现有的系统提示词和个人提示词合并
- [ ] 保留创建者信息（如果需要）
- [ ] 清理无用的数据字段

### 阶段2: 用户管理功能开发

#### 2.1 后端API开发
- [ ] 创建用户管理API端点 `/api/v1/admin/users/`
- [ ] 实现用户列表查询（分页、搜索、筛选）
- [ ] 实现用户创建功能
- [ ] 实现用户信息更新功能
- [ ] 实现用户删除功能（软删除）
- [ ] 实现用户状态管理（启用/禁用）

#### 2.2 前端页面开发
- [ ] 创建用户管理页面 `UserManagementView.vue`
- [ ] 创建用户表单页面 `UserFormView.vue`
- [ ] 实现用户列表展示（表格形式）
- [ ] 实现用户搜索和筛选功能
- [ ] 实现用户创建/编辑表单
- [ ] 实现用户删除确认功能

#### 2.3 导航和权限
- [ ] 在侧边栏添加"用户管理"菜单项（仅管理员可见）
- [ ] 添加用户管理相关路由
- [ ] 实现权限控制，确保只有管理员可访问

### 阶段3: 测试和优化

#### 3.1 功能测试
- [ ] 测试提示词管理的所有功能
- [ ] 测试用户管理的所有功能
- [ ] 测试权限控制是否正确
- [ ] 测试数据迁移是否成功

#### 3.2 用户体验优化
- [ ] 优化界面布局和交互
- [ ] 添加操作确认和提示
- [ ] 完善错误处理和用户反馈
- [ ] 优化移动端适配

## 🔧 技术实现细节

### 数据库变更

#### Prompt表结构调整
```sql
-- 移除prompt_type字段
ALTER TABLE prompts DROP COLUMN prompt_type;

-- 可选：添加创建时间和更新时间
ALTER TABLE prompts ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE prompts ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;
```

#### 新增用户管理相关字段（如果需要）
```sql
-- 用户状态字段
ALTER TABLE users ADD COLUMN status ENUM('active', 'inactive') DEFAULT 'active';
ALTER TABLE users ADD COLUMN last_login_at TIMESTAMP NULL;
```

### API端点设计

#### 提示词管理API（重构后）
```
GET    /api/v1/prompts/           # 获取所有提示词（仅管理员）
POST   /api/v1/prompts/           # 创建提示词（仅管理员）
GET    /api/v1/prompts/{id}       # 获取单个提示词（仅管理员）
PUT    /api/v1/prompts/{id}       # 更新提示词（仅管理员）
DELETE /api/v1/prompts/{id}       # 删除提示词（仅管理员）
```

#### 用户管理API（新增）
```
GET    /api/v1/admin/users/       # 获取用户列表（仅管理员）
POST   /api/v1/admin/users/       # 创建用户（仅管理员）
GET    /api/v1/admin/users/{id}   # 获取用户详情（仅管理员）
PUT    /api/v1/admin/users/{id}   # 更新用户信息（仅管理员）
DELETE /api/v1/admin/users/{id}   # 删除用户（仅管理员）
PUT    /api/v1/admin/users/{id}/status  # 更新用户状态（仅管理员）
```

### 前端路由设计

```typescript
// 新增路由
{
  path: '/admin/users',
  name: 'user-management',
  component: () => import('@/views/UserManagementView.vue'),
  meta: { requiresAuth: true, requiresAdmin: true }
},
{
  path: '/admin/users/create',
  name: 'user-create',
  component: () => import('@/views/UserFormView.vue'),
  meta: { requiresAuth: true, requiresAdmin: true }
},
{
  path: '/admin/users/:id/edit',
  name: 'user-edit',
  component: () => import('@/views/UserFormView.vue'),
  meta: { requiresAuth: true, requiresAdmin: true }
}
```

## 📊 开发时间估算

| 阶段 | 任务 | 预估时间 |
|------|------|----------|
| 1.1 | 后端数据模型调整 | 2小时 |
| 1.2 | 后端API重构 | 3小时 |
| 1.3 | 前端界面重构 | 4小时 |
| 1.4 | 数据迁移 | 1小时 |
| 2.1 | 用户管理API开发 | 6小时 |
| 2.2 | 用户管理前端开发 | 8小时 |
| 2.3 | 导航和权限 | 2小时 |
| 3.1 | 功能测试 | 3小时 |
| 3.2 | 用户体验优化 | 2小时 |
| **总计** | | **31小时** |

## 🎯 验收标准

### 提示词管理
- [ ] 管理员可以查看所有提示词
- [ ] 管理员可以创建、编辑、删除提示词
- [ ] 普通用户只能查看和使用提示词，不能管理
- [ ] 界面简洁，无系统/个人提示词区分

### 用户管理
- [ ] 管理员可以查看所有用户列表
- [ ] 管理员可以创建新用户
- [ ] 管理员可以编辑用户信息
- [ ] 管理员可以删除用户
- [ ] 管理员可以启用/禁用用户
- [ ] 支持用户搜索和筛选

### 权限控制
- [ ] 只有管理员可以访问用户管理功能
- [ ] 只有管理员可以管理提示词
- [ ] 普通用户无法访问管理功能
- [ ] 路由级别的权限验证正常工作

## 🚀 部署计划

1. **开发环境测试**：在本地完成所有功能开发和测试
2. **数据备份**：部署前备份现有数据
3. **数据库迁移**：执行数据库结构变更
4. **代码部署**：部署新版本代码
5. **功能验证**：验证所有功能正常工作
6. **用户培训**：为管理员提供新功能使用说明

---

**计划制定时间**: 2025-07-26  
**目标完成时间**: 2025-07-26  
**版本号**: v1.4.0
