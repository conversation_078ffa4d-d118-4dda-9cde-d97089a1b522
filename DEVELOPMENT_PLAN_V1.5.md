# 开发计划 v1.5.0 - 批量创建笔记功能

## 🎯 需求概述

为提高内容创作效率，开发批量创建笔记功能，允许用户为单个商品一次性创建多个笔记。

### 核心功能
- 用户可以一次输入多个笔记标题（每行一个）
- 系统自动为每个标题创建一个新笔记
- 所有新笔记都关联到指定的商品
- 支持最多50个标题的批量创建

## 📋 详细开发计划

### 阶段1: 后端API开发

#### 1.1 API端点设计
- **端点**: `POST /api/v1/products/{product_id}/notes/batch`
- **功能**: 为指定商品批量创建笔记
- **权限**: 商品所有者或管理员

#### 1.2 请求/响应设计
```typescript
// 请求体
{
  "titles": ["标题1", "标题2", "标题3", ...]
}

// 响应体
{
  "success": true,
  "created_count": 3,
  "notes": [
    {
      "id": 1,
      "title": "标题1",
      "product_id": 123,
      "status": "draft"
    },
    ...
  ],
  "errors": []  // 如果有标题创建失败
}
```

#### 1.3 业务逻辑
- 验证商品存在且用户有权限
- 过滤空行和重复标题
- 限制最多50个标题
- 批量创建笔记记录
- 记录操作日志

### 阶段2: 前端界面开发

#### 2.1 批量创建对话框
- **触发**: 在商品详情页添加"批量创建笔记"按钮
- **界面**: 弹出对话框，包含多行文本输入框
- **功能**: 
  - 实时显示标题数量
  - 验证输入格式
  - 提交创建请求

#### 2.2 用户体验优化
- **输入提示**: 显示"每行一个标题，最多50个"
- **实时计数**: 显示当前输入的标题数量
- **去重提示**: 自动检测重复标题
- **创建反馈**: 显示创建进度和结果

### 阶段3: 功能集成和测试

#### 3.1 集成测试
- 测试批量创建API
- 测试前端界面交互
- 测试权限控制
- 测试错误处理

#### 3.2 用户体验测试
- 测试不同数量的标题创建
- 测试边界条件（空标题、重复标题等）
- 测试移动端适配

## 🔧 技术实现细节

### 后端实现

#### 数据库操作
```python
# 批量插入笔记
async def batch_create_notes(
    db: AsyncSession,
    product_id: int,
    titles: List[str],
    user_id: int
) -> List[Note]:
    # 过滤和去重
    unique_titles = list(dict.fromkeys([
        title.strip() for title in titles 
        if title.strip()
    ]))
    
    # 限制数量
    if len(unique_titles) > 50:
        raise HTTPException(400, "最多支持50个标题")
    
    # 批量创建
    notes = []
    for title in unique_titles:
        note = Note(
            title=title,
            product_id=product_id,
            status=NoteStatus.DRAFT
        )
        notes.append(note)
    
    db.add_all(notes)
    await db.commit()
    
    return notes
```

#### API端点实现
```python
@router.post("/{product_id}/notes/batch")
async def batch_create_notes(
    product_id: int,
    request: BatchCreateNotesRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    # 验证商品权限
    product = await get_product_with_permission(
        db, product_id, current_user
    )
    
    # 批量创建笔记
    notes = await batch_create_notes_service(
        db, product_id, request.titles, current_user.id
    )
    
    return {
        "success": True,
        "created_count": len(notes),
        "notes": notes
    }
```

### 前端实现

#### 批量创建组件
```vue
<template>
  <van-dialog
    v-model:show="showDialog"
    title="批量创建笔记"
    show-cancel-button
    @confirm="handleSubmit"
  >
    <div class="batch-create-form">
      <van-field
        v-model="titlesText"
        type="textarea"
        placeholder="请输入笔记标题，每行一个&#10;最多支持50个标题"
        rows="10"
        @input="updateTitleCount"
      />
      
      <div class="title-info">
        <span>标题数量: {{ titleCount }}/50</span>
        <span v-if="duplicateCount > 0" class="warning">
          检测到 {{ duplicateCount }} 个重复标题
        </span>
      </div>
    </div>
  </van-dialog>
</template>

<script setup>
const titlesText = ref('')
const titleCount = ref(0)
const duplicateCount = ref(0)

const updateTitleCount = () => {
  const titles = titlesText.value
    .split('\n')
    .map(t => t.trim())
    .filter(t => t)
  
  titleCount.value = titles.length
  duplicateCount.value = titles.length - new Set(titles).size
}

const handleSubmit = async () => {
  const titles = titlesText.value
    .split('\n')
    .map(t => t.trim())
    .filter(t => t)
  
  if (titles.length === 0) {
    showToast('请输入至少一个标题')
    return
  }
  
  try {
    const response = await api.post(
      `/api/v1/products/${productId}/notes/batch`,
      { titles }
    )
    
    showToast(`成功创建 ${response.data.created_count} 个笔记`)
    emit('created', response.data.notes)
    showDialog.value = false
  } catch (error) {
    showToast('创建失败')
  }
}
</script>
```

## 📊 开发时间估算

| 阶段 | 任务 | 预估时间 |
|------|------|----------|
| 1.1 | 后端API设计和实现 | 3小时 |
| 1.2 | 数据验证和错误处理 | 2小时 |
| 1.3 | 权限控制和安全检查 | 1小时 |
| 2.1 | 前端批量创建组件 | 4小时 |
| 2.2 | 界面集成和交互优化 | 2小时 |
| 3.1 | 功能测试和调试 | 2小时 |
| 3.2 | 用户体验测试 | 1小时 |
| **总计** | | **15小时** |

## 🎯 验收标准

### 功能验收
- [ ] 用户可以在商品详情页找到"批量创建笔记"按钮
- [ ] 点击按钮弹出批量创建对话框
- [ ] 可以输入多个标题，每行一个
- [ ] 实时显示标题数量和重复检测
- [ ] 成功创建多个笔记，都关联到指定商品
- [ ] 新创建的笔记状态为"待发布"

### 边界条件验证
- [ ] 空输入时给出提示
- [ ] 超过50个标题时给出限制提示
- [ ] 重复标题自动去重
- [ ] 权限验证正常工作

### 用户体验验证
- [ ] 界面响应式，移动端适配良好
- [ ] 操作流程简单直观
- [ ] 错误提示清晰友好
- [ ] 创建成功后有明确反馈

## 🚀 部署计划

1. **开发环境测试**: 完成所有功能开发和测试
2. **API文档更新**: 更新API文档，添加新端点说明
3. **用户指南**: 更新用户使用指南
4. **功能发布**: 部署到生产环境

---

**计划制定时间**: 2025-07-26  
**目标完成时间**: 2025-07-26  
**版本号**: v1.5.0
