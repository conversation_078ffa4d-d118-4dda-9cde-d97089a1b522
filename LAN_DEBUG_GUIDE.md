# 局域网登录问题调试指南

## 🔧 调试工具使用说明

### 1. 启动Debug模式服务
```bash
# 使用增强的debug启动脚本
start-lan-debug.bat
```

### 2. 运行网络诊断
```bash
# 运行网络诊断工具
network-debug.bat
```

### 3. 使用Web调试工具
在浏览器中访问：`http://你的IP地址:3000/debug.html`

## 📋 调试步骤

### 步骤1：基础网络检查
1. **运行网络诊断脚本**：
   ```bash
   network-debug.bat
   ```
   
2. **检查输出信息**：
   - IP地址检测是否正确
   - 端口3000和8000是否可用
   - 防火墙状态
   - 服务连通性测试

### 步骤2：启动Debug服务
1. **使用debug启动脚本**：
   ```bash
   start-lan-debug.bat
   ```
   
2. **观察启动日志**：
   - 配置更新是否成功
   - 服务启动是否正常
   - 网络连通性测试结果

### 步骤3：Web调试测试
1. **在服务器上测试**：
   - 访问：`http://localhost:3000/debug.html`
   - 运行所有连通性测试
   - 测试登录功能

2. **在局域网设备上测试**：
   - 访问：`http://你的IP地址:3000/debug.html`
   - 运行所有连通性测试
   - 测试登录功能

### 步骤4：分析日志
1. **后端日志**：
   - 查看后端服务窗口的详细日志
   - 检查`backend_debug.log`文件
   - 注意CORS配置和请求来源

2. **前端日志**：
   - 打开浏览器开发者工具
   - 查看Console标签页的错误信息
   - 查看Network标签页的请求状态

## 🔍 常见问题诊断

### 问题1：无法访问前端页面
**症状**：浏览器显示"无法访问此网站"

**检查项**：
- [ ] 防火墙是否阻止了3000端口
- [ ] 前端服务是否正确绑定到0.0.0.0
- [ ] 网络连接是否正常

**解决方案**：
```bash
# 添加防火墙规则（管理员权限）
netsh advfirewall firewall add rule name="XHS Frontend" dir=in action=allow protocol=TCP localport=3000
```

### 问题2：前端页面可访问，但API请求失败
**症状**：页面加载正常，但登录时显示"网络错误"

**检查项**：
- [ ] 后端服务是否正常运行
- [ ] 8000端口是否可访问
- [ ] CORS配置是否包含客户端IP
- [ ] 代理配置是否正确

**解决方案**：
```bash
# 添加防火墙规则（管理员权限）
netsh advfirewall firewall add rule name="XHS Backend" dir=in action=allow protocol=TCP localport=8000

# 检查CORS配置
type backend\.env | findstr ALLOWED_ORIGINS
```

### 问题3：CORS错误
**症状**：浏览器控制台显示CORS相关错误

**检查项**：
- [ ] 后端CORS配置是否包含客户端来源
- [ ] 请求头是否正确
- [ ] 预检请求是否成功

**解决方案**：
1. 检查`backend/.env`中的`ALLOWED_ORIGINS`配置
2. 确保包含客户端的完整URL（包括端口）

### 问题4：认证失败
**症状**：用户名密码正确但登录失败

**检查项**：
- [ ] 数据库是否正常
- [ ] 用户账户是否存在
- [ ] 密码是否正确

**解决方案**：
```bash
# 重置管理员密码
cd backend
python reset_admin_password.py reset admin admin123
```

## 📊 Debug信息收集

### 收集以下信息用于问题分析：

1. **网络诊断输出**：
   ```bash
   network-debug.bat > network-debug-output.txt
   ```

2. **服务启动日志**：
   - 后端服务窗口的完整输出
   - `backend_debug.log`文件内容

3. **浏览器信息**：
   - 开发者工具Console的错误信息
   - Network标签页的请求详情
   - Web调试工具的测试结果

4. **系统信息**：
   - 操作系统版本
   - 防火墙设置
   - 网络配置

## 🛠️ 高级调试

### 使用Wireshark抓包
如果问题仍然存在，可以使用Wireshark抓包分析：

1. 安装Wireshark
2. 捕获网络接口流量
3. 过滤条件：`tcp.port == 3000 or tcp.port == 8000`
4. 分析TCP连接和HTTP请求

### 临时禁用防火墙测试
**警告**：仅在安全的网络环境中进行

```bash
# 临时禁用Windows防火墙（管理员权限）
netsh advfirewall set allprofiles state off

# 测试完成后重新启用
netsh advfirewall set allprofiles state on
```

## 📞 获取帮助

如果问题仍然存在，请提供以下信息：

1. 网络诊断工具的完整输出
2. 服务启动时的详细日志
3. 浏览器开发者工具的错误信息
4. Web调试工具的测试结果
5. 网络环境描述（路由器型号、网络拓扑等）

## 🎯 快速解决方案

### 最常见的解决方案：

1. **添加防火墙例外**：
   ```bash
   netsh advfirewall firewall add rule name="XHS Frontend" dir=in action=allow protocol=TCP localport=3000
   netsh advfirewall firewall add rule name="XHS Backend" dir=in action=allow protocol=TCP localport=8000
   ```

2. **检查IP地址配置**：
   确保`start-lan-debug.bat`检测到的IP地址正确

3. **重启服务**：
   关闭所有服务窗口，重新运行`start-lan-debug.bat`

4. **清除浏览器缓存**：
   清除浏览器缓存和Cookie，重新访问
