# 小红书笔记管理系统 - 产品需求文档 (PRD)

| 文档版本 | V2.5 |
| :--- | :--- |
| **创建日期** | 2025年1月22日 |
| **最后更新** | 2025年7月27日 |
| **产品负责人** | [你的名字] |

## 1. 产品概述

### 1.1 产品目标
本系统是一个为小红书内容创作者设计的后台内容管理系统（CMS）。旨在提供一个高效、集中的平台，用于管理商品素材、创作和规划笔记内容、并通过AI辅助提升创作效率。

### 1.2 目标用户
- **管理员**：负责整个系统的维护、用户管理和内容监督
- **普通用户**：小红书内容创作者，使用本系统管理个人素材和创作笔记

### 1.3 核心价值
- **素材集中化**：将分散的商品图文、笔记素材统一管理
- **流程标准化**：从商品到笔记，建立标准化的内容生产流程
- **效率提升**：通过快捷操作（一键复制/下载）和AI辅助功能，缩短内容创作时间

## 2. 功能需求

### 2.1 系统与用户权限
- **多用户系统**：支持多用户登录，隔离不同用户的数据
- **用户角色与权限**：
  - **管理员**：
    - 拥有所有普通用户的权限
    - 可增删改查所有用户、所有商品、所有笔记
    - 负责创建新的**普通用户**账号（系统不设开放注册）
    - 可访问回收站功能
  - **普通用户**：
    - 只能增删改查**自己名下**的商品和笔记
    - 对自己名下的数据拥有完全控制权

### 2.2 商品管理
此模块用于管理作为笔记创作基础的商品素材。

- **核心操作**：支持管理员和普通用户对其权限内的商品进行增、删、改、查
- **数据归属**：每个商品只能属于一个普通用户
- **用户关联**：
  - 创建商品时，管理员可以选择商品归属的用户
  - 修改商品时，管理员可以更改商品的归属用户
  - 查看商品时，显示商品的归属用户信息
  - 普通用户只能管理自己名下的商品
- **商品字段**：
  - `商品简称`：内部识别用，文本输入框
  - `商品标题`：笔记中可能引用的正式标题，文本输入框
  - `商品主图`：本地上传，最多10张（无需排序功能）
  - `商品详情图`：本地上传，最多30张（无需排序功能）
  - `商品介绍文字`：富文本或多行文本区域
  - `商品定价`：文本/数字输入框
  - `商品特点分析`：多行文本区域（此内容由用户在系统外通过AI生成后，粘贴入系统）

### 2.3 笔记管理（核心模块）
此模块是系统的核心，实现了从笔记创建到管理的完整闭环。

- **访问流程**：在"商品管理"页面，每个商品下方展示其关联的"笔记列表"。点击"新建笔记"或编辑现有笔记，进入统一的笔记管理页
- **数据关系**：
  - 每个商品可以关联N个笔记
  - 每个笔记只能属于一个商品
- **笔记字段/功能**：
  - `笔记标题`：文本输入框。创建笔记时，标题为必填项
    - **交互**：标题旁提供【复制】按钮，一键复制标题文本
  - `封面图片`：用户可单独上传最多5张图片作为封面候选，并从中**指定1张**为当前笔记的最终封面
  - `笔记图片`：本地上传，最多30张
    - **交互**：每张图片旁提供【下载】按钮
  - `笔记视频`：本地上传，最多5个，格式限定为MP4
    - **交互**：每个视频旁提供【下载】按钮
  - `笔记正文`：富文本或多行文本区域
    - **交互**：正文区域旁提供【复制】按钮，一键复制全部正文
  - `笔记标签`：文本输入框，多个标签建议用逗号或空格分隔
    - **交互**：标签输入框旁提供【复制】按钮，一键复制所有标签
  - `计划发布时间`：日期时间选择器，仅作为用户备忘录
  - `关联的商品`：只读字段，自动关联

#### 2.3.1 笔记状态管理
- **状态**：笔记包含两种状态：`待发布`、`已发布`
- **默认状态**：笔记创建后，默认为`待发布`状态
- **状态切换**：提供【标记为已发布】/【取消发布】按钮，让用户一键切换状态

#### 2.3.2 软删除与回收站
- **软删除机制**：删除笔记时不会立即从数据库中移除，而是标记`deleted_at`字段
- **回收站功能**（仅限管理员）：
  - 查看所有被软删除的笔记
  - 恢复被删除的笔记
  - 彻底删除笔记（物理删除）

#### 2.3.3 批量创建笔记
为提高创作效率，系统支持为单个商品批量创建笔记：
- **入口**：在商品详情页面和菜单栏，提供【批量创建笔记】按钮
- **操作流程**：
  - 点击按钮后，弹出批量创建对话框
  - 用户可在多行文本框中输入多个笔记标题，每行一个标题
  - 系统自动为每个标题创建一个新笔记，关联到当前商品
  - 新创建的笔记默认状态为`待发布`
- **界面特性**：
  - 大尺寸可拖拽调整的对话框
  - 显示行号，方便查看笔记数量
  - 支持无限制数量的笔记创建
  - 空行自动忽略，重复标题自动去重

#### 2.3.4 操作日志
在笔记管理页面的下方，自动记录并显示状态变更历史：
- **日志格式**：`[操作人]` 于 `[YYYY-MM-DD HH:mm:ss]` 将笔记状态从 `[原状态]` 修改为 `[新状态]`
- 另外也记录笔记的创建时间

### 2.4 AI提示词（Prompt）管理
- **核心操作**：创建、编辑、删除、查询和一键复制提示词
- **权限设计**：
  - **系统提示词**：
    - **管理者**：仅限**管理员**可增删改查
    - **使用者**：**所有用户**可见，并可点击【复制】按钮使用，但不能修改或删除
  - **个人提示词**：
    - **管理者**：**普通用户**可对自己的提示词进行增删改查
    - **可见性**：个人提示词仅创建者自己可见
- **提示词字段**：
  - `提示词标题`：用于快速识别和搜索
  - `提示词内容`：多行文本区域，支持包含变量占位符（如 `{商品标题}`, `{商品特点分析}`），以便未来系统进行自动填充

### 2.5 菜单栏快捷操作
为提高用户操作效率，在左侧菜单栏添加常用功能的快捷入口：
- **创建商品**：直接跳转到商品创建页面
- **创建笔记**：跳转到笔记创建页面（需要先选择商品）
- **批量创建笔记**：跳转到批量创建笔记页面（需要先选择商品）
- **笔记下载**：跳转到笔记下载管理页面
- **商品下载**：跳转到商品下载管理页面
- **权限控制**：根据用户角色显示相应的快捷操作

### 2.6 下载功能模块

#### 2.6.1 笔记下载功能
- **功能概述**：为用户提供便捷的笔记内容获取和管理功能
- **核心特性**：
  - **笔记预览**：以卡片形式展示笔记列表，包含标题、内容摘要、封面图片
  - **商品关联显示**：每个笔记显示其关联的商品信息，支持点击跳转到商品详情
  - **使用状态管理**：支持标记笔记为"已使用"或"未使用"状态，便于内容复用管理
  - **统计信息**：显示总笔记数、未使用数、已使用数和使用率
  - **搜索筛选**：支持按关键词搜索和按使用状态筛选
  - **快捷操作**：
    - 一键复制笔记标题
    - 一键复制笔记完整内容
    - 批量下载笔记图片
    - 图片预览功能
  - **详情页面**：
    - 显示笔记完整信息（标题、内容、标签、图片、视频等）
    - 关联商品信息展示和跳转
    - 每个图片/视频独立下载按钮
    - 使用状态切换功能

#### 2.6.2 商品下载功能
- **功能概述**：为用户提供便捷的商品素材获取和管理功能
- **核心特性**：
  - **商品列表**：展示用户权限范围内的所有商品
  - **权限控制**：
    - **普通用户**：只能查看和下载自己的商品
    - **管理员**：可以查看和下载所有用户的商品
  - **商品信息展示**：
    - 商品标题和详细描述
    - 商品主图和详情图片预览
    - 商品基本信息（价格、特点分析等）
  - **快捷操作**：
    - 一键复制商品标题
    - 一键复制商品描述
    - 一键复制商品特点分析
    - 一键下载所有商品图片
  - **详情页面**：
    - 显示商品完整信息
    - 图片网格展示，每个图片独立下载按钮
    - 支持图片预览功能
    - 批量下载功能

## 3. 非功能性需求

### 3.1 UI/UX 设计
- **设计原则**：**移动端优先**。界面设计首先保证在手机浏览器上的简洁和易用性，同时兼容PC浏览器的操作体验
- **响应式布局**：页面元素应能根据屏幕宽度自适应调整

### 3.2 数据存储
- **文件存储**：所有用户上传的图片和视频，均存储在**服务器本地文件系统**中
- **注意**：此方案简化了初期部署，但需关注服务器硬盘容量、备份策略和未来扩展性问题

## 4. 技术架构

### 4.1 系统架构图

```
+--------------+   +------------------------+   +----------------+
|              |   |                        |   |                |
| 用户浏览器   |-->|      Nginx (生产)      |-->|   FastAPI 应用  |
| (PC/Mobile)  |   | (反向代理/静态文件服务) |   |   (Uvicorn)    |
|              |   |                        |   |                |
+--------------+   +------------------------+   +-------+--------+
                                                        |
                           +----------------------------+----------------------------+
                           |                            |                            |
                   +-------v---------+          +-------v---------+          +-------v---------+
                   |                 |          |                 |          |                 |
                   |   MySQL/SQLite  |          |  服务器本地文件存储  |          | Celery/ARQ (V2) |
                   |    (数据库)      |          |  (图片/视频)     |          |   (异步任务)    |
                   |                 |          |                 |          |                 |
                   +-----------------+          +-----------------+          +-----------------+
```

### 4.2 后端技术栈
- **语言/框架**：Python 3.9+ / FastAPI
- **数据校验**：Pydantic
- **数据库ORM**：SQLAlchemy 2.0 (异步 AsyncSession)
- **异步任务队列 (V2)**：Celery 或 ARQ
- **Web服务器**：Uvicorn (开发) / Gunicorn (生产)
- **反向代理 (生产)**：Nginx

### 4.3 前端技术栈
- **核心框架**：Vue 3 (组合式API)
- **构建工具**：Vite
- **UI组件库**：Vant 4 (移动端优先)
- **状态管理**：Pinia
- **路由管理**：Vue Router 4
- **HTTP客户端**：Axios

## 5. 数据库 Schema 设计

```python
# models.py (SQLAlchemy Models)
import enum
from sqlalchemy import (Column, Integer, String, Text, ForeignKey, DateTime, 
                        Boolean, Enum, func)
from sqlalchemy.orm import relationship
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()

# --- Enums for choices ---
class UserRole(str, enum.Enum):
    ADMIN = "admin"
    USER = "user"

class NoteStatus(str, enum.Enum):
    TO_BE_PUBLISHED = "待发布"
    PUBLISHED = "已发布"

class PromptType(str, enum.Enum):
    SYSTEM = "system"
    PERSONAL = "personal"

# --- Tables ---
class User(Base):
    __tablename__ = "users"
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    role = Column(Enum(UserRole), default=UserRole.USER, nullable=False)
    products = relationship("Product", back_populates="owner", cascade="all, delete-orphan")
    prompts = relationship("Prompt", back_populates="owner", cascade="all, delete-orphan")

class Product(Base):
    __tablename__ = "products"
    id = Column(Integer, primary_key=True, index=True)
    short_name = Column(String(100), nullable=False, index=True)
    title = Column(String(255), index=True)
    main_images = Column(Text)
    detail_images = Column(Text)
    description = Column(Text)
    price = Column(String(50))
    feature_analysis = Column(Text)
    created_at = Column(DateTime, server_default=func.now(), index=True)
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    owner_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    owner = relationship("User", back_populates="products")
    notes = relationship("Note", back_populates="product", cascade="all, delete-orphan")

class Note(Base):
    __tablename__ = "notes"
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(255), nullable=False, index=True)
    cover_images_candidate = Column(Text)
    selected_cover_image = Column(String(255))
    note_images = Column(Text)
    videos = Column(Text)
    body = Column(Text)
    tags = Column(String(255))
    scheduled_at = Column(DateTime, nullable=True, index=True)
    status = Column(Enum(NoteStatus), default=NoteStatus.TO_BE_PUBLISHED)
    created_at = Column(DateTime, server_default=func.now(), index=True)
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    # --- 新增回收站相关字段 ---
    deleted_at = Column(DateTime, nullable=True, index=True)  # 软删除标记
    
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False)
    product = relationship("Product", back_populates="notes")
    logs = relationship("NoteLog", back_populates="note", cascade="all, delete-orphan")

class NoteLog(Base):
    __tablename__ = "note_logs"
    id = Column(Integer, primary_key=True, index=True)
    log_message = Column(String(500), nullable=False)
    created_at = Column(DateTime, server_default=func.now())
    note_id = Column(Integer, ForeignKey("notes.id"), nullable=False)
    note = relationship("Note", back_populates="logs")

class Prompt(Base):
    __tablename__ = "prompts"
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(255), nullable=False)
    content = Column(Text, nullable=False)
    prompt_type = Column(Enum(PromptType), nullable=False)
    owner_id = Column(Integer, ForeignKey("users.id"), nullable=True)  # 系统提示词该字段为NULL
    owner = relationship("User", back_populates="prompts")
```

**重要**：所有常规的笔记查询 (GET) 请求，都必须增加 `WHERE deleted_at IS NULL` 的过滤条件。

## 6. API 接口设计 (RESTful)

### 6.1 认证
- `POST /api/v1/auth/token`

### 6.2 商品
- `GET /api/v1/products/`：获取商品列表
  - Query Params: `page: int = 1`, `size: int = 50`, `q: str = None`, `sort_by: str = 'created_at'`, `order: str = 'desc'`
- `POST /api/v1/products/`：创建商品
- `GET, PUT, DELETE /api/v1/products/{id}`：操作单个商品

### 6.3 笔记
- `GET /api/v1/products/{product_id}/notes/`：获取指定商品的笔记列表（`deleted_at IS NULL`）
  - Query Params: `page: int = 1`, `size: int = 50`, `q: str = None`, `sort_by: str = 'created_at'`, `order: str = 'desc'`
- `POST /api/v1/products/{product_id}/notes/`：创建笔记
- `GET, PUT /api/v1/notes/{id}`：操作单个笔记
- `DELETE /api/v1/notes/{id}`：软删除笔记 (更新`deleted_at`字段)
- `PATCH /api/v1/notes/{id}/status`：更新笔记状态

### 6.4 回收站（新增，仅限管理员）
- `GET /api/v1/recycle-bin/notes/`：获取回收站中的笔记列表 (`deleted_at IS NOT NULL`)
  - Query Params: 同笔记列表
- `POST /api/v1/recycle-bin/notes/{id}/restore`：恢复笔记 (设置`deleted_at = NULL`)
- `DELETE /api/v1/recycle-bin/notes/{id}`：彻底删除笔记 (物理删除)

### 6.5 提示词
- `GET, POST /api/v1/prompts/` & `GET, PUT, DELETE /api/v1/prompts/{id}`

### 6.6 文件上传
- `POST /api/v1/upload/image`
- `POST /api/v1/upload/video`

## 7. 环境与部署

### 7.1 开发环境 (Windows 11)
- **后端**：使用 `uvicorn main:app --reload` 命令直接启动FastAPI应用进行开发调试
- **前端**：使用 `npm run dev` 启动Vite开发服务器
- **数据库**：使用SQLite文件数据库，方便快捷
- **说明**：此环境不需要Nginx。前后端通过不同的本地端口进行通信

### 7.2 生产环境 (Ubuntu)
- **数据库**：切换为MySQL，保证性能和稳定性
- **后端部署**：使用Gunicorn和Uvicorn Worker (`gunicorn -w 4 -k uvicorn.workers.UvicornWorker main:app`) 运行应用
- **前端部署**：运行 `npm run build` 打包生成静态文件，并上传至服务器
- **Nginx配置**：
  - **作为反向代理**：将API请求（如 `/api/v1/*`）转发给Gunicorn
  - **作为静态文件服务器**：直接处理前端静态文件的请求，并配置`try_files`以支持SPA的History路由模式

## 8. 后续扩展 (Roadmap V2)

### 8.1 自动生成笔记内容
- **触发方式**：在笔记页面提供"AI生成"按钮
- **实现逻辑**：后台通过**异步任务队列**（如Celery/ARQ）调用大模型API
- **输入**：`AI提示词` + `商品信息` + `人工录入的爆款标题`
- **输出**：生成的笔记文字内容，自动填充到笔记正文区域

### 8.2 自动生成封面图
- **触发方式**：可在笔记页面提供"AI生成封面"按钮
- **实现逻辑**：调用外部AI绘画API服务
- **输入**：`人工录入的爆款标题`
- **输出**：返回N张图片，供用户选择作为封面

### 8.3 自动生成视频
- **触发方式**：在笔记管理页面，允许用户选择当前笔记中的多张图片，并点击"AI生成视频"按钮
- **实现逻辑**：后台通过异步任务队列调用外部AI视频生成API服务
- **输入**：用户选择的图片序列
- **输出**：生成的视频文件，自动保存并关联到当前笔记中

## 9. v1.6.0 新增功能

### 9.1 商品用户关联管理
- **功能描述**：每个商品只属于一个普通用户，管理员可以指定商品归属
- **权限控制**：
  - 管理员：可以为任何商品指定归属用户，可以查看所有商品
  - 普通用户：只能查看和管理自己名下的商品
- **界面展示**：商品列表和详情页显示归属用户信息
- **操作流程**：
  1. 管理员创建商品时选择归属用户
  2. 管理员可以修改现有商品的归属用户
  3. 系统自动验证用户存在性

### 9.2 菜单栏快捷操作
- **功能描述**：在左侧导航菜单新增快捷操作区域
- **快捷按钮**：
  - 创建商品：直接跳转到商品创建页面
  - 创建笔记：跳转到笔记创建页面（需先选择商品）
  - 批量创建笔记：跳转到批量创建页面（需先选择商品）
- **视觉设计**：快捷操作按钮采用特殊蓝色样式，易于识别
- **响应式适配**：完美适配桌面端和移动端

### 9.3 批量创建界面优化
- **大尺寸窗口**：默认800x600像素，最大可达屏幕90%
- **拖拽调整**：支持鼠标拖拽右下角调整对话框大小
- **行号显示**：左侧显示行号，方便查看笔记数量
- **无数量限制**：完全移除50个标题的限制，支持无限制创建
- **代码编辑器体验**：类似代码编辑器的界面设计，提升用户体验

### 9.4 技术架构优化
- **后端API增强**：
  - 新增用户列表API：`GET /api/v1/products/users/list`
  - 商品API返回用户信息：`ProductWithOwner`模型
  - 优化关联查询性能：使用JOIN查询
- **前端组件优化**：
  - 用户选择器组件：支持搜索和筛选
  - 独立页面：专门的笔记创建和批量创建页面
  - 拖拽交互：原生JavaScript实现窗口调整
- **数据模型优化**：
  - 商品用户关联的数据完整性保证
  - 向下兼容现有数据结构

## 10. v2.5.0 新增功能

### 10.1 商品管理卡片布局优化
- **功能描述**：重新设计商品管理页面，采用现代化卡片布局
- **核心特性**：
  - **横向卡片设计**：缩略图 + 内容区域 + 操作按钮的布局
  - **完整商品信息展示**：
    - 商品缩略图（100x100px）
    - 标题、价格、笔记数量
    - 归属用户、创建时间
    - 图片预览（显示前4张，超出显示"+N"）
  - **操作功能**：编辑、详情、笔记、删除四个操作按钮
  - **响应式设计**：
    - **PC端**：横向布局，操作按钮垂直排列
    - **平板端**：保持横向布局，适当调整间距
    - **手机端**：改为纵向布局，操作按钮水平排列
  - **交互效果**：悬停动画、图片预览、确认删除对话框

### 10.2 笔记管理功能
- **功能描述**：全新的笔记管理页面，提供完整的笔记管理功能
- **核心特性**：
  - **搜索和筛选**：
    - 搜索框：支持标题和内容搜索
    - 使用状态筛选：全部/未使用/已使用
    - 关联商品筛选：全部商品/特定商品
  - **统计信息**：
    - 总笔记数、未使用数、已使用数、使用率
    - 实时更新，支持筛选条件
  - **笔记卡片**：
    - 笔记缩略图、标题、内容预览
    - 使用状态标签、关联商品标签
    - 创建时间、标签信息
    - 图片预览（显示前4张）
    - 视频数量显示
  - **操作功能**：
    - 编辑、详情、状态切换、删除
    - 图片预览功能
    - 确认删除对话框
  - **响应式设计**：与商品管理保持一致的设计风格

### 10.3 导航菜单集成
- **功能描述**：在侧边栏菜单中添加笔记管理入口
- **位置**：商品管理和AI提示词之间
- **图标**：notes-o，与功能匹配
- **权限控制**：所有用户可访问，但只能管理自己的笔记

### 10.4 后端API扩展
- **新增API**：
  - `GET /api/v1/notes/`：获取笔记列表，支持搜索、筛选、分页
  - `GET /api/v1/notes/stats`：获取笔记统计信息，支持筛选条件
- **功能特性**：
  - 支持搜索、状态筛选、商品筛选
  - 返回总数、未使用数、已使用数、使用率
  - 用户权限控制，通过Product关联检查权限

### 10.5 设计系统统一
- **卡片设计**：
  - 统一样式：圆角12px、阴影效果、白色背景
  - 悬停效果：上移2px、阴影加深
  - 内容布局：缩略图 + 内容区域 + 操作按钮
- **颜色方案**：
  - 价格标签：绿色背景 (#e8f8e8)
  - 状态标签：蓝色(未使用)、红色(已使用)
  - 商品标签：绿色背景
- **响应式布局**：
  - 网格系统：CSS Grid `repeat(auto-fit, minmax(400px, 1fr))`
  - 断点设计：手机端单列、平板端2-3列、PC端多列

### 10.6 用户体验优化
- **交互反馈**：
  - 加载状态：下拉刷新、无限滚动
  - 空状态：友好的空数据提示和引导
  - 错误处理：API调用失败的错误提示
  - 操作确认：删除操作的确认对话框
- **性能优化**：
  - 图片懒加载：van-image组件自带懒加载
  - 分页加载：支持无限滚动分页
  - 数据过滤：安全的数据访问，防止null/undefined错误

### 10.7 Bug修复
- **菜单高亮问题**：修复商品下载页面时商品管理菜单错误高亮的问题
- **详情跳转问题**：修复商品卡片详情按钮跳转路径错误的问题
- **特点分析显示**：移除商品卡片中的特点分析显示
- **API调用问题**：修复笔记管理页面API调用404错误，完善后端API实现
