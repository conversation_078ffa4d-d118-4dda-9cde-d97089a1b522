# 🚀 一键启动指南

本项目提供了跨平台的一键启动脚本，让您无需手动配置即可快速运行小红书笔记管理系统。

## 📋 系统要求

### 必需软件
- **Python 3.11+** - 后端运行环境
- **Node.js 18+** - 前端运行环境
- **Git** - 代码管理（可选）

### 支持的操作系统
- ✅ Windows 10/11
- ✅ macOS 10.15+
- ✅ Ubuntu 18.04+
- ✅ CentOS 7+
- ✅ 其他Linux发行版

## 🎯 一键启动

### Windows 用户

#### 方法1：双击运行（推荐）
```
双击 start.bat 文件
```

#### 方法2：命令提示符(CMD)
```cmd
start.bat
```

#### 方法3：PowerShell（推荐）
```powershell
# 运行PowerShell版本（更好的显示效果）
.\start.ps1

# 或者运行批处理版本
.\start.bat
```

**注意**：如果PowerShell提示执行策略限制，请运行：
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### Linux/Mac 用户

1. **在终端中运行**
   ```bash
   chmod +x start.sh
   ./start.sh
   ```

2. **或者直接运行**
   ```bash
   bash start.sh
   ```

## 🛑 停止服务

### Windows 用户
```cmd
# 命令提示符
stop.bat

# PowerShell
.\stop.ps1
```

### Linux/Mac 用户
```bash
./stop.sh
```

## 🔧 脚本功能

### 自动环境检查
- ✅ 检查Python和Node.js是否已安装
- ✅ 验证版本兼容性
- ✅ 提供安装指导（如果缺少依赖）

### 自动初始化
- 🔄 首次运行自动安装Python依赖
- 🔄 首次运行自动安装Node.js依赖
- 🔄 自动初始化数据库和默认用户
- 🔄 创建必要的目录结构

### 智能启动
- 🚀 自动启动后端服务（端口8000）
- 🚀 自动启动前端服务（端口3000）
- 🌐 自动打开浏览器访问应用
- 📊 显示服务状态和访问地址

### 日志管理
- 📝 Linux/Mac版本自动创建日志文件
- 📝 后端日志：`logs/backend.log`
- 📝 前端日志：`logs/frontend.log`
- 📝 进程ID保存：`logs/backend.pid`、`logs/frontend.pid`

## 🌐 访问地址

启动成功后，您可以通过以下地址访问：

| 服务 | 地址 | 说明 |
|------|------|------|
| 前端应用 | http://localhost:3000 | 主要用户界面 |
| 后端API | http://localhost:8000 | API服务 |
| API文档 | http://localhost:8000/docs | Swagger文档 |

## 👤 默认账户

| 角色 | 用户名 | 密码 |
|------|--------|------|
| 管理员 | admin | admin123 |

## 🐛 常见问题

### 1. 端口被占用
**问题**：启动时提示端口8000或3000被占用

**解决方案**：
```bash
# 查看端口占用（Windows）
netstat -ano | findstr :8000
netstat -ano | findstr :3000

# 查看端口占用（Linux/Mac）
lsof -i :8000
lsof -i :3000

# 停止占用进程或修改配置文件中的端口
```

### 2. Python依赖安装失败
**问题**：pip install失败

**解决方案**：
```bash
# 升级pip
python -m pip install --upgrade pip

# 使用国内镜像源
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

### 3. Node.js依赖安装失败
**问题**：npm install失败

**解决方案**：
```bash
# 清理缓存
npm cache clean --force

# 使用国内镜像源
npm install --registry https://registry.npmmirror.com

# 或使用yarn
yarn install
```

### 4. 权限问题（Linux/Mac）
**问题**：脚本没有执行权限

**解决方案**：
```bash
chmod +x start.sh stop.sh
```

### 5. 数据库初始化失败
**问题**：数据库文件创建失败

**解决方案**：
```bash
# 手动初始化
cd backend
python init_db.py
```

## 📁 目录结构

启动后的项目目录结构：
```
xhs_notes_manager/
├── start.bat              # Windows启动脚本
├── start.sh               # Linux/Mac启动脚本
├── stop.bat               # Windows停止脚本
├── stop.sh                # Linux/Mac停止脚本
├── logs/                  # 日志目录（Linux/Mac）
│   ├── backend.log        # 后端日志
│   ├── frontend.log       # 前端日志
│   ├── backend.pid        # 后端进程ID
│   └── frontend.pid       # 前端进程ID
├── backend/
│   ├── xhs_notes.db       # SQLite数据库文件
│   └── uploads/           # 文件上传目录
└── frontend/
    └── node_modules/      # Node.js依赖
```

## 🔄 手动启动（备用方案）

如果一键启动脚本遇到问题，您也可以手动启动：

### 1. 启动后端
```bash
cd backend
pip install -r requirements.txt
python init_db.py  # 仅首次运行
python run.py
```

### 2. 启动前端
```bash
cd frontend
npm install
npm run dev
```

## 📞 技术支持

如果遇到问题，请：
1. 检查系统要求是否满足
2. 查看错误日志信息
3. 参考常见问题解决方案
4. 提交Issue到项目仓库

---

**享受您的小红书笔记管理之旅！** 🎉
