# 小红书笔记管理系统

一个为小红书内容创作者设计的后台内容管理系统（CMS），提供高效、集中的平台用于管理商品素材、创作和规划笔记内容，并通过AI辅助提升创作效率。

## 🎯 项目状态

**当前版本**: v2.8.0
**开发状态**: 商品管理页面增强完成，支持高级筛选和PC友好交互，可用于生产环境
**最后更新**: 2025-01-03

## ✨ 功能特性

### 核心功能

- **🔐 用户认证系统**：JWT Token认证，支持管理员和普通用户角色
- **📦 商品管理**：现代化卡片布局，完整的商品CRUD操作，支持用户关联、图片上传、搜索、分页、排序
  - **🆕 笔记统计显示**：实时显示每个商品的总计/已用/待用笔记数量
  - **🆕 高级筛选功能**：按商品所有人和待使用笔记数量范围筛选
  - **🆕 PC友好交互**：横向布局的数值范围输入，简洁的下拉选择器
- **📝 笔记管理**：全新的笔记管理页面，支持编号显示、内容状态指示、搜索筛选、状态管理、软删除、操作日志
  - **🆕 内容状态指示器**：显示笔记是否包含内容、封面图、笔记图片、视频
- **🤖 AI提示词管理**：系统和个人提示词库，支持变量占位符，一键复制
- **🗂️ 回收站功能**：软删除机制，支持恢复和彻底删除
- **📱 响应式设计**：移动端优先，PC端布局优化，完美适配各种屏幕尺寸
- **🎨 现代化UI**：统一的卡片设计系统，悬停效果，图片预览功能
- **📹 大文件支持**：视频上传限制提升至500MB

### 技术特色

- **现代化架构**：前后端分离，RESTful API设计
- **高性能**：异步数据库操作，优化的查询性能
- **安全可靠**：密码加密存储，JWT Token认证，权限控制
- **开发友好**：完整的API文档，类型安全，热重载开发
- **响应式UI**：移动端优先设计，PC端布局优化，适配各种设备

## 🛠️ 技术栈

### 后端技术
- **Python 3.11** + **FastAPI** - 现代化异步Web框架
- **SQLAlchemy 2.0** - 异步ORM，支持多种数据库
- **Pydantic** - 数据验证和序列化
- **JWT** - 安全的用户认证
- **Uvicorn** - 高性能ASGI服务器
- **SQLite/MySQL** - 灵活的数据库支持

### 前端技术
- **Vue 3** - 渐进式JavaScript框架（Composition API）
- **TypeScript** - 类型安全的JavaScript超集
- **Vite** - 极速的前端构建工具
- **Vant 4** - 移动端UI组件库
- **Pinia** - 现代化状态管理
- **Vue Router 4** - 官方路由管理器
- **Axios** - HTTP客户端库

## 📁 项目结构

```text
xhs_notes_manager/
├── start.bat               # Windows一键启动脚本(CMD)
├── start.ps1               # Windows一键启动脚本(PowerShell)
├── start.sh                # Linux/Mac一键启动脚本
├── stop.bat                # Windows停止服务脚本(CMD)
├── stop.ps1                # Windows停止服务脚本(PowerShell)
├── stop.sh                 # Linux/Mac停止服务脚本
├── test_startup.py         # 启动测试脚本
├── test-start.bat          # 诊断测试脚本
├── simple-start.bat        # 简单测试脚本
├── QUICK_START.md          # 详细启动指南
├── backend/                # 后端代码
│   ├── app/
│   │   ├── api/v1/         # API路由（v1版本）
│   │   │   └── endpoints/  # 具体端点实现
│   │   ├── core/           # 核心配置（数据库、安全、依赖）
│   │   ├── models/         # SQLAlchemy数据模型
│   │   └── schemas/        # Pydantic数据模式
│   ├── uploads/            # 文件上传目录
│   ├── init_db.py          # 数据库初始化脚本
│   ├── run.py              # 开发服务器启动脚本
│   └── requirements.txt    # Python依赖
├── frontend/               # 前端代码
│   ├── src/
│   │   ├── views/          # 页面组件
│   │   ├── stores/         # Pinia状态管理
│   │   ├── utils/          # 工具函数（API封装等）
│   │   └── router/         # Vue Router配置
│   ├── package.json        # Node.js依赖
│   └── vite.config.ts      # Vite配置
├── logs/                   # 日志目录（Linux/Mac自动创建）
├── docs/                   # 文档目录
│   └── bug-fix-experience.md  # Bug修复经验文档
├── test_integration.py     # 前后端集成测试
└── README.md               # 项目文档
```

## 🚀 快速开始

### 环境要求
- **Python 3.11+**
- **Node.js 18+**
- **Git**

### 🎯 一键启动（推荐）

#### Windows 用户
```cmd
# 方法1: 双击运行 start.bat 文件

# 方法2: 命令提示符(CMD)
start.bat

# 方法3: PowerShell (推荐，更好的显示效果)
.\start.ps1
```

#### Linux/Mac 用户
```bash
# 给脚本添加执行权限并运行
chmod +x start.sh
./start.sh
```

#### 停止服务
```bash
# Windows CMD
stop.bat

# Windows PowerShell
.\stop.ps1

# Linux/Mac
./stop.sh
```

### 📖 详细启动指南
查看 [QUICK_START.md](QUICK_START.md) 获取完整的一键启动指南和故障排除。

### 🔧 手动启动（备用方案）

#### 1. 克隆项目
```bash
git clone https://gitee.com/bin1874/xhs_notes_manager.git
cd xhs_notes_manager
```

#### 2. 后端设置
```bash
cd backend
pip install -r requirements.txt
python init_db.py  # 初始化数据库和默认用户
python run.py      # 启动后端服务器 (http://localhost:8000)
```

#### 3. 前端设置
```bash
cd frontend
npm install
npm run dev        # 启动前端开发服务器 (http://localhost:3000)
```

#### 4. 访问应用
- **前端应用**: http://localhost:3000
- **后端API文档**: http://localhost:8000/docs
- **默认管理员账户**: admin / admin123

### 🔐 用户账户管理

系统提供完整的用户管理功能，支持管理员和普通用户的创建、修改、删除等操作。

#### 查看所有用户
```bash
cd backend
python reset_admin_password.py list
```

#### 重置admin密码
```bash
cd backend
python reset_admin_password.py reset <新密码>

# 示例
python reset_admin_password.py reset mynewpassword
```

#### 创建新管理员
```bash
cd backend
python reset_admin_password.py create <用户名> <密码>

# 示例
python reset_admin_password.py create superadmin mypassword
```

#### 设置用户密码 ⭐
```bash
cd backend
python reset_admin_password.py setpass <用户名> <新密码>

# 示例
python reset_admin_password.py setpass john newpassword123
```

#### 删除用户账户 ⭐
```bash
cd backend
python reset_admin_password.py delete <用户名>

# 示例（会要求确认）
python reset_admin_password.py delete olduser
```

#### 用户角色管理
```bash
# 提升用户为管理员
python reset_admin_password.py promote <用户名>

# 降级管理员为普通用户
python reset_admin_password.py demote <用户名>
```

#### 安全特性
- 🛡️ 防止删除最后一个管理员账户
- 🛡️ 删除操作需要确认
- 🛡️ 详细的操作日志和错误提示

**重要提醒**：执行用户管理操作前请先停止后端服务：
```bash
sudo systemctl stop xhs-backend
# 执行用户管理操作...
sudo systemctl start xhs-backend
```

详细使用指南请参考：[用户管理指南](docs/user-management-guide.md)

## 📸 功能截图

### 登录页面
- 现代化渐变背景设计
- 全屏背景装饰，PC端无空白区域
- 移动端优先的响应式布局
- 友好的用户提示信息

### 主要功能页面
- **首页**: 数据统计、快捷操作、最近活动
  - PC端：统计和活动区域并排显示，快捷操作4列网格布局
  - 移动端：单列垂直布局，快捷操作2列网格
- **商品管理**: 商品列表、搜索筛选、创建编辑
- **笔记管理**: 基于商品的笔记创作和管理
- **AI提示词**: 系统提示词库，支持一键复制

### 响应式设计特色
- **桌面端 (≥768px)**: 容器最大宽度1200-1400px，居中显示，充分利用屏幕空间
- **移动端 (<768px)**: 保持原有简洁的单列布局
- **自适应布局**: 根据屏幕尺寸自动调整网格列数和间距

## 🧪 测试

### 运行后端API测试
```bash
cd backend
python test_api.py
```

### 运行前后端集成测试
```bash
python test_integration.py
```

### 手动测试
1. 访问 http://localhost:3000
2. 使用 admin/admin123 登录
3. 测试各个功能模块

## 📚 API文档

启动后端服务器后，访问 http://localhost:8000/docs 查看完整的API文档。

### 主要API端点
- **认证**: `/api/v1/auth/token` - 用户登录
- **用户管理**: `/api/v1/users/` - 用户CRUD操作
- **商品管理**: `/api/v1/products/` - 商品CRUD操作
- **笔记管理**: `/api/v1/notes/` - 笔记CRUD操作
- **提示词管理**: `/api/v1/prompts/` - AI提示词管理
- **文件上传**: `/api/v1/upload/` - 图片上传(最大10MB)、视频上传(最大500MB)

## 🚀 部署

### 开发环境
项目已配置好开发环境，按照快速开始指南即可运行。

### 生产环境
1. 修改 `backend/.env` 中的配置
2. 使用 `gunicorn` 部署后端
3. 使用 `npm run build` 构建前端
4. 配置 Nginx 反向代理

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 🆕 v1.7.0 新功能

### 笔记下载功能
- **笔记预览**：卡片式展示笔记列表，包含标题、内容摘要、封面图片
- **商品关联**：显示笔记关联的商品信息，支持点击跳转到商品详情
- **使用状态管理**：支持标记笔记为"已使用"或"未使用"状态
- **统计信息**：显示总笔记数、未使用数、已使用数和使用率
- **搜索筛选**：支持按关键词搜索和按使用状态筛选
- **快捷操作**：一键复制标题/内容、批量下载图片、图片预览
- **详情页面**：完整笔记信息展示，每个图片/视频独立下载

### 商品下载功能
- **商品列表**：展示用户权限范围内的所有商品
- **权限控制**：普通用户只能查看自己的商品，管理员可查看所有
- **快捷操作**：一键复制商品标题/描述/特点分析，一键下载所有图片
- **详情页面**：完整商品信息展示，图片网格布局，独立下载按钮
- **批量下载**：支持一键下载商品所有图片

### 界面优化
- **菜单增强**：新增"笔记下载"和"商品下载"快捷入口
- **关联展示**：笔记页面显示关联商品，支持快速跳转
- **视觉改进**：优化卡片布局、标签样式和交互体验

## 📋 v1.6.0 功能

### 商品用户关联管理
- **用户指定**：管理员创建商品时可以指定归属用户
- **用户变更**：管理员可以修改商品的归属用户
- **用户显示**：商品列表和详情页显示归属用户信息
- **权限控制**：普通用户只能管理自己的商品

### 菜单栏快捷操作
- **快捷按钮**：左侧菜单新增创建商品、创建笔记、批量创建笔记按钮
- **视觉区分**：快捷操作按钮采用特殊样式，易于识别
- **智能跳转**：创建笔记功能支持先选择商品再创建

### 批量创建界面优化
- **大尺寸窗口**：默认800x600像素，最大可达屏幕90%
- **拖拽调整**：支持鼠标拖拽调整对话框大小
- **行号显示**：左侧显示行号，方便查看笔记数量
- **无数量限制**：移除50个标题的限制，支持无限制创建

## 📋 v2.7.1 Bug修复

### 商品搜索功能修复
- **搜索功能修复**：修复笔记管理页面商品搜索功能不生效的问题
- **前端优化**：商品搜索时同时触发笔记重新加载，确保搜索结果立即显示
- **API增强**：后端API支持在商品标题和简称中搜索，扩展搜索范围
- **搜索逻辑**：将商品搜索关键词合并到笔记搜索参数中，实现跨表搜索

### TypeScript构建错误修复
- **类型修复**：修复van-tag组件size属性的TypeScript类型错误
- **样式优化**：通过CSS控制商品标记大小，保持视觉效果一致
- **构建成功**：确保npm run build命令能够成功执行

### 技术改进
- **实时搜索**：商品搜索支持实时筛选相关笔记
- **精确匹配**：支持商品名称的模糊搜索和部分匹配
- **组合搜索**：支持笔记内容搜索和商品搜索同时使用
- **代码质量**：修复TypeScript类型检查错误，提升代码质量

## 📋 v2.7.0 新功能

### 首页快捷操作优化
- **笔记管理入口**：在首页快捷操作区域新增"笔记管理"按钮
- **图标优化**：使用更合适的图标和渐变色彩搭配
- **快速访问**：一键跳转到笔记管理页面，提升操作效率

### 笔记管理页面增强
- **商品搜索功能**：新增商品搜索框，支持按商品名称快速筛选笔记
- **标记显示优化**：
  - 重新设计标记布局，采用垂直排列方式
  - 为使用状态标记添加图标（时钟图标表示未使用，成功图标表示已使用）
  - 新增商品名称标记，显示笔记关联的商品信息
  - 改进标记的视觉层次和间距设计
- **标题显示优化**：
  - 支持标题换行显示，最多显示2行
  - 添加智能换行处理，确保长标题能完整显示
  - 优化文字排版和行高设置
- **卡片布局调整**：增加卡片高度以容纳更多内容信息

### 数据源优化
- **API接口升级**：使用包含商品信息的笔记接口，支持显示商品名称
- **搜索功能增强**：商品搜索支持实时筛选，提升用户体验

### 配置修复
- **代理配置修复**：修复前端开发环境代理端口配置问题
- **网络连接优化**：确保前后端通信正常，提升系统稳定性

## 📋 v2.6.0 功能

### 卡片布局全面优化
- **笔记管理页面**：重新设计为紧凑的网格卡片布局，支持横向排布多个卡片
- **笔记下载页面**：优化卡片尺寸，缩略图从80px缩小到60px，整体更紧凑美观
- **响应式网格**：智能网格布局，桌面端一行显示4-5个卡片，移动端自适应
- **视觉优化**：统一的圆角、阴影、悬停效果，提升整体视觉体验

### 商品管理卡片布局优化
- **现代化设计**：重新设计商品管理页面，采用横向卡片布局
- **完整信息展示**：显示商品缩略图、标题、价格、笔记数量、归属用户、创建时间
- **图片预览**：显示前4张图片，超出显示"+N"
- **响应式设计**：PC端横向布局，手机端纵向布局，完美适配各种屏幕
- **交互优化**：悬停动画、图片预览、确认删除对话框

### 笔记管理功能
- **独立管理页面**：全新的笔记管理页面，提供完整的笔记管理功能
- **搜索和筛选**：支持标题内容搜索、使用状态筛选、关联商品筛选
- **统计信息**：实时显示总笔记数、未使用数、已使用数、使用率
- **笔记卡片**：紧凑的网格布局，显示笔记缩略图、标题、内容预览、状态标签
- **操作功能**：编辑、详情、状态切换、删除等完整操作
- **导航集成**：在侧边栏菜单中添加笔记管理入口

### 设计系统统一
- **卡片设计**：统一的圆角8px、阴影效果、悬停动画
- **颜色方案**：价格标签绿色、状态标签蓝色/红色、商品标签绿色
- **响应式布局**：CSS Grid网格系统，自适应屏幕尺寸
- **用户体验**：加载状态、空状态提示、错误处理、操作确认

### Bug修复
- **菜单高亮**：修复商品下载页面时商品管理菜单错误高亮
- **详情跳转**：修复商品卡片详情按钮跳转路径错误
- **界面优化**：移除商品卡片中的特点分析显示
- **API修复**：修复笔记管理页面API调用404错误

## 📄 许可证

MIT License

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 提交 Issue: https://gitee.com/bin1874/xhs_notes_manager/issues
- 项目地址: https://gitee.com/bin1874/xhs_notes_manager
