# Ubuntu 24 + Nginx + MySQL 快速部署指南

## 🚀 一键部署

### 前提条件

- **服务器**: Ubuntu 24.04 LTS
- **服务器名称**: ben
- **项目目录**: `/home/<USER>/xhs_notes_manager`
- **数据库**: MySQL 8.0+
- **数据库名**: xhsnote
- **数据库密码**: dhwdw6789jhgffHGF

### 端口配置 (避免冲突)

- **前端端口**: 8080 (Nginx，替代默认80端口)
- **后端端口**: 9000 (FastAPI，替代默认8000端口)
- **数据库端口**: 3306 (MySQL默认端口)

### 快速部署步骤

#### 1. 准备项目代码

```bash
# 创建项目目录
sudo mkdir -p /home/<USER>
sudo chown $USER:$USER /home/<USER>

# 将项目代码复制到目标目录
# 方法1: 从Git克隆
cd /home/<USER>
git clone <your-repo-url> xhs_notes_manager

# 方法2: 从现有代码复制
cp -r /path/to/your/xhs_notes_manager /home/<USER>/

# 确保目录结构正确
ls -la /home/<USER>/xhs_notes_manager
# 应该看到: backend/ frontend/ docs/ scripts/ 等目录
```

#### 2. 运行环境检查

```bash
cd /home/<USER>/xhs_notes_manager

# 设置脚本权限
chmod +x scripts/pre-deploy-check.sh
chmod +x scripts/ubuntu24-deploy.sh

# 运行环境检查
./scripts/pre-deploy-check.sh
```

#### 3. 自动部署

```bash
# 如果环境检查通过，运行自动部署脚本
./scripts/ubuntu24-deploy.sh
```

#### 4. 验证部署

```bash
# 检查服务状态
sudo systemctl status xhs-backend
sudo systemctl status nginx
sudo systemctl status mysql

# 测试访问
curl http://ben:8080/health
curl -I http://ben:8080/
```

## 🔧 手动部署（如果自动部署失败）

### 步骤1: 系统准备

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装基础软件
sudo apt install -y curl wget git vim htop unzip

# 安装Python
sudo apt install -y python3 python3-pip python3-venv python3-dev

# 安装Node.js 20
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt install -y nodejs
```

### 步骤2: 安装数据库

```bash
# 安装MySQL
sudo apt install -y mysql-server mysql-client
sudo systemctl start mysql
sudo systemctl enable mysql

# 配置数据库
sudo mysql -e "CREATE DATABASE xhsnote CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
sudo mysql -e "CREATE USER 'xhsnote_user'@'localhost' IDENTIFIED BY 'dhwdw6789jhgffHGF';"
sudo mysql -e "GRANT ALL PRIVILEGES ON xhsnote.* TO 'xhsnote_user'@'localhost';"
sudo mysql -e "FLUSH PRIVILEGES;"
```

### 步骤3: 配置后端

```bash
cd /home/<USER>/xhs_notes_manager/backend

# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 安装依赖
pip install --upgrade pip
pip install -r requirements.txt
pip install gunicorn uvicorn[standard] pymysql cryptography

# 创建配置文件
cat > .env.production << 'EOF'
DATABASE_URL=mysql+pymysql://xhsnote_user:dhwdw6789jhgffHGF@localhost:3306/xhsnote
SECRET_KEY=your-super-secret-key-change-this
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=43200
UPLOAD_DIR=/home/<USER>/xhs_notes_manager/backend/uploads
ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000,http://ben:8080,https://ben:8080
DEBUG=false
ENVIRONMENT=production
EOF

# 初始化数据库
export ENV_FILE=/home/<USER>/xhs_notes_manager/backend/.env.production
python init_db.py

# 创建管理员账户
python reset_admin_password.py create admin admin123
```

### 步骤4: 配置前端

```bash
cd /home/<USER>/xhs_notes_manager/frontend

# 安装依赖
npm install

# 构建生产版本
npm run build
```

### 步骤5: 安装和配置Nginx

```bash
# 安装Nginx
sudo apt install -y nginx

# 创建站点配置
sudo tee /etc/nginx/sites-available/xhs-notes-manager > /dev/null << 'EOF'
server {
    listen 80;
    server_name ben localhost;
    
    client_max_body_size 10M;

    location / {
        root /home/<USER>/xhs_notes_manager/frontend/dist;
        try_files $uri $uri/ /index.html;
    }

    location /api/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /uploads/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
    }

    location /docs {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
    }

    location /health {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        access_log off;
    }
}
EOF

# 启用站点
sudo ln -sf /etc/nginx/sites-available/xhs-notes-manager /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default
sudo nginx -t
sudo systemctl reload nginx
```

### 步骤6: 创建系统服务

```bash
# 创建systemd服务
sudo tee /etc/systemd/system/xhs-backend.service > /dev/null << 'EOF'
[Unit]
Description=XHS Notes Manager Backend
After=network.target mysql.service
Requires=mysql.service

[Service]
Type=notify
User=ubuntu
Group=ubuntu
WorkingDirectory=/home/<USER>/xhs_notes_manager/backend
Environment=PATH=/home/<USER>/xhs_notes_manager/backend/venv/bin
Environment=ENV_FILE=/home/<USER>/xhs_notes_manager/backend/.env.production
ExecStart=/home/<USER>/xhs_notes_manager/backend/venv/bin/gunicorn -c gunicorn.conf.py app.main:app
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
EOF

# 启动服务
sudo systemctl daemon-reload
sudo systemctl enable xhs-backend
sudo systemctl start xhs-backend
```

## 📋 部署后检查

### 服务状态检查

```bash
# 检查后端服务
sudo systemctl status xhs-backend

# 检查Nginx服务
sudo systemctl status nginx

# 检查MySQL服务
sudo systemctl status mysql
```

### 功能测试

```bash
# 测试API健康检查
curl http://ben:8080/health

# 测试前端页面
curl -I http://ben:8080/

# 测试API文档
curl -I http://ben:9000/docs
```

### 日志查看

```bash
# 查看后端日志
sudo journalctl -u xhs-backend -f

# 查看Nginx错误日志
sudo tail -f /var/log/nginx/error.log

# 查看MySQL日志
sudo tail -f /var/log/mysql/error.log
```

## 🌐 访问应用

部署完成后，可以通过以下方式访问：

- **前端界面**: <http://ben:8080>
- **API文档**: <http://ben:9000/docs>
- **健康检查**: <http://ben:8080/health>

**默认管理员账户**:
- 用户名: admin
- 密码: admin123

## 🔧 常用管理命令

```bash
# 启动服务
sudo systemctl start xhs-backend

# 停止服务
sudo systemctl stop xhs-backend

# 重启服务
sudo systemctl restart xhs-backend

# 查看服务状态
sudo systemctl status xhs-backend

# 重新加载Nginx配置
sudo systemctl reload nginx

# 查看实时日志
sudo journalctl -u xhs-backend -f
```

## 🚨 故障排除

### 常见问题

1. **服务启动失败**
   ```bash
   sudo journalctl -u xhs-backend --no-pager -l
   ```

2. **数据库连接失败**
   ```bash
   mysql -u xhsnote_user -p'dhwdw6789jhgffHGF' xhsnote -e "SELECT 1;"
   ```

3. **权限问题**
   ```bash
   sudo chown -R $USER:$USER /home/<USER>/xhs_notes_manager
   ```

4. **端口被占用**
   ```bash
   sudo netstat -tlnp | grep :8000
   sudo netstat -tlnp | grep :80
   ```

### 重新部署

如果需要重新部署：

```bash
# 停止服务
sudo systemctl stop xhs-backend

# 清理旧数据（可选）
sudo mysql -e "DROP DATABASE IF EXISTS xhsnote;"

# 重新运行部署脚本
./scripts/ubuntu24-deploy.sh
```

## 📞 技术支持

如果遇到问题，请：

1. 运行环境检查脚本
2. 查看相关日志文件
3. 检查服务状态
4. 参考故障排除部分

---

**部署完成！** 🎉
