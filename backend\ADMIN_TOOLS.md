# 管理员工具使用指南

## 🔧 管理员密码管理工具

### 功能说明
- 查看所有用户
- 重置管理员密码
- 创建新的管理员用户
- 测试登录功能

### 使用方法

#### 1. 查看所有用户
```bash
python reset_admin_password.py list
```

#### 2. 重置管理员密码
```bash
python reset_admin_password.py reset <新密码>
```

示例：
```bash
python reset_admin_password.py reset admin1234
python reset_admin_password.py reset myNewPassword123
```

#### 3. 创建新的管理员用户
```bash
python reset_admin_password.py create <用户名> <密码>
```

示例：
```bash
python reset_admin_password.py create superadmin mypassword
```

#### 4. 测试登录功能
```bash
python test_login.py <用户名> <密码>
```

示例：
```bash
python test_login.py admin admin1234
```

## 🚨 重要注意事项

1. **停止服务器**：在运行密码重置脚本之前，请确保后端服务器已停止
2. **密码安全**：请使用强密码，包含字母、数字和特殊字符
3. **备份数据**：在进行重要操作前，建议备份数据库文件 `xhs_notes.db`

## 📋 默认账户信息

- **默认管理员用户名**：`admin`
- **默认密码**：`admin123`（初始化时）

## 🔍 故障排除

### 问题：无法登录
1. 检查用户名和密码是否正确
2. 使用 `python reset_admin_password.py list` 查看用户列表
3. 使用 `python test_login.py admin <密码>` 测试登录
4. 如果仍然无法登录，重置密码：`python reset_admin_password.py reset <新密码>`

### 问题：忘记管理员密码
```bash
# 重置为默认密码
python reset_admin_password.py reset admin123

# 或设置新密码
python reset_admin_password.py reset admin1234
```

### 问题：需要创建新的管理员
```bash
python reset_admin_password.py create newadmin newpassword
```

## 📝 使用示例

### 完整的密码重置流程
```bash
# 1. 停止后端服务器（Ctrl+C）

# 2. 查看当前用户
python reset_admin_password.py list

# 3. 重置管理员密码
python reset_admin_password.py reset admin1234

# 4. 测试新密码
python test_login.py admin admin1234

# 5. 重新启动后端服务器
python -m uvicorn app.main:app --reload --host 127.0.0.1 --port 8000
```

### 创建额外管理员
```bash
# 创建备用管理员账户
python reset_admin_password.py create backup_admin secure_password_123

# 验证新账户
python test_login.py backup_admin secure_password_123
```

## 🔐 安全建议

1. **定期更换密码**：建议定期更换管理员密码
2. **使用强密码**：密码应包含大小写字母、数字和特殊字符
3. **限制访问**：只有必要的人员才应该知道管理员密码
4. **日志监控**：定期检查登录日志，发现异常及时处理

## 📞 技术支持

如果遇到问题，请检查：
1. Python 环境是否正确
2. 数据库文件是否存在
3. 依赖包是否安装完整
4. 是否有足够的文件权限
