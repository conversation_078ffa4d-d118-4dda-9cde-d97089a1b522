from fastapi import APIRouter

from .endpoints import auth, users, products, notes, prompts, upload, recycle_bin, admin_users, notes_download, products_download

api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(products.router, prefix="/products", tags=["products"])
api_router.include_router(notes.router, prefix="/notes", tags=["notes"])
api_router.include_router(prompts.router, prefix="/prompts", tags=["prompts"])
api_router.include_router(upload.router, prefix="/upload", tags=["upload"])
api_router.include_router(recycle_bin.router, prefix="/recycle-bin", tags=["recycle-bin"])
api_router.include_router(admin_users.router, prefix="/admin/users", tags=["admin-users"])
api_router.include_router(notes_download.router, prefix="/notes-download", tags=["notes-download"])
api_router.include_router(products_download.router, prefix="/products-download", tags=["products-download"])
