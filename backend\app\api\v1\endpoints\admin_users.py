from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, desc, asc, or_, func
from passlib.context import CryptContext

from ....core.database import get_db
from ....core.deps import get_current_admin_user
from ....models.user import User, UserRole
from ....schemas.user import User as UserSchema, UserCreate, UserUpdate

router = APIRouter()
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


@router.get("/", response_model=List[UserSchema])
async def read_users(
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    q: Optional[str] = Query(None, description="Search query"),
    role: Optional[UserRole] = Query(None, description="Filter by role"),
    sort_by: str = Query("created_at", description="Sort field"),
    order: str = Query("desc", regex="^(asc|desc)$"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    Retrieve users. Only accessible by administrators.
    """
    # Build base query
    query = select(User)
    
    # Apply search filter
    if q:
        search_filter = or_(
            User.username.ilike(f"%{q}%"),
            User.email.ilike(f"%{q}%") if hasattr(User, 'email') else False
        )
        query = query.where(search_filter)
    
    # Apply role filter
    if role:
        query = query.where(User.role == role)
    
    # Apply sorting
    if sort_by == "username":
        order_column = User.username
    elif sort_by == "role":
        order_column = User.role
    else:  # default to id
        order_column = User.id
    
    if order == "asc":
        query = query.order_by(asc(order_column))
    else:
        query = query.order_by(desc(order_column))
    
    # Apply pagination
    query = query.offset(skip).limit(limit)
    
    result = await db.execute(query)
    users = result.scalars().all()
    
    return users


@router.post("/", response_model=UserSchema)
async def create_user(
    user_in: UserCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    Create new user. Only accessible by administrators.
    """
    # Check if username already exists
    result = await db.execute(select(User).where(User.username == user_in.username))
    if result.scalar_one_or_none():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Username already registered"
        )
    
    # Hash password
    hashed_password = pwd_context.hash(user_in.password)
    
    # Create user
    db_user = User(
        username=user_in.username,
        hashed_password=hashed_password,
        role=user_in.role if hasattr(user_in, 'role') else UserRole.USER
    )
    
    db.add(db_user)
    await db.commit()
    await db.refresh(db_user)
    
    return db_user


@router.get("/{user_id}", response_model=UserSchema)
async def read_user(
    user_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    Get a specific user by ID. Only accessible by administrators.
    """
    result = await db.execute(select(User).where(User.id == user_id))
    user = result.scalar_one_or_none()
    
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    return user


@router.put("/{user_id}", response_model=UserSchema)
async def update_user(
    user_id: int,
    user_in: UserUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    Update a user. Only accessible by administrators.
    """
    result = await db.execute(select(User).where(User.id == user_id))
    user = result.scalar_one_or_none()

    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    # Prevent admin from modifying their own role
    if user.id == current_user.id and hasattr(user_in, 'role') and user_in.role:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot modify your own role"
        )

    # Update user fields
    update_data = user_in.dict(exclude_unset=True)
    
    # Handle password update
    if "password" in update_data and update_data["password"]:
        update_data["hashed_password"] = pwd_context.hash(update_data["password"])
        del update_data["password"]
    
    for field, value in update_data.items():
        if hasattr(user, field):
            setattr(user, field, value)

    await db.commit()
    await db.refresh(user)
    
    return user


@router.delete("/{user_id}")
async def delete_user(
    user_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    Delete a user. Only accessible by administrators.
    """
    result = await db.execute(select(User).where(User.id == user_id))
    user = result.scalar_one_or_none()

    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    # Prevent admin from deleting themselves
    if user.id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot delete your own account"
        )

    await db.delete(user)
    await db.commit()
    return {"message": "User deleted successfully"}


@router.get("/{user_id}/stats")
async def get_user_stats(
    user_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    Get user statistics. Only accessible by administrators.
    """
    result = await db.execute(select(User).where(User.id == user_id))
    user = result.scalar_one_or_none()

    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    # Get user statistics
    from ....models.product import Product
    from ....models.note import Note
    
    # Count products - 使用数据库计数，避免加载所有记录到内存
    products_result = await db.execute(
        select(func.count(Product.id)).where(Product.owner_id == user_id)
    )
    products_count = products_result.scalar() or 0
    
    # Count notes - 使用数据库计数，只计数未删除的笔记
    notes_result = await db.execute(
        select(func.count(Note.id)).join(Product).where(
            Product.owner_id == user_id,
            Note.deleted_at.is_(None)
        )
    )
    notes_count = notes_result.scalar() or 0
    
    return {
        "user_id": user_id,
        "username": user.username,
        "role": user.role,
        "products_count": products_count,
        "notes_count": notes_count
    }
