from typing import List, Optional
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, desc, asc, func

from ....core.database import get_db
from ....core.deps import get_current_user, get_current_admin_user
from ....models.user import User, UserRole
from ....models.product import Product
from ....models.note import Note, NoteLog, NoteStatus, NoteUsageStatus
from ....schemas.note import (
    Note as NoteSchema,
    NoteCreate,
    NoteUpdate,
    NoteStatusUpdate,
    NoteLog as NoteLogSchema
)
from ....utils.file_utils import serialize_files, deserialize_files

router = APIRouter()


async def check_product_access(product_id: int, current_user: User, db: AsyncSession) -> Product:
    """Check if user has access to the product."""
    query = select(Product).where(Product.id == product_id)
    
    # Apply user filter for non-admin users
    if current_user.role != UserRole.ADMIN:
        query = query.where(Product.owner_id == current_user.id)
    
    result = await db.execute(query)
    product = result.scalar_one_or_none()
    
    if not product:
        raise HTTPException(status_code=404, detail="Product not found")
    
    return product


async def check_note_access(note_id: int, current_user: User, db: AsyncSession) -> Note:
    """Check if user has access to the note."""
    query = select(Note).where(and_(Note.id == note_id, Note.deleted_at.is_(None)))
    
    # Apply user filter for non-admin users
    if current_user.role != UserRole.ADMIN:
        # Join with Product to check ownership
        query = query.join(Product).where(Product.owner_id == current_user.id)
    
    result = await db.execute(query)
    note = result.scalar_one_or_none()
    
    if not note:
        raise HTTPException(status_code=404, detail="Note not found")
    
    return note


async def create_note_log(note_id: int, message: str, db: AsyncSession):
    """Create a log entry for note operations."""
    log = NoteLog(note_id=note_id, log_message=message)
    db.add(log)


@router.get("/products/{product_id}/notes/", response_model=List[NoteSchema])
async def read_product_notes(
    product_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    q: Optional[str] = Query(None, description="Search query"),
    sort_by: str = Query("created_at", description="Sort field"),
    order: str = Query("desc", regex="^(asc|desc)$"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Retrieve notes for a specific product.
    """
    # Check product access
    await check_product_access(product_id, current_user, db)
    
    # Build query for notes (exclude soft deleted)
    query = select(Note).where(
        and_(Note.product_id == product_id, Note.deleted_at.is_(None))
    )
    
    # Apply search filter
    if q:
        search_filter = or_(
            Note.title.ilike(f"%{q}%"),
            Note.body.ilike(f"%{q}%"),
            Note.tags.ilike(f"%{q}%")
        )
        query = query.where(search_filter)
    
    # Apply sorting
    sort_column = getattr(Note, sort_by, Note.created_at)
    if order == "desc":
        query = query.order_by(desc(sort_column))
    else:
        query = query.order_by(asc(sort_column))
    
    # Apply pagination
    query = query.offset(skip).limit(limit)
    
    result = await db.execute(query)
    notes = result.scalars().all()
    
    # Convert JSON strings to lists for response
    for note in notes:
        note.cover_images_candidate = deserialize_files(note.cover_images_candidate)
        note.note_images = deserialize_files(note.note_images)
        note.videos = deserialize_files(note.videos)
    
    return notes



# 注意：笔记统计功能已移至 /notes-download/stats 端点
# 该端点提供更合理的统计逻辑（显示全局统计数据）


@router.get("/{note_id}", response_model=NoteSchema)
async def read_note(
    note_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get a specific note by ID.
    """
    note = await check_note_access(note_id, current_user, db)

    # Convert JSON strings to lists for response
    note.cover_images_candidate = deserialize_files(note.cover_images_candidate)
    note.note_images = deserialize_files(note.note_images)
    note.videos = deserialize_files(note.videos)

    return note


@router.put("/{note_id}", response_model=NoteSchema)
async def update_note(
    note_id: int,
    note_in: NoteUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Update a note.
    """
    note = await check_note_access(note_id, current_user, db)

    # Update note fields
    update_data = note_in.dict(exclude_unset=True)

    # Handle file lists
    if "cover_images_candidate" in update_data:
        update_data["cover_images_candidate"] = serialize_files(update_data["cover_images_candidate"])
    if "note_images" in update_data:
        update_data["note_images"] = serialize_files(update_data["note_images"])
    if "videos" in update_data:
        update_data["videos"] = serialize_files(update_data["videos"])

    for field, value in update_data.items():
        setattr(note, field, value)

    await db.commit()
    await db.refresh(note)

    # Create log entry
    await create_note_log(note.id, f"笔记内容更新", db)
    await db.commit()

    # Convert JSON strings to lists for response
    note.cover_images_candidate = deserialize_files(note.cover_images_candidate)
    note.note_images = deserialize_files(note.note_images)
    note.videos = deserialize_files(note.videos)

    return note


@router.delete("/{note_id}")
async def delete_note(
    note_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Soft delete a note.
    """
    note = await check_note_access(note_id, current_user, db)

    # Soft delete by setting deleted_at timestamp
    note.deleted_at = datetime.utcnow()
    await db.commit()

    # Create log entry
    await create_note_log(note.id, f"笔记删除", db)
    await db.commit()

    return {"message": "Note deleted successfully"}


@router.patch("/{note_id}/status", response_model=NoteSchema)
async def update_note_status(
    note_id: int,
    status_update: NoteStatusUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Update note status.
    """
    note = await check_note_access(note_id, current_user, db)

    old_status = note.status
    note.status = status_update.status
    await db.commit()
    await db.refresh(note)

    # Create log entry
    await create_note_log(
        note.id,
        f"笔记状态从 {old_status.value} 修改为 {status_update.status.value}",
        db
    )
    await db.commit()

    # Convert JSON strings to lists for response
    note.cover_images_candidate = deserialize_files(note.cover_images_candidate)
    note.note_images = deserialize_files(note.note_images)
    note.videos = deserialize_files(note.videos)

    return note


@router.get("/{note_id}/logs", response_model=List[NoteLogSchema])
async def read_note_logs(
    note_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get logs for a specific note.
    """
    # Check note access first
    await check_note_access(note_id, current_user, db)

    # Get logs
    result = await db.execute(
        select(NoteLog)
        .where(NoteLog.note_id == note_id)
        .order_by(desc(NoteLog.created_at))
    )
    logs = result.scalars().all()

    return logs


@router.get("/", response_model=List[NoteSchema])
async def read_notes(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    q: Optional[str] = Query(None, description="Search query"),
    usage_status: Optional[NoteUsageStatus] = Query(None, description="Filter by usage status"),
    product_id: Optional[int] = Query(None, description="Filter by product ID"),
    sort_by: str = Query("created_at", description="Sort field"),
    order: str = Query("desc", regex="^(asc|desc)$"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Retrieve notes with filtering and pagination.
    """
    # Build base query with product join for user access control
    query = select(Note).join(Product, Note.product_id == Product.id).where(Note.deleted_at.is_(None))

    # Apply user access control
    if current_user.role != UserRole.ADMIN:
        query = query.where(Product.owner_id == current_user.id)

    # Apply filters
    if q:
        search_filter = or_(
            Note.title.ilike(f"%{q}%"),
            Note.body.ilike(f"%{q}%"),
            Note.tags.ilike(f"%{q}%")
        )
        query = query.where(search_filter)

    if usage_status:
        query = query.where(Note.usage_status == usage_status)

    if product_id:
        query = query.where(Note.product_id == product_id)

    # Apply sorting
    if hasattr(Note, sort_by):
        sort_column = getattr(Note, sort_by)
        if order == "desc":
            query = query.order_by(desc(sort_column))
        else:
            query = query.order_by(asc(sort_column))
    else:
        query = query.order_by(desc(Note.created_at))

    # Apply pagination
    query = query.offset(skip).limit(limit)

    # Execute query
    result = await db.execute(query)
    notes = result.scalars().all()

    # Convert JSON strings to lists for response
    for note in notes:
        note.cover_images_candidate = deserialize_files(note.cover_images_candidate)
        note.note_images = deserialize_files(note.note_images)
        note.videos = deserialize_files(note.videos)

    return notes



