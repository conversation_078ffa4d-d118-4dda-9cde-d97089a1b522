"""
笔记下载功能API端点
提供笔记预览、下载、使用状态管理等功能
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, desc, asc, func

from ....core.database import get_db
from ....core.deps import get_current_user
from ....models.user import User, UserRole
from ....models.product import Product
from ....models.note import Note, NoteUsageStatus
from ....schemas.note import Note as NoteSchema, NoteUsageStatusUpdate
from ....utils.file_utils import deserialize_files

router = APIRouter()


@router.get("/preview", response_model=List[NoteSchema])
async def get_notes_preview(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(20, ge=1, le=100, description="返回的记录数"),
    q: Optional[str] = Query(None, description="搜索关键词"),
    product_search: Optional[str] = Query(None, description="商品搜索关键词"),
    usage_status: Optional[NoteUsageStatus] = Query(None, description="使用状态筛选"),
    product_id: Optional[int] = Query(None, description="商品ID筛选"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取笔记预览列表，支持搜索和筛选
    普通用户只能看到自己商品的笔记，管理员可以看到所有笔记
    """
    # 构建基础查询，包含商品信息
    query = select(
        Note,
        Product.title.label('product_title'),
        Product.short_name.label('product_short_name')
    ).join(Product, Note.product_id == Product.id)
    
    # 应用用户权限过滤
    if current_user.role != UserRole.ADMIN:
        query = query.where(Product.owner_id == current_user.id)
    
    # 应用软删除过滤
    query = query.where(Note.deleted_at.is_(None))
    
    # 应用搜索过滤
    if q:
        query = query.where(
            or_(
                Note.title.contains(q),
                Note.body.contains(q),
                Note.tags.contains(q),
                Product.title.contains(q),
                Product.short_name.contains(q)
            )
        )

    # 应用商品搜索过滤
    if product_search:
        query = query.where(
            or_(
                Product.title.contains(product_search),
                Product.short_name.contains(product_search)
            )
        )

    # 应用使用状态过滤
    if usage_status:
        query = query.where(Note.usage_status == usage_status)



    # 应用商品ID过滤
    if product_id:
        query = query.where(Note.product_id == product_id)
    
    # 应用分页和排序
    query = query.order_by(desc(Note.created_at)).offset(skip).limit(limit)
    
    result = await db.execute(query)
    rows = result.all()

    # 构建返回数据
    notes_with_product = []
    for row in rows:
        note, product_title, product_short_name = row

        # 转换图片字段
        note.cover_images_candidate = deserialize_files(note.cover_images_candidate)
        note.note_images = deserialize_files(note.note_images)
        note.videos = deserialize_files(note.videos)

        # 创建包含商品信息的笔记对象
        note_dict = {
            "id": note.id,
            "title": note.title,
            "body": note.body,
            "tags": note.tags,
            "cover_images_candidate": note.cover_images_candidate,
            "selected_cover_image": note.selected_cover_image,
            "note_images": note.note_images,
            "videos": note.videos,
            "scheduled_at": note.scheduled_at,
            "status": note.status,
            "usage_status": note.usage_status,
            "created_at": note.created_at,
            "updated_at": note.updated_at,
            "product_id": note.product_id,
            "product_title": product_title,
            "product_short_name": product_short_name,
            "product_name": product_title or product_short_name  # 优先使用title，fallback到short_name
        }
        notes_with_product.append(note_dict)

    return notes_with_product


@router.get("/{note_id}/content")
async def get_note_content(
    note_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取笔记的完整内容，用于复制
    """
    # 查询笔记及其关联的商品
    query = select(
        Note,
        Product.title.label('product_title'),
        Product.short_name.label('product_short_name')
    ).join(Product, Note.product_id == Product.id).where(
        and_(Note.id == note_id, Note.deleted_at.is_(None))
    )
    
    # 应用用户权限过滤
    if current_user.role != UserRole.ADMIN:
        query = query.where(Product.owner_id == current_user.id)
    
    result = await db.execute(query)
    row = result.first()

    if not row:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="笔记不存在或无权访问"
        )

    note, product_title, product_short_name = row

    # 转换图片字段
    note.cover_images_candidate = deserialize_files(note.cover_images_candidate)
    note.note_images = deserialize_files(note.note_images)
    note.videos = deserialize_files(note.videos)

    return {
        "id": note.id,
        "title": note.title,
        "body": note.body,
        "tags": note.tags,
        "cover_images_candidate": note.cover_images_candidate,
        "selected_cover_image": note.selected_cover_image,
        "note_images": note.note_images,
        "videos": note.videos,
        "usage_status": note.usage_status,
        "created_at": note.created_at,
        "product_id": note.product_id,
        "product_title": product_title,
        "product_short_name": product_short_name,
        "product_name": product_title or product_short_name
    }


@router.put("/{note_id}/usage-status")
async def update_note_usage_status(
    note_id: int,
    status_update: NoteUsageStatusUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    更新笔记的使用状态
    """
    # 查询笔记及其关联的商品
    query = select(Note).join(Product, Note.product_id == Product.id).where(
        and_(Note.id == note_id, Note.deleted_at.is_(None))
    )
    
    # 应用用户权限过滤
    if current_user.role != UserRole.ADMIN:
        query = query.where(Product.owner_id == current_user.id)
    
    result = await db.execute(query)
    note = result.scalar_one_or_none()
    
    if not note:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="笔记不存在或无权访问"
        )
    
    # 更新使用状态
    note.usage_status = status_update.usage_status
    await db.commit()
    await db.refresh(note)
    
    return {
        "id": note.id,
        "title": note.title,
        "usage_status": note.usage_status,
        "message": f"笔记使用状态已更新为: {status_update.usage_status}"
    }


@router.get("/stats")
async def get_notes_stats(
    q: Optional[str] = Query(None, description="搜索关键词"),
    product_search: Optional[str] = Query(None, description="商品搜索关键词"),
    usage_status: Optional[NoteUsageStatus] = Query(None, description="使用状态筛选"),
    product_id: Optional[int] = Query(None, description="商品ID筛选"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取笔记统计信息，始终返回全局统计数据（不受usage_status筛选条件影响）
    """
    # 统计信息显示全局数据，不受任何搜索或筛选条件影响
    # 只应用用户权限过滤（普通用户只看自己的数据，管理员看全部）
    
    # 统计总数
    total_query = select(func.count(Note.id)).join(Product, Note.product_id == Product.id).where(
        Note.deleted_at.is_(None)
    )
    
    # 只应用用户权限过滤
    if current_user.role != UserRole.ADMIN:
        total_query = total_query.where(Product.owner_id == current_user.id)
    
    total_result = await db.execute(total_query)
    total_count = total_result.scalar() or 0
    
    # 统计未使用数量
    unused_query = select(func.count(Note.id)).join(Product, Note.product_id == Product.id).where(
        Note.deleted_at.is_(None),
        Note.usage_status == NoteUsageStatus.UNUSED
    )
    
    # 只应用用户权限过滤
    if current_user.role != UserRole.ADMIN:
        unused_query = unused_query.where(Product.owner_id == current_user.id)
    
    unused_result = await db.execute(unused_query)
    unused_count = unused_result.scalar() or 0
    
    # 计算已使用数量
    used_count = total_count - unused_count
    
    return {
        "total_notes": total_count,
        "unused_notes": unused_count,
        "used_notes": used_count,
        "usage_rate": round((used_count / total_count * 100) if total_count > 0 else 0, 2)
    }
