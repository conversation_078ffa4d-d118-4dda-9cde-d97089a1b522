from typing import List, Optional
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, desc, asc, func

from ....core.database import get_db
from ....core.deps import get_current_user, get_current_admin_user
from ....models.user import User, UserRole
from ....models.product import Product
from ....models.note import Note, NoteLog, NoteStatus, NoteUsageStatus
from ....schemas.product import Product as ProductSchema, ProductCreate, ProductUpdate, ProductWithOwner
from ....schemas.note import (
    Note as NoteSchema,
    NoteCreate,
    BatchCreateNotesRequest,
    BatchCreateNotesResponse
)
from ....utils.file_utils import serialize_files, deserialize_files, serialize_images, deserialize_images

router = APIRouter()


@router.get("/", response_model=List[ProductWithOwner])
async def read_products(
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    q: Optional[str] = Query(None, description="Search query"),
    sort_by: str = Query("created_at", description="Sort field"),
    order: str = Query("desc", regex="^(asc|desc)$"),
    owner_id: Optional[int] = Query(None, description="Filter by owner ID"),
    unused_notes_min: Optional[int] = Query(None, description="Minimum unused notes count"),
    unused_notes_max: Optional[int] = Query(None, description="Maximum unused notes count"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Retrieve products with pagination, search, and sorting.
    Admin can see all products, regular users can only see their own.
    """
    # Build base query with user join and notes count by usage status
    query = select(
        Product,
        User.username.label('owner_username'),
        func.count(Note.id).label('total_notes_count'),
        func.count(Note.id).filter(Note.usage_status == NoteUsageStatus.UNUSED).label('unused_notes_count'),
        func.count(Note.id).filter(Note.usage_status == NoteUsageStatus.USED).label('used_notes_count')
    ).join(
        User, Product.owner_id == User.id, isouter=True
    ).join(
        Note, and_(Product.id == Note.product_id, Note.deleted_at.is_(None)), isouter=True
    ).group_by(Product.id, User.username)

    # Apply user filter
    if current_user.role != UserRole.ADMIN:
        query = query.where(Product.owner_id == current_user.id)
    elif owner_id is not None:
        # Admin can filter by specific owner
        query = query.where(Product.owner_id == owner_id)

    # Apply search filter
    if q:
        search_filter = or_(
            Product.short_name.ilike(f"%{q}%"),
            Product.title.ilike(f"%{q}%"),
            Product.description.ilike(f"%{q}%")
        )
        query = query.where(search_filter)

    # Apply sorting
    sort_column = getattr(Product, sort_by, Product.created_at)
    if order == "desc":
        query = query.order_by(desc(sort_column))
    else:
        query = query.order_by(asc(sort_column))

    # Apply pagination
    query = query.offset(skip).limit(limit)

    result = await db.execute(query)
    rows = result.all()

    # Convert to ProductWithOwner objects and apply unused notes filter
    products = []
    for row in rows:
        product, owner_username, total_notes_count, unused_notes_count, used_notes_count = row

        # Convert None to 0 for counts
        total_notes_count = total_notes_count or 0
        unused_notes_count = unused_notes_count or 0
        used_notes_count = used_notes_count or 0

        # Apply unused notes count filter
        if unused_notes_min is not None and unused_notes_count < unused_notes_min:
            continue
        if unused_notes_max is not None and unused_notes_count > unused_notes_max:
            continue

        # Convert JSON strings to lists
        product.main_images = deserialize_images(product.main_images)
        product.detail_images = deserialize_images(product.detail_images)

        # Create ProductWithOwner object
        product_with_owner = ProductWithOwner(
            id=product.id,
            short_name=product.short_name,
            title=product.title,
            description=product.description,
            price=product.price,
            feature_analysis=product.feature_analysis,
            main_images=product.main_images,
            detail_images=product.detail_images,
            created_at=product.created_at,
            updated_at=product.updated_at,
            owner_id=product.owner_id,
            owner_username=owner_username,
            notes_count=total_notes_count,
            unused_notes_count=unused_notes_count,
            used_notes_count=used_notes_count
        )
        products.append(product_with_owner)

    return products


@router.post("/", response_model=ProductSchema)
async def create_product(
    product_in: ProductCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Create new product.
    Admin can specify owner_id, regular users create for themselves.
    """
    # Determine owner_id
    if current_user.role == UserRole.ADMIN and product_in.owner_id:
        # Admin can specify owner
        owner_id = product_in.owner_id

        # Verify the specified user exists
        user_result = await db.execute(select(User).where(User.id == owner_id))
        if not user_result.scalar_one_or_none():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="指定的用户不存在"
            )
    else:
        # Regular users or admin without specified owner
        owner_id = current_user.id

    db_product = Product(
        short_name=product_in.short_name,
        title=product_in.title,
        main_images=serialize_images(product_in.main_images),
        detail_images=serialize_images(product_in.detail_images),
        description=product_in.description,
        price=product_in.price,
        feature_analysis=product_in.feature_analysis,
        owner_id=owner_id
    )
    
    db.add(db_product)
    await db.commit()
    await db.refresh(db_product)
    
    # Convert JSON strings to lists for response
    db_product.main_images = deserialize_images(db_product.main_images)
    db_product.detail_images = deserialize_images(db_product.detail_images)
    
    return db_product


@router.get("/{product_id}", response_model=ProductWithOwner)
async def read_product(
    product_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get a specific product by ID.
    """
    query = select(
        Product,
        User.username.label('owner_username'),
        func.count(Note.id).label('notes_count')
    ).join(
        User, Product.owner_id == User.id, isouter=True
    ).join(
        Note, and_(Product.id == Note.product_id, Note.deleted_at.is_(None)), isouter=True
    ).where(Product.id == product_id).group_by(Product.id, User.username)

    # Apply user filter for non-admin users
    if current_user.role != UserRole.ADMIN:
        query = query.where(Product.owner_id == current_user.id)

    result = await db.execute(query)
    row = result.first()

    if not row:
        raise HTTPException(status_code=404, detail="Product not found")

    product, owner_username, notes_count = row

    # Convert JSON strings to lists for response
    product.main_images = deserialize_images(product.main_images)
    product.detail_images = deserialize_images(product.detail_images)

    # Create ProductWithOwner object
    product_with_owner = ProductWithOwner(
        id=product.id,
        short_name=product.short_name,
        title=product.title,
        description=product.description,
        price=product.price,
        feature_analysis=product.feature_analysis,
        main_images=product.main_images,
        detail_images=product.detail_images,
        created_at=product.created_at,
        updated_at=product.updated_at,
        owner_id=product.owner_id,
        owner_username=owner_username,
        notes_count=notes_count or 0
    )

    return product_with_owner


@router.put("/{product_id}", response_model=ProductSchema)
async def update_product(
    product_id: int,
    product_in: ProductUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Update a product.
    """
    query = select(Product).where(Product.id == product_id)

    # Apply user filter for non-admin users
    if current_user.role != UserRole.ADMIN:
        query = query.where(Product.owner_id == current_user.id)

    result = await db.execute(query)
    product = result.scalar_one_or_none()

    if not product:
        raise HTTPException(status_code=404, detail="Product not found")

    # Update product fields
    update_data = product_in.dict(exclude_unset=True)

    # Handle owner_id change (admin only)
    if "owner_id" in update_data and current_user.role == UserRole.ADMIN:
        new_owner_id = update_data["owner_id"]
        if new_owner_id:
            # Verify the specified user exists
            user_result = await db.execute(select(User).where(User.id == new_owner_id))
            if not user_result.scalar_one_or_none():
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="指定的用户不存在"
                )
    elif "owner_id" in update_data and current_user.role != UserRole.ADMIN:
        # Remove owner_id from update_data if not admin
        del update_data["owner_id"]

    # Handle image lists
    if "main_images" in update_data:
        update_data["main_images"] = serialize_images(update_data["main_images"])
    if "detail_images" in update_data:
        update_data["detail_images"] = serialize_images(update_data["detail_images"])

    for field, value in update_data.items():
        setattr(product, field, value)

    await db.commit()
    await db.refresh(product)

    # Convert JSON strings to lists for response
    product.main_images = deserialize_images(product.main_images)
    product.detail_images = deserialize_images(product.detail_images)

    return product


@router.delete("/{product_id}")
async def delete_product(
    product_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Delete a product.
    """
    query = select(Product).where(Product.id == product_id)

    # Apply user filter for non-admin users
    if current_user.role != UserRole.ADMIN:
        query = query.where(Product.owner_id == current_user.id)

    result = await db.execute(query)
    product = result.scalar_one_or_none()

    if not product:
        raise HTTPException(status_code=404, detail="Product not found")

    await db.delete(product)
    await db.commit()
    return {"message": "Product deleted successfully"}





async def create_note_log(note_id: int, message: str, db: AsyncSession):
    """Create a log entry for note operations."""
    log = NoteLog(note_id=note_id, log_message=message)
    db.add(log)


@router.post("/{product_id}/notes/", response_model=NoteSchema)
async def create_note(
    product_id: int,
    note_in: NoteCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Create a new note for a product.
    """
    # Check if product exists and user has access
    query = select(Product).where(Product.id == product_id)

    # Apply user filter for non-admin users
    if current_user.role != UserRole.ADMIN:
        query = query.where(Product.owner_id == current_user.id)

    result = await db.execute(query)
    product = result.scalar_one_or_none()

    if not product:
        raise HTTPException(status_code=404, detail="Product not found")

    # Create note
    note_data = note_in.dict()
    note_data["product_id"] = product_id

    # Serialize file lists
    if "cover_images_candidate" in note_data:
        note_data["cover_images_candidate"] = serialize_files(note_data["cover_images_candidate"])
    if "note_images" in note_data:
        note_data["note_images"] = serialize_files(note_data["note_images"])
    if "videos" in note_data:
        note_data["videos"] = serialize_files(note_data["videos"])

    note = Note(**note_data)
    db.add(note)
    await db.commit()
    await db.refresh(note)

    # Create log entry
    await create_note_log(note.id, f"笔记创建", db)
    await db.commit()

    # Deserialize file lists for response
    note.cover_images_candidate = deserialize_files(note.cover_images_candidate)
    note.note_images = deserialize_files(note.note_images)
    note.videos = deserialize_files(note.videos)

    return note


@router.get("/{product_id}/notes/", response_model=List[NoteSchema])
async def read_product_notes(
    product_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    q: Optional[str] = Query(None, description="Search query"),
    sort_by: str = Query("created_at", description="Sort field"),
    order: str = Query("desc", regex="^(asc|desc)$"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Retrieve notes for a specific product.
    """
    # Check if product exists and user has access
    query = select(Product).where(Product.id == product_id)

    # Apply user filter for non-admin users
    if current_user.role != UserRole.ADMIN:
        query = query.where(Product.owner_id == current_user.id)

    result = await db.execute(query)
    product = result.scalar_one_or_none()

    if not product:
        raise HTTPException(status_code=404, detail="Product not found")

    # Build query for notes (exclude soft deleted)
    query = select(Note).where(
        and_(Note.product_id == product_id, Note.deleted_at.is_(None))
    )

    # Apply search filter
    if q:
        search_filter = or_(
            Note.title.contains(q),
            Note.body.contains(q),
            Note.tags.contains(q)
        )
        query = query.where(search_filter)

    # Apply sorting
    if sort_by == "title":
        order_column = Note.title
    elif sort_by == "status":
        order_column = Note.status
    elif sort_by == "updated_at":
        order_column = Note.updated_at
    else:  # default to created_at
        order_column = Note.created_at

    if order == "asc":
        query = query.order_by(asc(order_column))
    else:
        query = query.order_by(desc(order_column))

    # Apply pagination
    query = query.offset(skip).limit(limit)

    result = await db.execute(query)
    notes = result.scalars().all()

    # Deserialize file lists
    for note in notes:
        note.cover_images_candidate = deserialize_files(note.cover_images_candidate)
        note.note_images = deserialize_files(note.note_images)
        note.videos = deserialize_files(note.videos)

    return notes


@router.post("/{product_id}/notes/batch", response_model=BatchCreateNotesResponse)
async def batch_create_notes_for_product(
    product_id: int,
    request: BatchCreateNotesRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Batch create notes for a specific product.
    """
    # Check if product exists and user has access
    query = select(Product).where(Product.id == product_id)

    # Apply user filter for non-admin users
    if current_user.role != UserRole.ADMIN:
        query = query.where(Product.owner_id == current_user.id)

    result = await db.execute(query)
    product = result.scalar_one_or_none()

    if not product:
        raise HTTPException(status_code=404, detail="Product not found")

    # Validate and process titles
    if not request.titles:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="至少需要提供一个标题"
        )

    # Filter empty titles and remove duplicates while preserving order
    processed_titles = []
    seen_titles = set()

    for title in request.titles:
        title = title.strip()
        if title and title not in seen_titles:
            processed_titles.append(title)
            seen_titles.add(title)

    if not processed_titles:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="没有有效的标题"
        )

    # No limit on number of titles (removed 50 title limit)

    # Batch create notes
    created_notes = []
    errors = []

    for title in processed_titles:
        try:
            # Create note
            db_note = Note(
                title=title,
                product_id=product_id,
                status=NoteStatus.TO_BE_PUBLISHED,
                cover_images_candidate=serialize_files([]),
                note_images=serialize_files([]),
                videos=serialize_files([])
            )

            db.add(db_note)
            await db.flush()  # Get the ID without committing

            # Create log entry
            log_entry = NoteLog(
                note_id=db_note.id,
                log_message=f"笔记由 {current_user.username} 通过批量创建功能创建"
            )
            db.add(log_entry)

            created_notes.append(db_note)

        except Exception as e:
            errors.append(f"创建标题 '{title}' 失败: {str(e)}")

    # Commit all changes
    await db.commit()

    # Refresh all created notes to get updated data and deserialize for response
    for note in created_notes:
        await db.refresh(note)

    # Deserialize file lists for response (after refresh)
    for note in created_notes:
        note.cover_images_candidate = deserialize_files(note.cover_images_candidate)
        note.note_images = deserialize_files(note.note_images)
        note.videos = deserialize_files(note.videos)

    return BatchCreateNotesResponse(
        success=True,
        created_count=len(created_notes),
        notes=created_notes,
        errors=errors
    )


@router.get("/users/list")
async def get_users_for_product_assignment(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)  # 只有管理员可以访问
):
    """
    Get list of users for product assignment. Only accessible by administrators.
    """
    result = await db.execute(
        select(User.id, User.username, User.role).order_by(User.username)
    )
    users = result.all()

    return [
        {
            "id": user.id,
            "username": user.username,
            "role": user.role
        }
        for user in users
    ]
