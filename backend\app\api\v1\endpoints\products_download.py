"""
商品下载功能API端点
提供商品预览、下载、内容复制等功能
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, desc, asc, func

from ....core.database import get_db
from ....core.deps import get_current_user
from ....models.user import User, UserRole
from ....models.product import Product
from ....models.note import Note
from ....schemas.product import ProductWithOwner
from ....utils.file_utils import deserialize_files

router = APIRouter()


@router.get("/preview", response_model=List[ProductWithOwner])
async def get_products_preview(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(20, ge=1, le=100, description="返回的记录数"),
    q: Optional[str] = Query(None, description="搜索关键词"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取商品预览列表，支持搜索
    普通用户只能看到自己的商品，管理员可以看到所有商品
    """
    # 构建基础查询，包含用户信息和笔记数量
    query = select(
        Product, 
        User.username.label('owner_username'),
        func.count(Note.id).label('notes_count')
    ).join(
        User, Product.owner_id == User.id, isouter=True
    ).join(
        Note, and_(Product.id == Note.product_id, Note.deleted_at.is_(None)), isouter=True
    ).group_by(Product.id, User.username)
    
    # 应用用户权限过滤
    if current_user.role != UserRole.ADMIN:
        query = query.where(Product.owner_id == current_user.id)
    
    # 应用搜索过滤
    if q:
        query = query.where(
            or_(
                Product.title.contains(q),
                Product.short_name.contains(q),
                Product.description.contains(q),
                Product.feature_analysis.contains(q)
            )
        )
    
    # 应用分页和排序
    query = query.order_by(desc(Product.created_at)).offset(skip).limit(limit)
    
    result = await db.execute(query)
    rows = result.all()
    
    # 构建返回数据
    products = []
    for row in rows:
        product, owner_username, notes_count = row
        
        # 转换图片字段
        product.main_images = deserialize_files(product.main_images)
        product.detail_images = deserialize_files(product.detail_images)
        
        # 创建ProductWithOwner对象
        product_with_owner = ProductWithOwner(
            id=product.id,
            short_name=product.short_name,
            title=product.title,
            description=product.description,
            price=product.price,
            feature_analysis=product.feature_analysis,
            main_images=product.main_images,
            detail_images=product.detail_images,
            created_at=product.created_at,
            updated_at=product.updated_at,
            owner_id=product.owner_id,
            owner_username=owner_username,
            notes_count=notes_count or 0
        )
        products.append(product_with_owner)
    
    return products


@router.get("/{product_id}/content")
async def get_product_content(
    product_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取商品的完整内容，用于复制和下载
    """
    # 查询商品及其关联的用户
    query = select(
        Product, 
        User.username.label('owner_username'),
        func.count(Note.id).label('notes_count')
    ).join(
        User, Product.owner_id == User.id, isouter=True
    ).join(
        Note, and_(Product.id == Note.product_id, Note.deleted_at.is_(None)), isouter=True
    ).where(Product.id == product_id).group_by(Product.id, User.username)
    
    # 应用用户权限过滤
    if current_user.role != UserRole.ADMIN:
        query = query.where(Product.owner_id == current_user.id)
    
    result = await db.execute(query)
    row = result.first()
    
    if not row:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="商品不存在或无权访问"
        )
    
    product, owner_username, notes_count = row
    
    # 转换图片字段
    product.main_images = deserialize_files(product.main_images)
    product.detail_images = deserialize_files(product.detail_images)
    
    return {
        "id": product.id,
        "short_name": product.short_name,
        "title": product.title,
        "description": product.description,
        "price": product.price,
        "feature_analysis": product.feature_analysis,
        "main_images": product.main_images,
        "detail_images": product.detail_images,
        "created_at": product.created_at,
        "updated_at": product.updated_at,
        "owner_id": product.owner_id,
        "owner_username": owner_username,
        "notes_count": notes_count or 0
    }


@router.get("/stats")
async def get_products_stats(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取商品统计信息
    """
    # 构建基础查询
    base_query = select(Product)
    
    # 应用用户权限过滤
    if current_user.role != UserRole.ADMIN:
        base_query = base_query.where(Product.owner_id == current_user.id)
    
    # 统计总数 - 使用简单查询
    total_query = select(func.count(Product.id))
    
    # 应用用户权限过滤
    if current_user.role != UserRole.ADMIN:
        total_query = total_query.where(Product.owner_id == current_user.id)
    
    total_result = await db.execute(total_query)
    total_count = total_result.scalar() or 0
    
    # 统计有笔记的商品数量 - 使用简单查询
    products_with_notes_query = select(func.count(func.distinct(Product.id))).join(
        Note, and_(Product.id == Note.product_id, Note.deleted_at.is_(None))
    )
    
    # 应用用户权限过滤
    if current_user.role != UserRole.ADMIN:
        products_with_notes_query = products_with_notes_query.where(Product.owner_id == current_user.id)
    
    products_with_notes_result = await db.execute(products_with_notes_query)
    products_with_notes_count = products_with_notes_result.scalar() or 0
    
    # 注意：图片数量统计已简化，避免加载大量数据到内存
    # 如果需要精确的图片统计，建议在数据库层面添加计算字段或使用缓存
    total_images = total_count * 2  # 估算值：假设每个商品平均有2张图片
    
    return {
        "total_products": total_count,
        "products_with_notes": products_with_notes_count,
        "products_without_notes": total_count - products_with_notes_count,
        "total_images": total_images,
        "coverage_rate": round((products_with_notes_count / total_count * 100) if total_count > 0 else 0, 2)
    }
