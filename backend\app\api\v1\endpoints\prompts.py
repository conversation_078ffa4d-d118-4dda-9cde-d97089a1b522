from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, desc, asc

from ....core.database import get_db
from ....core.deps import get_current_user, get_current_admin_user
from ....models.user import User, UserRole
from ....models.prompt import Prompt
from ....schemas.prompt import Prompt as PromptSchema, PromptCreate, PromptUpdate

router = APIRouter()


@router.get("/", response_model=List[PromptSchema])
async def read_prompts(
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    q: Optional[str] = Query(None, description="Search query"),
    sort_by: str = Query("created_at", description="Sort field"),
    order: str = Query("desc", regex="^(asc|desc)$"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)  # 只有管理员可以访问
):
    """
    Retrieve all prompts. Only accessible by administrators.
    """
    # Build base query - 管理员可以看到所有提示词
    query = select(Prompt)

    # Apply search filter
    if q:
        from sqlalchemy import or_
        search_filter = or_(
            Prompt.title.ilike(f"%{q}%"),
            Prompt.content.ilike(f"%{q}%")
        )
        query = query.where(search_filter)

    # Apply sorting
    if sort_by == "title":
        order_column = Prompt.title
    elif sort_by == "updated_at":
        order_column = Prompt.updated_at
    else:  # default to created_at
        order_column = Prompt.created_at

    if order == "asc":
        query = query.order_by(asc(order_column))
    else:
        query = query.order_by(desc(order_column))

    # Apply pagination
    query = query.offset(skip).limit(limit)
    
    result = await db.execute(query)
    prompts = result.scalars().all()
    
    return prompts


@router.post("/", response_model=PromptSchema)
async def create_prompt(
    prompt_in: PromptCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)  # 只有管理员可以创建
):
    """
    Create new prompt. Only accessible by administrators.
    """
    db_prompt = Prompt(
        title=prompt_in.title,
        content=prompt_in.content,
        created_by=current_user.id  # 记录创建者
    )

    db.add(db_prompt)
    await db.commit()
    await db.refresh(db_prompt)

    return db_prompt


@router.get("/{prompt_id}", response_model=PromptSchema)
async def read_prompt(
    prompt_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)  # 只有管理员可以访问
):
    """
    Get a specific prompt by ID. Only accessible by administrators.
    """
    result = await db.execute(select(Prompt).where(Prompt.id == prompt_id))
    prompt = result.scalar_one_or_none()

    if not prompt:
        raise HTTPException(status_code=404, detail="Prompt not found")

    return prompt


@router.put("/{prompt_id}", response_model=PromptSchema)
async def update_prompt(
    prompt_id: int,
    prompt_in: PromptUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)  # 只有管理员可以更新
):
    """
    Update a prompt. Only accessible by administrators.
    """
    result = await db.execute(select(Prompt).where(Prompt.id == prompt_id))
    prompt = result.scalar_one_or_none()

    if not prompt:
        raise HTTPException(status_code=404, detail="Prompt not found")

    # Update prompt fields
    update_data = prompt_in.dict(exclude_unset=True)

    for field, value in update_data.items():
        setattr(prompt, field, value)

    await db.commit()
    await db.refresh(prompt)

    return prompt


@router.delete("/{prompt_id}")
async def delete_prompt(
    prompt_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)  # 只有管理员可以删除
):
    """
    Delete a prompt. Only accessible by administrators.
    """
    result = await db.execute(select(Prompt).where(Prompt.id == prompt_id))
    prompt = result.scalar_one_or_none()

    if not prompt:
        raise HTTPException(status_code=404, detail="Prompt not found")

    await db.delete(prompt)
    await db.commit()
    return {"message": "Prompt deleted successfully"}


@router.get("/public/", response_model=List[PromptSchema])
async def read_public_prompts(
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    q: Optional[str] = Query(None, description="Search query"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)  # 所有用户都可以访问
):
    """
    Retrieve all prompts for public viewing. All authenticated users can access.
    """
    # Build base query - 所有用户都可以看到所有提示词（只读）
    query = select(Prompt)

    # Apply search filter
    if q:
        from sqlalchemy import or_
        search_filter = or_(
            Prompt.title.ilike(f"%{q}%"),
            Prompt.content.ilike(f"%{q}%")
        )
        query = query.where(search_filter)

    # Apply sorting and pagination
    query = query.order_by(desc(Prompt.created_at)).offset(skip).limit(limit)

    result = await db.execute(query)
    prompts = result.scalars().all()

    return prompts
