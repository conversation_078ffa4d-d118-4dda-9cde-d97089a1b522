import json
from typing import List, Optional
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, desc, asc

from ....core.database import get_db
from ....core.deps import get_current_admin_user
from ....models.user import User
from ....models.note import Note, NoteLog
from ....schemas.note import Note as NoteSchema

router = APIRouter()


def deserialize_files(files_json: str) -> List[str]:
    """Convert JSON string to list of file URLs."""
    if not files_json:
        return []
    try:
        file_paths = json.loads(files_json)
        # Convert relative paths to full URLs
        return [f"/uploads/{path}" if not path.startswith(('http://', 'https://', '/uploads/')) else path for path in file_paths]
    except (json.JSONDecodeError, TypeError):
        return []


async def create_note_log(note_id: int, message: str, db: AsyncSession):
    """Create a log entry for note operations."""
    log = NoteLog(note_id=note_id, log_message=message)
    db.add(log)


@router.get("/notes/", response_model=List[NoteSchema])
async def read_deleted_notes(
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    q: Optional[str] = Query(None, description="Search query"),
    sort_by: str = Query("deleted_at", description="Sort field"),
    order: str = Query("desc", regex="^(asc|desc)$"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    Retrieve deleted notes from recycle bin.
    Only admin can access this endpoint.
    """
    # Build query for deleted notes
    query = select(Note).where(Note.deleted_at.is_not(None))
    
    # Apply search filter
    if q:
        search_filter = or_(
            Note.title.ilike(f"%{q}%"),
            Note.body.ilike(f"%{q}%"),
            Note.tags.ilike(f"%{q}%")
        )
        query = query.where(search_filter)
    
    # Apply sorting
    sort_column = getattr(Note, sort_by, Note.deleted_at)
    if order == "desc":
        query = query.order_by(desc(sort_column))
    else:
        query = query.order_by(asc(sort_column))
    
    # Apply pagination
    query = query.offset(skip).limit(limit)
    
    result = await db.execute(query)
    notes = result.scalars().all()
    
    # Convert JSON strings to lists for response
    for note in notes:
        note.cover_images_candidate = deserialize_files(note.cover_images_candidate)
        note.note_images = deserialize_files(note.note_images)
        note.videos = deserialize_files(note.videos)
    
    return notes


@router.post("/notes/{note_id}/restore")
async def restore_note(
    note_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    Restore a deleted note from recycle bin.
    Only admin can access this endpoint.
    """
    # Find the deleted note
    result = await db.execute(
        select(Note).where(
            and_(Note.id == note_id, Note.deleted_at.is_not(None))
        )
    )
    note = result.scalar_one_or_none()
    
    if not note:
        raise HTTPException(status_code=404, detail="Deleted note not found")
    
    # Restore the note by clearing deleted_at
    note.deleted_at = None
    await db.commit()
    
    # Create log entry
    await create_note_log(note.id, f"笔记从回收站恢复", db)
    await db.commit()
    
    return {"message": "Note restored successfully"}


@router.delete("/notes/{note_id}")
async def permanently_delete_note(
    note_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)
):
    """
    Permanently delete a note from recycle bin.
    Only admin can access this endpoint.
    """
    # Find the deleted note
    result = await db.execute(
        select(Note).where(
            and_(Note.id == note_id, Note.deleted_at.is_not(None))
        )
    )
    note = result.scalar_one_or_none()
    
    if not note:
        raise HTTPException(status_code=404, detail="Deleted note not found")
    
    # Permanently delete the note
    await db.delete(note)
    await db.commit()
    
    return {"message": "Note permanently deleted"}
