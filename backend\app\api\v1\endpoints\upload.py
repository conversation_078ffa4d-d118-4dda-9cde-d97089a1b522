import os
import uuid
from datetime import datetime
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File
from fastapi.responses import FileResponse
import aiofiles

from ....core.config import settings
from ....core.deps import get_current_user
from ....models.user import User

router = APIRouter()


def get_file_extension(filename: str) -> str:
    """Get file extension from filename."""
    return filename.split('.')[-1].lower() if '.' in filename else ''


def is_allowed_image(filename: str) -> bool:
    """Check if file is an allowed image type."""
    ext = get_file_extension(filename)
    return ext in settings.allowed_image_extensions_list


def is_allowed_video(filename: str) -> bool:
    """Check if file is an allowed video type."""
    ext = get_file_extension(filename)
    return ext in settings.allowed_video_extensions_list


def generate_unique_filename(original_filename: str) -> str:
    """Generate a unique filename with timestamp while preserving the extension."""
    ext = get_file_extension(original_filename)

    # 生成时间戳
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # 生成短UUID（前8位）
    short_uuid = str(uuid.uuid4())[:8]

    # 组合文件名：timestamp_uuid.ext
    if ext:
        return f"{timestamp}_{short_uuid}.{ext}"
    else:
        return f"{timestamp}_{short_uuid}"


async def save_upload_file(upload_file: UploadFile, subfolder: str) -> str:
    """Save uploaded file and return the file path."""
    # Create subfolder if it doesn't exist
    upload_path = os.path.join(settings.upload_dir, subfolder)
    os.makedirs(upload_path, exist_ok=True)
    
    # Generate unique filename
    filename = generate_unique_filename(upload_file.filename)
    file_path = os.path.join(upload_path, filename)
    
    # Save file
    async with aiofiles.open(file_path, 'wb') as f:
        content = await upload_file.read()
        await f.write(content)
    
    # Return relative path for storage in database
    return f"{subfolder}/{filename}"


@router.post("/image")
async def upload_image(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user)
):
    """
    Upload an image file.
    """
    # Check file size
    if file.size > settings.max_file_size:
        raise HTTPException(
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            detail=f"File size exceeds maximum allowed size of {settings.max_file_size} bytes"
        )
    
    # Check file type
    if not is_allowed_image(file.filename):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"File type not allowed. Allowed types: {', '.join(settings.allowed_image_extensions_list)}"
        )
    
    try:
        # Save file
        file_path = await save_upload_file(file, "images")
        
        return {
            "message": "Image uploaded successfully",
            "filename": file.filename,
            "file_path": file_path,
            "url": f"/uploads/{file_path}"
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to upload file: {str(e)}"
        )


@router.post("/video")
async def upload_video(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user)
):
    """
    Upload a video file.
    """
    # Check file size
    if file.size > settings.max_file_size:
        raise HTTPException(
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            detail=f"File size exceeds maximum allowed size of {settings.max_file_size} bytes"
        )
    
    # Check file type
    if not is_allowed_video(file.filename):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"File type not allowed. Allowed types: {', '.join(settings.allowed_video_extensions_list)}"
        )
    
    try:
        # Save file
        file_path = await save_upload_file(file, "videos")
        
        return {
            "message": "Video uploaded successfully",
            "filename": file.filename,
            "file_path": file_path,
            "url": f"/uploads/{file_path}"
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to upload file: {str(e)}"
        )


@router.post("/images")
async def upload_multiple_images(
    files: List[UploadFile] = File(...),
    current_user: User = Depends(get_current_user)
):
    """
    Upload multiple image files.
    """
    if len(files) > 30:  # Limit based on PRD requirements
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Too many files. Maximum 30 images allowed."
        )

    uploaded_files = []
    errors = []

    for file in files:
        try:
            # Check file size
            if file.size > settings.max_file_size:
                errors.append(f"{file.filename}: File size exceeds maximum allowed size")
                continue

            # Check file type
            if not is_allowed_image(file.filename):
                errors.append(f"{file.filename}: File type not allowed")
                continue

            # Save file
            file_path = await save_upload_file(file, "images")
            uploaded_files.append({
                "filename": file.filename,
                "file_path": file_path,
                "url": f"/uploads/{file_path}"
            })

        except Exception as e:
            errors.append(f"{file.filename}: {str(e)}")

    return {
        "message": f"Uploaded {len(uploaded_files)} files successfully",
        "uploaded_files": uploaded_files,
        "errors": errors
    }


@router.get("/download/{file_path:path}")
async def download_file(
    file_path: str,
    current_user: User = Depends(get_current_user)
):
    """
    Download a file by its path.
    """
    full_path = os.path.join(settings.upload_dir, file_path)

    if not os.path.exists(full_path):
        raise HTTPException(status_code=404, detail="File not found")

    # Get original filename from the path
    filename = os.path.basename(file_path)

    return FileResponse(
        path=full_path,
        filename=filename,
        media_type='application/octet-stream'
    )
