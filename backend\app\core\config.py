from pydantic_settings import BaseSettings
from typing import List
import os


class Settings(BaseSettings):
    # Database
    database_url: str = "sqlite+aiosqlite:///./xhs_notes.db"

    # Security
    secret_key: str = "your-secret-key-here"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 43200  # 30 days * 24 hours * 60 minutes = 43200 minutes (1 month)

    # File Upload
    upload_dir: str = "./uploads"
    max_file_size: int = 524288000  # 500MB
    allowed_image_extensions: str = "jpg,jpeg,png,gif,webp"
    allowed_video_extensions: str = "mp4,mov,avi"

    # CORS
    allowed_origins: str = "http://localhost:3000,http://127.0.0.1:3000,http://localhost:8080,http://127.0.0.1:8080,http://ben:8080"

    # Development
    debug: bool = True
    log_level: str = "INFO"

    # Environment
    environment: str = "development"

    @property
    def allowed_image_extensions_list(self) -> List[str]:
        return [ext.strip() for ext in self.allowed_image_extensions.split(",")]

    @property
    def allowed_video_extensions_list(self) -> List[str]:
        return [ext.strip() for ext in self.allowed_video_extensions.split(",")]

    @property
    def allowed_origins_list(self) -> List[str]:
        return [origin.strip() for origin in self.allowed_origins.split(",")]

    class Config:
        # 动态确定环境文件
        env_file = os.getenv("ENV_FILE", ".env")
        case_sensitive = False


# Create settings instance
settings = Settings()

# Ensure upload directory exists
os.makedirs(settings.upload_dir, exist_ok=True)
