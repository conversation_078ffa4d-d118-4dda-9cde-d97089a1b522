from fastapi import Fast<PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import os
import logging
import time

from .core.config import settings
from .core.database import create_tables
from .api.v1.api import api_router

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="小红书笔记管理系统",
    description="一个为小红书内容创作者设计的后台内容管理系统",
    version="1.0.0",
    debug=settings.debug
)

# Add request logging middleware with detailed debug info
@app.middleware("http")
async def log_requests(request: Request, call_next):
    start_time = time.time()
    client_ip = request.client.host if request.client else "unknown"
    origin = request.headers.get("origin", "no-origin")
    user_agent = request.headers.get("user-agent", "no-user-agent")

    print(f"🔥 Request: {request.method} {request.url}")
    print(f"   Client IP: {client_ip}")
    print(f"   Origin: {origin}")
    print(f"   User-Agent: {user_agent[:50]}...")

    try:
        response = await call_next(request)
        process_time = time.time() - start_time
        print(f"✅ Response: {response.status_code} - {process_time:.4f}s")

        # Add CORS headers for debugging
        if origin and origin != "no-origin":
            print(f"   CORS Origin Check: {origin} in {settings.allowed_origins_list}")

        return response
    except Exception as e:
        process_time = time.time() - start_time
        print(f"❌ Error: {str(e)} - {process_time:.4f}s")
        print(f"   Exception type: {type(e).__name__}")
        raise

# Add CORS middleware with debug logging
print(f"🌐 CORS Configuration:")
print(f"   Allowed Origins: {settings.allowed_origins_list}")
print(f"   Allow Credentials: True")
print(f"   Allow Methods: *")
print(f"   Allow Headers: *")

app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins_list,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files for uploads
if os.path.exists(settings.upload_dir):
    app.mount("/uploads", StaticFiles(directory=settings.upload_dir), name="uploads")

# Include API router
app.include_router(api_router, prefix="/api/v1")


@app.on_event("startup")
async def startup_event():
    """Create database tables on startup."""
    await create_tables()


@app.get("/")
async def root():
    """Root endpoint."""
    return {"message": "小红书笔记管理系统 API", "version": "1.0.0"}


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy"}
