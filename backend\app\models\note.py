import enum
from sqlalchemy import Column, Integer, String, Text, ForeignKey, DateTime, Enum, func
from sqlalchemy.orm import relationship
from ..core.database import Base


class NoteStatus(str, enum.Enum):
    TO_BE_PUBLISHED = "待发布"
    PUBLISHED = "已发布"


class NoteUsageStatus(str, enum.Enum):
    UNUSED = "UNUSED"
    USED = "USED"


class Note(Base):
    __tablename__ = "notes"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(255), nullable=False, index=True)
    cover_images_candidate = Column(Text)  # JSON string of candidate cover image paths
    selected_cover_image = Column(String(255))  # Selected cover image path
    note_images = Column(Text)  # JSON string of note image paths
    videos = Column(Text)  # JSON string of video paths
    body = Column(Text)
    tags = Column(String(255))
    scheduled_at = Column(DateTime, nullable=True, index=True)
    status = Column(Enum(NoteStatus), default=NoteStatus.TO_BE_PUBLISHED)
    usage_status = Column(Enum(NoteUsageStatus), default=NoteUsageStatus.UNUSED, index=True)
    created_at = Column(DateTime, server_default=func.now(), index=True)
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    
    # Soft delete field
    deleted_at = Column(DateTime, nullable=True, index=True)
    
    # Foreign key
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False)
    
    # Relationships
    product = relationship("Product", back_populates="notes")
    logs = relationship("NoteLog", back_populates="note", cascade="all, delete-orphan")


class NoteLog(Base):
    __tablename__ = "note_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    log_message = Column(String(500), nullable=False)
    created_at = Column(DateTime, server_default=func.now())
    
    # Foreign key
    note_id = Column(Integer, ForeignKey("notes.id"), nullable=False)
    
    # Relationships
    note = relationship("Note", back_populates="logs")
