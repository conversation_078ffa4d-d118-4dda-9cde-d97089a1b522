from sqlalchemy import Column, Integer, String, Text, ForeignKey, DateTime, func
from sqlalchemy.orm import relationship
from ..core.database import Base


class Product(Base):
    __tablename__ = "products"
    
    id = Column(Integer, primary_key=True, index=True)
    short_name = Column(String(100), nullable=False, index=True)
    title = Column(String(255), index=True)
    main_images = Column(Text)  # JSON string of image paths
    detail_images = Column(Text)  # JSON string of image paths
    description = Column(Text)
    price = Column(String(50))
    feature_analysis = Column(Text)
    created_at = Column(DateTime, server_default=func.now(), index=True)
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    
    # Foreign key
    owner_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Relationships
    owner = relationship("User", back_populates="products")
    notes = relationship("Note", back_populates="product", cascade="all, delete-orphan")
