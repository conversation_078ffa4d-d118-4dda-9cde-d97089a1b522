import enum
from sqlalchemy import Column, Integer, String, Enum
from sqlalchemy.orm import relationship
from ..core.database import Base


class UserRole(str, enum.Enum):
    ADMIN = "admin"
    USER = "user"


class User(Base):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    role = Column(Enum(UserRole), default=UserRole.USER, nullable=False)
    
    # Relationships
    products = relationship("Product", back_populates="owner", cascade="all, delete-orphan")
    prompts = relationship("Prompt", back_populates="creator", cascade="all, delete-orphan")
