from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime
from ..models.note import NoteStatus, NoteUsageStatus


class NoteBase(BaseModel):
    title: str
    body: Optional[str] = None
    tags: Optional[str] = None
    scheduled_at: Optional[datetime] = None


class NoteCreate(NoteBase):
    cover_images_candidate: Optional[List[str]] = []
    selected_cover_image: Optional[str] = None
    note_images: Optional[List[str]] = []
    videos: Optional[List[str]] = []


class NoteUpdate(BaseModel):
    title: Optional[str] = None
    body: Optional[str] = None
    tags: Optional[str] = None
    scheduled_at: Optional[datetime] = None
    cover_images_candidate: Optional[List[str]] = None
    selected_cover_image: Optional[str] = None
    note_images: Optional[List[str]] = None
    videos: Optional[List[str]] = None


class Note(NoteBase):
    id: int
    cover_images_candidate: List[str] = []
    selected_cover_image: Optional[str] = None
    note_images: List[str] = []
    videos: List[str] = []
    status: NoteStatus
    usage_status: NoteUsageStatus
    created_at: datetime
    updated_at: datetime
    deleted_at: Optional[datetime] = None
    product_id: int
    
    class Config:
        from_attributes = True


class NoteStatusUpdate(BaseModel):
    status: NoteStatus


class NoteUsageStatusUpdate(BaseModel):
    usage_status: NoteUsageStatus


class NoteLogBase(BaseModel):
    log_message: str


class NoteLog(NoteLogBase):
    id: int
    created_at: datetime
    note_id: int

    class Config:
        from_attributes = True


class BatchCreateNotesRequest(BaseModel):
    titles: List[str]


class BatchCreateNotesResponse(BaseModel):
    success: bool
    created_count: int
    notes: List[Note]
    errors: List[str] = []
