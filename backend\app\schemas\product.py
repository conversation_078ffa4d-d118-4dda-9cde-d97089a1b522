from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime


class ProductBase(BaseModel):
    short_name: str
    title: Optional[str] = None
    description: Optional[str] = None
    price: Optional[str] = None
    feature_analysis: Optional[str] = None


class ProductCreate(ProductBase):
    main_images: Optional[List[str]] = []
    detail_images: Optional[List[str]] = []
    owner_id: Optional[int] = None  # 管理员可以指定商品归属用户


class ProductUpdate(BaseModel):
    short_name: Optional[str] = None
    title: Optional[str] = None
    description: Optional[str] = None
    price: Optional[str] = None
    feature_analysis: Optional[str] = None
    main_images: Optional[List[str]] = None
    detail_images: Optional[List[str]] = None
    owner_id: Optional[int] = None  # 管理员可以更改商品归属用户


class Product(ProductBase):
    id: int
    main_images: List[str] = []
    detail_images: List[str] = []
    created_at: datetime
    updated_at: datetime
    owner_id: int

    class Config:
        from_attributes = True


class ProductWithOwner(Product):
    owner_username: Optional[str] = None  # 商品归属用户的用户名
    notes_count: int = 0  # 商品的笔记总数量
    unused_notes_count: int = 0  # 未使用的笔记数量
    used_notes_count: int = 0  # 已使用的笔记数量
