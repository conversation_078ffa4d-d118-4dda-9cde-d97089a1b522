from pydantic import BaseModel
from typing import Optional
from datetime import datetime


class PromptBase(BaseModel):
    title: str
    content: str


class PromptCreate(PromptBase):
    pass


class PromptUpdate(BaseModel):
    title: Optional[str] = None
    content: Optional[str] = None


class Prompt(PromptBase):
    id: int
    created_by: Optional[int] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True
