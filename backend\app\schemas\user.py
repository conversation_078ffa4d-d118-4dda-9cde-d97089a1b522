from pydantic import BaseModel
from typing import Optional
from ..models.user import UserRole


class UserBase(BaseModel):
    username: str
    role: UserRole = UserRole.USER


class UserCreate(UserBase):
    password: str


class UserUpdate(BaseModel):
    username: Optional[str] = None
    password: Optional[str] = None
    role: Optional[UserRole] = None


class UserInDB(UserBase):
    id: int
    hashed_password: str
    
    class Config:
        from_attributes = True


class User(UserBase):
    id: int
    
    class Config:
        from_attributes = True
