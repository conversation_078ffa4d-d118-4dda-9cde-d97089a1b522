"""
文件处理工具模块
提供统一的文件序列化/反序列化功能
"""

import json
from typing import List


def serialize_files(files: List[str]) -> str:
    """将文件路径列表转换为JSON字符串"""
    return json.dumps(files) if files else "[]"


def deserialize_files(files_json: str) -> List[str]:
    """将JSON字符串转换为文件URL列表"""
    if not files_json:
        return []
    try:
        file_paths = json.loads(files_json)
        # 将相对路径转换为完整URL
        return [
            f"/uploads/{path}" if not path.startswith(('http://', 'https://', '/uploads/')) 
            else path 
            for path in file_paths
        ]
    except (json.JSONDecodeError, TypeError):
        return []


def serialize_images(images: List[str]) -> str:
    """将图片路径列表转换为JSON字符串（向后兼容性别名）"""
    return serialize_files(images)


def deserialize_images(images_json: str) -> List[str]:
    """将JSON字符串转换为图片路径列表（向后兼容性别名）"""
    return deserialize_files(images_json)


def validate_file_paths(file_paths: List[str]) -> List[str]:
    """验证并过滤有效的文件路径"""
    if not file_paths:
        return []
    
    valid_paths = []
    for path in file_paths:
        if isinstance(path, str) and path.strip():
            valid_paths.append(path.strip())
    
    return valid_paths