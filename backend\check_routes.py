#!/usr/bin/env python3
from app.main import app

print("🔍 Registered Routes:")
print("=" * 50)

for route in app.routes:
    if hasattr(route, 'path') and hasattr(route, 'methods'):
        methods = getattr(route, 'methods', set())
        print(f"{route.path} - {methods}")
    elif hasattr(route, 'path_regex'):
        print(f"{route.path_regex.pattern} - [Mount/Include]")

print("\n🔍 Looking for auth routes:")
auth_routes = [route for route in app.routes if hasattr(route, 'path') and 'auth' in route.path]
for route in auth_routes:
    methods = getattr(route, 'methods', set())
    print(f"  {route.path} - {methods}")
