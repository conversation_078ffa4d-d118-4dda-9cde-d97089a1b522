#!/usr/bin/env python3
import asyncio
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.core.database import get_db
from app.models.user import User
from sqlalchemy import select

async def check_users():
    async for db in get_db():
        result = await db.execute(select(User))
        users = result.scalars().all()
        print(f'Found {len(users)} users:')
        for user in users:
            print(f'  - {user.username} (role: {user.role})')
        break

if __name__ == "__main__":
    asyncio.run(check_users())
