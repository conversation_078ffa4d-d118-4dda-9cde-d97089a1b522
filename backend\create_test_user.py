#!/usr/bin/env python3
import asyncio
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.core.database import get_db
from app.models.user import User, UserRole
from app.core.security import get_password_hash
from sqlalchemy import select

async def create_test_user():
    async for db in get_db():
        # Check if test user already exists
        result = await db.execute(select(User).where(User.username == "test"))
        existing_user = result.scalar_one_or_none()
        
        if existing_user:
            print("Test user already exists")
            return
        
        # Create test user
        hashed_password = get_password_hash("test")
        test_user = User(
            username="test",
            hashed_password=hashed_password,
            role=UserRole.USER
        )
        
        db.add(test_user)
        await db.commit()
        print("Test user created successfully")
        break

if __name__ == "__main__":
    asyncio.run(create_test_user())
