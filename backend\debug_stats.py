#!/usr/bin/env python3
"""
Debug stats API to see the exact error.
"""
import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import traceback
from fastapi import HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from app.core.database import Async<PERSON>essionLocal
from app.api.v1.endpoints.notes import get_notes_stats
from app.models.user import User, UserRole

async def debug_stats():
    """Debug stats API to see exact error."""
    async with AsyncSessionLocal() as db:
        # Create a mock admin user
        class MockUser:
            def __init__(self):
                self.id = 1
                self.username = "admin"
                self.role = UserRole.ADMIN
        
        mock_user = MockUser()
        
        try:
            print("🔍 Testing stats API...")
            # Call the stats function directly
            result = await get_notes_stats(
                q=None,
                usage_status=None,
                product_id=None,
                db=db,
                current_user=mock_user
            )
            print("✅ Stats API test successful:")
            print(f"   Result: {result}")
        except HTTPException as e:
            print(f"❌ HTTPException: {e.status_code} - {e.detail}")
        except Exception as e:
            print(f"❌ Exception: {type(e).__name__}: {e}")
            print("Full traceback:")
            traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_stats())
