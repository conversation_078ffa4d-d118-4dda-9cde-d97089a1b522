#!/usr/bin/env python3
"""
配置诊断脚本
用于检查当前配置是否正确，特别是数据库配置
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
sys.path.insert(0, str(Path(__file__).parent))

def check_environment():
    """检查环境变量配置"""
    print("=== 环境变量检查 ===")
    
    # 检查 ENV_FILE 环境变量
    env_file = os.getenv("ENV_FILE")
    print(f"ENV_FILE 环境变量: {env_file}")
    
    if env_file:
        if os.path.exists(env_file):
            print(f"✅ 环境文件存在: {env_file}")
            
            # 读取并显示环境文件内容（隐藏敏感信息）
            print(f"\n--- {env_file} 内容 ---")
            with open(env_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if line and not line.startswith('#'):
                        # 隐藏密码等敏感信息
                        if any(keyword in line.upper() for keyword in ['PASSWORD', 'SECRET', 'KEY']):
                            key = line.split('=')[0]
                            print(f"{line_num:2d}: {key}=***隐藏***")
                        else:
                            print(f"{line_num:2d}: {line}")
        else:
            print(f"❌ 环境文件不存在: {env_file}")
    else:
        print("⚠️  ENV_FILE 环境变量未设置，将使用默认的 .env 文件")
        
        # 检查默认 .env 文件
        default_env = ".env"
        if os.path.exists(default_env):
            print(f"✅ 默认环境文件存在: {default_env}")
        else:
            print(f"❌ 默认环境文件不存在: {default_env}")

def check_config():
    """检查应用配置"""
    print("\n=== 应用配置检查 ===")
    
    try:
        from app.core.config import settings
        
        print(f"数据库URL: {settings.database_url}")
        print(f"环境: {settings.environment}")
        print(f"调试模式: {settings.debug}")
        print(f"日志级别: {settings.log_level}")
        print(f"上传目录: {settings.upload_dir}")
        print(f"允许的源: {settings.allowed_origins}")
        
        # 检查数据库类型
        if "sqlite" in settings.database_url.lower():
            print("⚠️  当前使用 SQLite 数据库")
            if settings.environment == "production":
                print("❌ 生产环境不应该使用 SQLite 数据库！")
            else:
                print("✅ 开发环境使用 SQLite 数据库正常")
        elif "mysql" in settings.database_url.lower():
            print("✅ 当前使用 MySQL 数据库")
        else:
            print(f"⚠️  未知的数据库类型: {settings.database_url}")
            
    except Exception as e:
        print(f"❌ 加载配置失败: {e}")

def check_database_connection():
    """检查数据库连接"""
    print("\n=== 数据库连接检查 ===")
    
    try:
        from app.core.config import settings
        
        if "mysql" in settings.database_url.lower():
            # 尝试连接 MySQL
            try:
                import pymysql
                
                # 解析数据库URL
                # mysql+pymysql://user:password@host:port/database
                url_parts = settings.database_url.replace("mysql+pymysql://", "").split("/")
                db_name = url_parts[-1]
                auth_host = url_parts[0]
                
                if "@" in auth_host:
                    auth, host_port = auth_host.split("@")
                    username, password = auth.split(":")
                    
                    if ":" in host_port:
                        host, port = host_port.split(":")
                        port = int(port)
                    else:
                        host = host_port
                        port = 3306
                    
                    print(f"尝试连接 MySQL: {username}@{host}:{port}/{db_name}")
                    
                    connection = pymysql.connect(
                        host=host,
                        port=port,
                        user=username,
                        password=password,
                        database=db_name,
                        charset='utf8mb4'
                    )
                    
                    with connection.cursor() as cursor:
                        cursor.execute("SELECT 1")
                        result = cursor.fetchone()
                        
                    connection.close()
                    print("✅ MySQL 数据库连接成功")
                    
                except ImportError:
                    print("❌ pymysql 模块未安装，请运行: pip install pymysql")
                except Exception as e:
                    print(f"❌ MySQL 数据库连接失败: {e}")
                    
        elif "sqlite" in settings.database_url.lower():
            # 检查 SQLite 文件
            db_path = settings.database_url.replace("sqlite+aiosqlite:///", "")
            if db_path.startswith("./"):
                db_path = db_path[2:]
                
            if os.path.exists(db_path):
                print(f"✅ SQLite 数据库文件存在: {db_path}")
            else:
                print(f"❌ SQLite 数据库文件不存在: {db_path}")
                
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")

def main():
    """主函数"""
    print("小红书笔记管理系统 - 配置诊断工具")
    print("=" * 50)
    
    check_environment()
    check_config()
    check_database_connection()
    
    print("\n=== 诊断完成 ===")
    print("\n建议:")
    print("1. 如果是生产环境部署，确保设置了 ENV_FILE 环境变量指向 .env.production")
    print("2. 生产环境应该使用 MySQL 数据库，不要使用 SQLite")
    print("3. 确保数据库连接信息正确且数据库服务正在运行")
    print("4. 检查文件权限，确保应用可以读取配置文件")

if __name__ == "__main__":
    main()
