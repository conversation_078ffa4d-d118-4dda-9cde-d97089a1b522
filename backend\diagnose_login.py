#!/usr/bin/env python3
import asyncio
import aiohttp
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from app.core.database import AsyncSessionLocal
from app.core.security import verify_password
from app.models.user import User

async def diagnose_login():
    print("🔍 Login Diagnosis Tool")
    print("=" * 50)
    
    # 1. Check database connection
    try:
        async with AsyncSessionLocal() as db:
            result = await db.execute(select(User))
            users = result.scalars().all()
            print(f"✅ Database connection OK. Found {len(users)} users")
            
            for user in users:
                print(f"   - {user.username} ({user.role})")
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return
    
    # 2. Test password verification
    username = "admin"
    password = "yn6666789"
    
    async with AsyncSessionLocal() as db:
        result = await db.execute(select(User).where(User.username == username))
        user = result.scalar_one_or_none()
        
        if user:
            if verify_password(password, user.hashed_password):
                print(f"✅ Password verification successful for '{username}'")
            else:
                print(f"❌ Password verification failed for '{username}'")
        else:
            print(f"❌ User '{username}' not found")
    
    # 3. Test API endpoint
    try:
        async with aiohttp.ClientSession() as session:
            data = aiohttp.FormData()
            data.add_field('username', username)
            data.add_field('password', password)
            
            async with session.post('http://localhost:9000/api/v1/auth/login', data=data) as resp:
                if resp.status == 200:
                    result = await resp.json()
                    print(f"✅ API login successful. Token: {result.get('access_token', 'N/A')[:20]}...")
                else:
                    text = await resp.text()
                    print(f"❌ API login failed. Status: {resp.status}, Response: {text[:100]}")
    except Exception as e:
        print(f"❌ API test failed: {e}")

if __name__ == "__main__":
    asyncio.run(diagnose_login())
