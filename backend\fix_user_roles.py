#!/usr/bin/env python3
"""
Fix user roles in database.
This script standardizes all user role values to match the current enum definition.
"""
import asyncio
import sqlite3
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, text

from app.core.database import AsyncSessionLocal
from app.models.user import User, UserRole


async def fix_user_roles():
    """Fix user roles to match current enum definition."""
    print("🔧 Starting user role fix...")
    
    # First, let's check current data using direct SQLite connection
    print("\n📋 Current user roles in database:")
    conn = sqlite3.connect('xhs_notes.db')
    cursor = conn.cursor()
    cursor.execute('SELECT id, username, role FROM users ORDER BY id')
    users = cursor.fetchall()
    
    print("-" * 60)
    for user in users:
        print(f"ID: {user[0]:<3} | Username: {user[1]:<15} | Role: {user[2]}")
    print("-" * 60)
    
    # Count role types
    role_counts = {}
    for user in users:
        role = user[2]
        role_counts[role] = role_counts.get(role, 0) + 1
    
    print(f"\n📊 Role distribution:")
    for role, count in role_counts.items():
        print(f"  {role}: {count} users")
    
    conn.close()
    
    # Now fix the roles using SQLAlchemy
    async with AsyncSessionLocal() as db:
        print(f"\n🔄 Fixing user roles...")
        
        # Update 'admin' to 'admin' (already correct)
        result = await db.execute(
            update(User)
            .where(User.role == 'admin')
            .values(role=UserRole.ADMIN)
        )
        admin_updated = result.rowcount
        
        # Update 'user' to 'user' (already correct)
        result = await db.execute(
            update(User)
            .where(User.role == 'user')
            .values(role=UserRole.USER)
        )
        user_updated = result.rowcount
        
        # Update 'ADMIN' to 'admin'
        result = await db.execute(
            update(User)
            .where(User.role == 'ADMIN')
            .values(role=UserRole.ADMIN)
        )
        admin_uppercase_updated = result.rowcount
        
        # Update 'USER' to 'user'
        result = await db.execute(
            update(User)
            .where(User.role == 'USER')
            .values(role=UserRole.USER)
        )
        user_uppercase_updated = result.rowcount
        
        await db.commit()
        
        print(f"✅ Updated roles:")
        print(f"  - 'admin' roles: {admin_updated} (already correct)")
        print(f"  - 'user' roles: {user_updated} (already correct)")
        print(f"  - 'ADMIN' → 'admin': {admin_uppercase_updated}")
        print(f"  - 'USER' → 'user': {user_uppercase_updated}")
    
    # Verify the fix
    print(f"\n✅ Verification - Updated user roles:")
    conn = sqlite3.connect('xhs_notes.db')
    cursor = conn.cursor()
    cursor.execute('SELECT id, username, role FROM users ORDER BY id')
    users = cursor.fetchall()
    
    print("-" * 60)
    for user in users:
        print(f"ID: {user[0]:<3} | Username: {user[1]:<15} | Role: {user[2]}")
    print("-" * 60)
    
    # Count role types after fix
    role_counts = {}
    for user in users:
        role = user[2]
        role_counts[role] = role_counts.get(role, 0) + 1
    
    print(f"\n📊 Final role distribution:")
    for role, count in role_counts.items():
        print(f"  {role}: {count} users")
    
    conn.close()
    
    print(f"\n🎉 User role fix completed!")
    print(f"All user roles are now standardized to match the enum definition:")
    print(f"  - ADMIN = 'admin'")
    print(f"  - USER = 'user'")


async def main():
    """Main function."""
    try:
        await fix_user_roles()
    except Exception as e:
        print(f"❌ Error fixing user roles: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
