# Gunicorn configuration file
import multiprocessing

# Server socket
bind = "127.0.0.1:9000"
backlog = 2048

# Worker processes
workers = multiprocessing.cpu_count() * 2 + 1
worker_class = "uvicorn.workers.UvicornWorker"
worker_connections = 1000
timeout = 300
keepalive = 2

# Restart workers after this many requests, to help prevent memory leaks
max_requests = 1000
max_requests_jitter = 100

# Logging
accesslog = "/home/<USER>/xhs_notes_manager/backend/logs/access.log"
errorlog = "/home/<USER>/xhs_notes_manager/backend/logs/error.log"
loglevel = "info"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'

# Process naming
proc_name = "xhs_notes_manager"

# Daemon mode
daemon = False
pidfile = "/home/<USER>/xhs_notes_manager/backend/gunicorn.pid"
user = None
group = None
tmp_upload_dir = None

# SSL (if needed)
# keyfile = "/path/to/keyfile"
# certfile = "/path/to/certfile"
