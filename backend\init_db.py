#!/usr/bin/env python3
"""
Database initialization script.
Creates tables and default admin user.
This script is specifically designed to run in the production environment and will
load the production configuration file directly.
"""
import os
import sys
import asyncio
from dotenv import load_dotenv

# ==================== 新增的关键代码 ====================
# 在导入任何其他应用代码之前，强制从 .env.production 加载环境变量。
# 这将确保所有后续导入的模块（如 config 和 database）都能获取到正确的配置。

# 获取当前脚本所在的目录
# os.path.dirname(__file__) -> /home/<USER>/xhs_notes_manager/backend
# os.path.abspath(...) -> 确保路径是绝对的
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
PRODUCTION_ENV_PATH = os.path.join(BASE_DIR, ".env.production")

if os.path.exists(PRODUCTION_ENV_PATH):
    print(f"✅ Found production environment file, loading settings from: {PRODUCTION_ENV_PATH}")
    # override=True 表示用文件中的值覆盖掉任何已存在的环境变量
    load_dotenv(dotenv_path=PRODUCTION_ENV_PATH, override=True)
else:
    print(f"❌ CRITICAL ERROR: Production environment file not found at '{PRODUCTION_ENV_PATH}'.")
    print("Cannot proceed with database initialization without production settings.")
    sys.exit(1) # 如果生产配置文件不存在，直接退出以防误操作
# =======================================================


# 现在，可以安全地导入其他应用模块了，它们会使用已加载的正确配置
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

# 捕获可能发生的导入错误，提供更清晰的指引
try:
    from app.core.database import engine, AsyncSessionLocal, Base
    from app.core.security import get_password_hash
    from app.models import User, Product, Note, NoteLog, Prompt
    from app.models.user import UserRole
except ImportError as e:
    print(f"❌ ImportError: {e}")
    print("Please ensure you are running this script from the 'backend' directory")
    print("and that your Python virtual environment is activated.")
    sys.exit(1)


async def create_tables():
    """Create all database tables."""
    print("🔄 Creating database tables in MySQL...")
    async with engine.begin() as conn:
        # 下面的命令现在会向 MySQL 发送 "CREATE TABLE" 语句
        await conn.run_sync(Base.metadata.create_all)
    print("✅ Database tables created successfully")


async def create_default_admin():
    """Create default admin user if not exists."""
    async with AsyncSessionLocal() as db:
        # 检查 admin 用户是否已存在
        result = await db.execute(
            select(User).where(User.username == "admin")
        )
        admin_user = result.scalar_one_or_none()
        
        if not admin_user:
            # 创建默认 admin 用户
            print("👤 Creating default admin user...")
            admin_user = User(
                username="admin",
                hashed_password=get_password_hash("admin123"),
                role=UserRole.ADMIN
            )
            db.add(admin_user)
            await db.commit()
            print("✅ Default admin user created (username: admin, password: admin123)")
        else:
            print("ℹ️  Admin user already exists. Skipping creation.")


async def create_default_prompts():
    """Create some default system prompts."""
    async with AsyncSessionLocal() as db:
        # 检查 prompts 是否已存在
        result = await db.execute(select(Prompt))
        existing_prompts = result.scalars().all()

        if not existing_prompts:
            print("🤖 Creating default system prompts...")
            default_prompts = [
                {
                    "title": "小红书爆款标题生成",
                    "content": "请根据以下商品信息生成5个小红书爆款标题：\n\n商品名称：{商品标题}\n商品特点：{商品特点分析}\n\n要求：\n1. 标题要有吸引力和话题性\n2. 包含相关热门关键词\n3. 长度控制在20字以内\n4. 符合小红书用户喜好"
                },
                {
                    "title": "小红书种草文案生成",
                    "content": "请根据以下信息写一篇小红书种草文案：\n\n商品名称：{商品标题}\n商品特点：{商品特点分析}\n商品价格：{商品定价}\n\n要求：\n1. 文案要真实自然，像真人分享\n2. 突出商品优势和使用体验\n3. 包含适当的表情符号\n4. 字数控制在200-300字\n5. 结尾要有互动引导"
                },
                {
                    "title": "商品卖点提炼",
                    "content": "请分析以下商品信息，提炼出3-5个核心卖点：\n\n商品名称：{商品标题}\n商品介绍：{商品介绍文字}\n\n要求：\n1. 卖点要简洁有力\n2. 突出与竞品的差异化\n3. 符合目标用户需求\n4. 每个卖点不超过15字"
                }
            ]
            
            for prompt_data in default_prompts:
                prompt = Prompt(
                    title=prompt_data["title"],
                    content=prompt_data["content"],
                    created_by=None # 系统创建，无关联用户
                )
                db.add(prompt)
            
            await db.commit()
            print(f"✅ Created {len(default_prompts)} default prompts")
        else:
            print(f"ℹ️  {len(existing_prompts)} prompts already exist. Skipping creation.")


async def main():
    """Main initialization function."""
    print("🚀 Initializing database for PRODUCTION environment...")
    
    try:
        await create_tables()
        await create_default_admin()
        await create_default_prompts()
        
        print("\n✅ Database initialization completed successfully!")
        print("\n📝 Next steps:")
        print("1. Proceed with migrating data from your SQLite (.db) file to MySQL.")
        print("2. Once migration is complete, start the application services.")
        
    except Exception as e:
        print(f"❌ An error occurred during initialization: {e}")
        # 打印更详细的错误追踪信息
        import traceback
        traceback.print_exc()
        
    finally:
        # 确保数据库连接被关闭
        await engine.dispose()
        print("\nℹ️  Database connection closed.")


if __name__ == "__main__":
    # 确保 python-dotenv 已安装
    try:
        from dotenv import load_dotenv
    except ImportError:
        print("❌ 'python-dotenv' is not installed. Please run 'pip install python-dotenv'.")
        sys.exit(1)

    # 运行主异步函数
    asyncio.run(main())