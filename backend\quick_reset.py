#!/usr/bin/env python3
"""
Quick reset admin password script.
"""
import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
from app.core.database import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from app.core.security import get_password_hash
from app.models.user import User, UserRole

async def reset_admin():
    """Reset admin password to admin123."""
    async with AsyncSessionLocal() as db:
        # Find admin user
        result = await db.execute(select(User).where(User.username == "admin"))
        admin_user = result.scalar_one_or_none()
        
        if admin_user:
            # Update password
            hashed_password = get_password_hash("admin123")
            await db.execute(
                update(User)
                .where(User.username == "admin")
                .values(hashed_password=hashed_password)
            )
            await db.commit()
            print("✅ Admin password reset to 'admin123'")
        else:
            # Create admin user
            hashed_password = get_password_hash("admin123")
            admin_user = User(
                username="admin",
                hashed_password=hashed_password,
                role=UserRole.ADMIN
            )
            db.add(admin_user)
            await db.commit()
            print("✅ Admin user created with password 'admin123'")

if __name__ == "__main__":
    asyncio.run(reset_admin())
