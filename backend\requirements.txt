# FastAPI and ASGI server
fastapi==0.104.1
uvicorn[standard]==0.24.0

# Database
sqlalchemy==2.0.23
alembic==1.12.1
asyncpg==0.29.0  # PostgreSQL async driver (if needed)
aiomysql==0.2.0  # MySQL async driver
aiosqlite==0.19.0  # SQLite async driver

# Authentication and Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Data validation and serialization
pydantic==2.5.0
pydantic-settings==2.1.0

# File handling
python-multipart==0.0.6
aiofiles==23.2.1

# HTTP client (for future AI integration)
httpx==0.25.2

# Development and testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0

# Environment variables
python-dotenv==1.0.0

# CORS
fastapi-cors==0.0.6

# Logging
loguru==0.7.2
