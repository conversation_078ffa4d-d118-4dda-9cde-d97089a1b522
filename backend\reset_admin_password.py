#!/usr/bin/env python3
"""
Reset admin password script.
This script allows you to reset the admin user password.
"""
import asyncio
import sys
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update

from app.core.database import AsyncSession<PERSON>ocal
from app.core.security import get_password_hash
from app.models.user import User, UserRole


async def list_users():
    """List all users in the database."""
    async with AsyncSessionLocal() as db:
        result = await db.execute(select(User))
        users = result.scalars().all()
        
        if not users:
            print("❌ No users found in database")
            return
        
        print("\n📋 Current users:")
        print("-" * 50)
        for user in users:
            print(f"ID: {user.id}, Username: {user.username}, Role: {user.role}")
        print("-" * 50)


async def reset_admin_password(new_password: str):
    """Reset admin user password."""
    async with AsyncSessionLocal() as db:
        # Find admin user
        result = await db.execute(
            select(User).where(User.username == "admin")
        )
        admin_user = result.scalar_one_or_none()
        
        if not admin_user:
            print("❌ Admin user not found. Creating new admin user...")
            # Create new admin user
            admin_user = User(
                username="admin",
                hashed_password=get_password_hash(new_password),
                role=UserRole.ADMIN
            )
            db.add(admin_user)
            await db.commit()
            print(f"✅ New admin user created with password: {new_password}")
        else:
            # Update existing admin password
            hashed_password = get_password_hash(new_password)
            await db.execute(
                update(User)
                .where(User.username == "admin")
                .values(hashed_password=hashed_password)
            )
            await db.commit()
            print(f"✅ Admin password updated to: {new_password}")


async def create_admin_user(username: str, password: str):
    """Create a new admin user."""
    async with AsyncSessionLocal() as db:
        # Check if user already exists
        result = await db.execute(
            select(User).where(User.username == username)
        )
        existing_user = result.scalar_one_or_none()

        if existing_user:
            print(f"❌ User '{username}' already exists")
            return

        # Create new admin user
        admin_user = User(
            username=username,
            hashed_password=get_password_hash(password),
            role=UserRole.ADMIN
        )
        db.add(admin_user)
        await db.commit()
        print(f"✅ Admin user '{username}' created with password: {password}")


async def set_user_password(username: str, new_password: str):
    """Set password for a specific user."""
    async with AsyncSessionLocal() as db:
        # Find user
        result = await db.execute(
            select(User).where(User.username == username)
        )
        user = result.scalar_one_or_none()

        if not user:
            print(f"❌ User '{username}' not found")
            return

        # Update user password
        hashed_password = get_password_hash(new_password)
        await db.execute(
            update(User)
            .where(User.username == username)
            .values(hashed_password=hashed_password)
        )
        await db.commit()
        print(f"✅ Password updated for user '{username}' (Role: {user.role})")


async def delete_user(username: str):
    """Delete a user account."""
    async with AsyncSessionLocal() as db:
        # Find user
        result = await db.execute(
            select(User).where(User.username == username)
        )
        user = result.scalar_one_or_none()

        if not user:
            print(f"❌ User '{username}' not found")
            return

        # Prevent deleting the last admin user
        if user.role == UserRole.ADMIN:
            admin_count_result = await db.execute(
                select(User).where(User.role == UserRole.ADMIN)
            )
            admin_users = admin_count_result.scalars().all()

            if len(admin_users) <= 1:
                print(f"❌ Cannot delete '{username}': This is the last admin user")
                print("   Create another admin user first before deleting this one")
                return

        # Delete user
        await db.delete(user)
        await db.commit()
        print(f"✅ User '{username}' (Role: {user.role}) has been deleted")


async def promote_user(username: str):
    """Promote a user to admin role."""
    async with AsyncSessionLocal() as db:
        # Find user
        result = await db.execute(
            select(User).where(User.username == username)
        )
        user = result.scalar_one_or_none()

        if not user:
            print(f"❌ User '{username}' not found")
            return

        if user.role == UserRole.ADMIN:
            print(f"ℹ️ User '{username}' is already an admin")
            return

        # Promote to admin
        await db.execute(
            update(User)
            .where(User.username == username)
            .values(role=UserRole.ADMIN)
        )
        await db.commit()
        print(f"✅ User '{username}' has been promoted to admin")


async def demote_user(username: str):
    """Demote an admin user to regular user."""
    async with AsyncSessionLocal() as db:
        # Find user
        result = await db.execute(
            select(User).where(User.username == username)
        )
        user = result.scalar_one_or_none()

        if not user:
            print(f"❌ User '{username}' not found")
            return

        if user.role != UserRole.ADMIN:
            print(f"ℹ️ User '{username}' is not an admin")
            return

        # Check if this is the last admin
        admin_count_result = await db.execute(
            select(User).where(User.role == UserRole.ADMIN)
        )
        admin_users = admin_count_result.scalars().all()

        if len(admin_users) <= 1:
            print(f"❌ Cannot demote '{username}': This is the last admin user")
            print("   Create another admin user first before demoting this one")
            return

        # Demote to regular user
        await db.execute(
            update(User)
            .where(User.username == username)
            .values(role=UserRole.USER)
        )
        await db.commit()
        print(f"✅ User '{username}' has been demoted to regular user")


async def main():
    """Main function."""
    if len(sys.argv) < 2:
        print("🔧 User Management Tool")
        print("=" * 80)
        print("\n📋 Available Commands:")
        print("  list                              # List all users")
        print("  reset <new_password>              # Reset admin password")
        print("  create <username> <password>      # Create new admin user")
        print("  setpass <username> <password>     # Set password for specific user")
        print("  delete <username>                 # Delete user account")
        print("  promote <username>                # Promote user to admin")
        print("  demote <username>                 # Demote admin to regular user")
        print("\n💡 Examples:")
        print("  python reset_admin_password.py list")
        print("  python reset_admin_password.py reset admin123")
        print("  python reset_admin_password.py create superadmin mypassword")
        print("  python reset_admin_password.py setpass john newpassword123")
        print("  python reset_admin_password.py delete olduser")
        print("  python reset_admin_password.py promote john")
        print("  python reset_admin_password.py demote superadmin")
        print("\n📝 Important Notes:")
        print("  - Default admin credentials: admin / admin123")
        print("  - Cannot delete or demote the last admin user")
        print("  - Make sure the backend server is stopped before running this script")
        print("  - All passwords should be strong and secure")
        print("=" * 80)
        return
    
    command = sys.argv[1].lower()

    try:
        if command == "list":
            await list_users()
        elif command == "reset":
            if len(sys.argv) < 3:
                print("❌ Please provide new password")
                print("Usage: python reset_admin_password.py reset <new_password>")
                return
            new_password = sys.argv[2]
            await reset_admin_password(new_password)
        elif command == "create":
            if len(sys.argv) < 4:
                print("❌ Please provide username and password")
                print("Usage: python reset_admin_password.py create <username> <password>")
                return
            username = sys.argv[2]
            password = sys.argv[3]
            await create_admin_user(username, password)
        elif command == "setpass":
            if len(sys.argv) < 4:
                print("❌ Please provide username and new password")
                print("Usage: python reset_admin_password.py setpass <username> <password>")
                return
            username = sys.argv[2]
            password = sys.argv[3]
            await set_user_password(username, password)
        elif command == "delete":
            if len(sys.argv) < 3:
                print("❌ Please provide username to delete")
                print("Usage: python reset_admin_password.py delete <username>")
                return
            username = sys.argv[2]
            # Confirmation prompt
            print(f"⚠️ Are you sure you want to delete user '{username}'? (y/N): ", end="")
            confirmation = input().strip().lower()
            if confirmation in ['y', 'yes']:
                await delete_user(username)
            else:
                print("❌ Operation cancelled")
        elif command == "promote":
            if len(sys.argv) < 3:
                print("❌ Please provide username to promote")
                print("Usage: python reset_admin_password.py promote <username>")
                return
            username = sys.argv[2]
            await promote_user(username)
        elif command == "demote":
            if len(sys.argv) < 3:
                print("❌ Please provide username to demote")
                print("Usage: python reset_admin_password.py demote <username>")
                return
            username = sys.argv[2]
            await demote_user(username)
        else:
            print(f"❌ Unknown command: {command}")
            print("Available commands: list, reset, create, setpass, delete, promote, demote")

    except Exception as e:
        print(f"❌ Error: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
