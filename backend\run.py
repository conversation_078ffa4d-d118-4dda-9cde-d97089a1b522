#!/usr/bin/env python3
"""
Development server runner with debug logging.
"""
import uvicorn
import logging
import sys
from app.main import app

# Configure detailed logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('backend_debug.log')
    ]
)

logger = logging.getLogger(__name__)

if __name__ == "__main__":
    logger.info("Starting XHS Notes Manager Backend Server")
    logger.info("Host: 0.0.0.0 (all interfaces)")
    logger.info("Port: 9000")
    logger.info("Debug mode: Enabled")

    try:
        uvicorn.run(
            "app.main:app",
            host="0.0.0.0",
            port=9000,
            reload=True,
            log_level="debug",
            access_log=True
        )
    except Exception as e:
        logger.error(f"Failed to start server: {e}")
        raise
