#!/usr/bin/env python3
"""
Test API endpoints directly.
"""
import asyncio
import sys
import httpx
from urllib.parse import urlencode


async def test_login_api(username: str, password: str):
    """Test login API endpoint."""
    async with httpx.AsyncClient() as client:
        # Test login
        form_data = {
            'username': username,
            'password': password
        }
        
        try:
            response = await client.post(
                'http://127.0.0.1:8000/api/v1/auth/token',
                data=form_data,
                headers={'Content-Type': 'application/x-www-form-urlencoded'}
            )
            
            print(f"Login Response Status: {response.status_code}")
            print(f"Login Response Body: {response.text}")
            
            if response.status_code == 200:
                token_data = response.json()
                access_token = token_data.get('access_token')
                print(f"✅ Login successful, token: {access_token[:20]}...")
                
                # Test /users/me endpoint
                headers = {'Authorization': f'Bearer {access_token}'}
                me_response = await client.get(
                    'http://127.0.0.1:8000/api/v1/users/me',
                    headers=headers
                )
                
                print(f"Users/me Response Status: {me_response.status_code}")
                print(f"Users/me Response Body: {me_response.text}")
                
                if me_response.status_code == 200:
                    user_data = me_response.json()
                    print(f"✅ User info retrieved: {user_data}")
                    return True
                else:
                    print(f"❌ Failed to get user info: {me_response.status_code}")
                    return False
            else:
                print(f"❌ Login failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Error: {e}")
            return False


async def main():
    """Main function."""
    if len(sys.argv) < 3:
        print("🔐 API Test Tool")
        print("\nUsage:")
        print("  python test_api.py <username> <password>")
        print("\nExample:")
        print("  python test_api.py admin yn666678A")
        return
    
    username = sys.argv[1]
    password = sys.argv[2]
    
    print(f"Testing login for user: {username}")
    await test_login_api(username, password)


if __name__ == "__main__":
    asyncio.run(main())
