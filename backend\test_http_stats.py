#!/usr/bin/env python3
"""
Test stats API via HTTP to see the exact error.
"""
import requests
import json

def test_stats_api():
    """Test stats API via HTTP."""
    # First login to get token
    login_data = {
        "username": "admin",
        "password": "yn6666"
    }
    
    try:
        print("🔍 Testing login...")
        login_response = requests.post(
            "http://127.0.0.1:8000/api/v1/auth/token",
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        print(f"Login status: {login_response.status_code}")
        
        if login_response.status_code == 200:
            token_data = login_response.json()
            token = token_data["access_token"]
            print(f"✅ Login successful, token: {token[:20]}...")
            
            # Test stats API
            print("\n🔍 Testing stats API...")
            headers = {"Authorization": f"Bearer {token}"}
            stats_response = requests.get(
                "http://127.0.0.1:8000/api/v1/notes/stats",
                headers=headers
            )
            print(f"Stats status: {stats_response.status_code}")
            print(f"Stats response: {stats_response.text}")
            
            if stats_response.status_code == 422:
                print("❌ 422 Error details:")
                try:
                    error_data = stats_response.json()
                    print(json.dumps(error_data, indent=2))
                except:
                    print("Could not parse error response as JSON")
        else:
            print(f"❌ Login failed: {login_response.text}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")

if __name__ == "__main__":
    test_stats_api()
