#!/usr/bin/env python3
"""
Test login functionality.
"""
import asyncio
import sys
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.core.database import AsyncSessionLocal
from app.core.security import verify_password
from app.models.user import User


async def test_login(username: str, password: str):
    """Test login with given credentials."""
    async with AsyncSessionLocal() as db:
        # Get user from database
        result = await db.execute(select(User).where(User.username == username))
        user = result.scalar_one_or_none()
        
        if not user:
            print(f"❌ User '{username}' not found")
            return False
        
        # Verify password
        if verify_password(password, user.hashed_password):
            print(f"✅ Login successful for user '{username}' (Role: {user.role})")
            return True
        else:
            print(f"❌ Invalid password for user '{username}'")
            return False


async def main():
    """Main function."""
    if len(sys.argv) < 3:
        print("🔐 Login Test Tool")
        print("\nUsage:")
        print("  python test_login.py <username> <password>")
        print("\nExample:")
        print("  python test_login.py admin admin1234")
        return
    
    username = sys.argv[1]
    password = sys.argv[2]
    
    try:
        await test_login(username, password)
    except Exception as e:
        print(f"❌ Error: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
