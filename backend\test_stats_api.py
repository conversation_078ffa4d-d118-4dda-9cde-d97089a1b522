#!/usr/bin/env python3
"""
Test stats API directly.
"""
import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.ext.asyncio import AsyncSession
from app.core.database import AsyncSessionLocal
from app.api.v1.endpoints.notes import get_notes_stats
from app.models.user import User, UserRole

async def test_stats():
    """Test stats API directly."""
    async with AsyncSessionLocal() as db:
        # Create a mock admin user
        class MockUser:
            def __init__(self):
                self.id = 1
                self.username = "admin"
                self.role = UserRole.ADMIN
        
        mock_user = MockUser()
        
        try:
            # Call the stats function directly
            result = await get_notes_stats(
                q=None,
                usage_status=None,
                product_id=None,
                db=db,
                current_user=mock_user
            )
            print("✅ Stats API test successful:")
            print(f"   Total notes: {result['total_notes']}")
            print(f"   Unused notes: {result['unused_notes']}")
            print(f"   Used notes: {result['used_notes']}")
            print(f"   Usage rate: {result['usage_rate']}%")
        except Exception as e:
            print(f"❌ Stats API test failed: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_stats())
