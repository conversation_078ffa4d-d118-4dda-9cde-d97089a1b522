#!/usr/bin/env python3
"""
Test user management API after role fix.
"""
import asyncio
import requests
import json


def test_login():
    """Test login and get token."""
    print("🔐 Testing login...")
    
    login_data = {
        "username": "adminyn",
        "password": "123456"
    }
    
    response = requests.post(
        "http://127.0.0.1:8000/api/v1/auth/token",
        data=login_data
    )
    
    if response.status_code == 200:
        token = response.json()["access_token"]
        print(f"✅ Login successful, token: {token[:20]}...")
        return token
    else:
        print(f"❌ Login failed: {response.status_code} - {response.text}")
        return None


def test_user_list(token):
    """Test user list API."""
    print("\n👥 Testing user list API...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    response = requests.get(
        "http://127.0.0.1:8000/api/v1/admin/users/?skip=0&limit=20&sort_by=id&order=desc",
        headers=headers
    )
    
    if response.status_code == 200:
        users = response.json()
        print(f"✅ User list API successful, found {len(users)} users:")
        for user in users:
            print(f"  - ID: {user['id']}, Username: {user['username']}, Role: {user['role']}")
        return True
    else:
        print(f"❌ User list API failed: {response.status_code} - {response.text}")
        return False


def test_user_creation(token):
    """Test user creation API."""
    print("\n➕ Testing user creation API...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    user_data = {
        "username": "test_user_fix",
        "password": "test123",
        "role": "user"
    }
    
    response = requests.post(
        "http://127.0.0.1:8000/api/v1/admin/users/",
        headers=headers,
        json=user_data
    )
    
    if response.status_code == 200:
        user = response.json()
        print(f"✅ User creation successful:")
        print(f"  - ID: {user['id']}, Username: {user['username']}, Role: {user['role']}")
        return user['id']
    else:
        print(f"❌ User creation failed: {response.status_code} - {response.text}")
        return None


def test_user_deletion(token, user_id):
    """Test user deletion API."""
    print(f"\n🗑️ Testing user deletion API for user {user_id}...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    response = requests.delete(
        f"http://127.0.0.1:8000/api/v1/admin/users/{user_id}",
        headers=headers
    )
    
    if response.status_code == 200:
        print(f"✅ User deletion successful")
        return True
    else:
        print(f"❌ User deletion failed: {response.status_code} - {response.text}")
        return False


def main():
    """Main test function."""
    print("🧪 Testing User Management API after role fix")
    print("=" * 50)
    
    # Test login
    token = test_login()
    if not token:
        print("❌ Cannot proceed without valid token")
        return
    
    # Test user list
    if not test_user_list(token):
        print("❌ User list test failed")
        return
    
    # Test user creation
    user_id = test_user_creation(token)
    if user_id:
        # Test user deletion to clean up
        test_user_deletion(token, user_id)
    
    print("\n🎉 All tests completed!")
    print("✅ User management API is working correctly after role fix")


if __name__ == "__main__":
    main()
