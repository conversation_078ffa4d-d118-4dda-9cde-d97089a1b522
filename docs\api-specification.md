# 小红书笔记管理系统 API 接口规范文档

## 概述

本文档描述了小红书笔记管理系统的完整API接口规范，包括认证、用户管理、商品管理、笔记管理等核心功能。

## 基础信息

- **API Base URL**: `/api/v1`
- **认证方式**: <PERSON><PERSON> (JWT)
- **数据格式**: JSON
- **字符编码**: UTF-8

## 认证系统

### 1. 用户登录
- **端点**: `POST /auth/token`
- **认证**: 无需认证
- **请求格式**: `application/x-www-form-urlencoded`
- **请求参数**:
  ```
  username: string (required) - 用户名
  password: string (required) - 密码
  ```
- **响应示例**:
  ```json
  {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "bearer"
  }
  ```

## 用户管理 (/users)

### 2. 获取用户列表
- **端点**: `GET /users/`
- **认证**: 需要管理员权限
- **查询参数**:
  - `skip`: int (默认0) - 跳过记录数
  - `limit`: int (默认50, 最大100) - 返回记录数
  - `q`: string (可选) - 搜索关键词

### 3. 创建用户
- **端点**: `POST /users/`
- **认证**: 需要管理员权限
- **请求体**:
  ```json
  {
    "username": "string",
    "password": "string",
    "role": "USER" | "ADMIN"
  }
  ```

## 商品管理 (/products)

### 4. 获取商品列表
- **端点**: `GET /products/`
- **认证**: 需要登录
- **查询参数**:
  - `skip`: int (默认0)
  - `limit`: int (默认50, 最大100)
  - `q`: string (可选) - 搜索关键词
  - `sort_by`: string (默认"created_at") - 排序字段
  - `order`: "asc" | "desc" (默认"desc") - 排序方向
- **响应示例**:
  ```json
  [
    {
      "id": 1,
      "short_name": "商品简称",
      "title": "商品标题",
      "description": "商品描述",
      "price": 99.99,
      "feature_analysis": "特色分析",
      "main_images": ["/uploads/image1.jpg"],
      "detail_images": ["/uploads/detail1.jpg"],
      "created_at": "2024-01-01T00:00:00",
      "updated_at": "2024-01-01T00:00:00",
      "owner_id": 1,
      "owner_username": "admin",
      "notes_count": 5
    }
  ]
  ```

### 5. 创建商品
- **端点**: `POST /products/`
- **认证**: 需要登录
- **请求体**:
  ```json
  {
    "short_name": "string",
    "title": "string",
    "description": "string",
    "price": 0.0,
    "feature_analysis": "string",
    "main_images": ["string"],
    "detail_images": ["string"],
    "owner_id": 1  // 仅管理员可设置
  }
  ```

### 6. 获取指定商品
- **端点**: `GET /products/{product_id}`
- **认证**: 需要登录
- **路径参数**: `product_id`: int - 商品ID

### 7. 更新商品
- **端点**: `PUT /products/{product_id}`
- **认证**: 需要登录（仅所有者或管理员）

### 8. 删除商品
- **端点**: `DELETE /products/{product_id}`
- **认证**: 需要登录（仅所有者或管理员）

## 笔记管理 (/notes)

### 9. 获取笔记列表
- **端点**: `GET /notes/`
- **认证**: 需要登录
- **查询参数**:
  - `skip`: int (默认0)
  - `limit`: int (默认20, 最大100)
  - `q`: string (可选) - 搜索关键词
  - `usage_status`: "UNUSED" | "USED" (可选) - 使用状态筛选
  - `product_id`: int (可选) - 商品ID筛选
  - `sort_by`: string (默认"created_at") - 排序字段
  - `order`: "asc" | "desc" (默认"desc") - 排序方向

### 10. 获取商品的笔记列表
- **端点**: `GET /products/{product_id}/notes/`
- **认证**: 需要登录
- **查询参数**: 同上

### 11. 创建笔记
- **端点**: `POST /products/{product_id}/notes/`
- **认证**: 需要登录
- **请求体**:
  ```json
  {
    "title": "string",
    "body": "string",
    "tags": "string",
    "scheduled_at": "2024-01-01T00:00:00",
    "cover_images_candidate": ["string"],
    "selected_cover_image": "string",
    "note_images": ["string"],
    "videos": ["string"]
  }
  ```

### 12. 批量创建笔记
- **端点**: `POST /products/{product_id}/notes/batch`
- **认证**: 需要登录
- **请求体**:
  ```json
  {
    "titles": ["标题1", "标题2", "标题3"]
  }
  ```

### 13. 获取指定笔记
- **端点**: `GET /notes/{note_id}`
- **认证**: 需要登录

### 14. 更新笔记
- **端点**: `PUT /notes/{note_id}`
- **认证**: 需要登录（仅所有者或管理员）

### 15. 删除笔记（软删除）
- **端点**: `DELETE /notes/{note_id}`
- **认证**: 需要登录（仅所有者或管理员）

### 16. 更新笔记状态
- **端点**: `PATCH /notes/{note_id}/status`
- **认证**: 需要登录
- **请求体**:
  ```json
  {
    "status": "待发布" | "已发布"
  }
  ```

### 17. 获取笔记日志
- **端点**: `GET /notes/{note_id}/logs`
- **认证**: 需要登录

### 18. 获取笔记统计 (已废弃)
- **端点**: `GET /notes/stats` (已废弃，请使用 `/notes-download/stats`)
- **状态**: 已移除，请使用笔记下载功能中的统计端点

## 笔记下载功能 (/notes-download)

### 19. 获取笔记预览列表
- **端点**: `GET /notes-download/preview`
- **认证**: 需要登录
- **查询参数**:
  - `skip`: int (默认0) - 跳过记录数
  - `limit`: int (默认20, 最大100) - 返回记录数
  - `q`: string (可选) - 搜索关键词
  - `product_search`: string (可选) - 商品搜索关键词
  - `usage_status`: "UNUSED" | "USED" (可选) - 使用状态筛选
  - `product_id`: int (可选) - 商品ID筛选
- **响应示例**:
  ```json
  [
    {
      "id": 1,
      "title": "笔记标题",
      "body": "笔记内容",
      "tags": "标签",
      "cover_images_candidate": ["/uploads/cover1.jpg"],
      "selected_cover_image": "/uploads/selected.jpg",
      "note_images": ["/uploads/note1.jpg"],
      "videos": ["/uploads/video1.mp4"],
      "scheduled_at": null,
      "status": "待发布",
      "usage_status": "UNUSED",
      "created_at": "2024-01-01T00:00:00",
      "updated_at": "2024-01-01T00:00:00",
      "product_id": 1,
      "product_title": "商品标题",
      "product_short_name": "商品简称",
      "product_name": "商品标题"
    }
  ]
  ```

### 20. 获取笔记完整内容
- **端点**: `GET /notes-download/{note_id}/content`
- **认证**: 需要登录

### 21. 更新笔记使用状态
- **端点**: `PUT /notes-download/{note_id}/usage-status`
- **认证**: 需要登录
- **请求体**:
  ```json
  {
    "usage_status": "UNUSED" | "USED"
  }
  ```

### 22. 获取笔记下载统计
- **端点**: `GET /notes-download/stats`
- **认证**: 需要登录
- **查询参数**:
  - `q`: string (可选) - 搜索关键词
  - `product_search`: string (可选) - 商品搜索关键词
  - `product_id`: int (可选) - 商品ID筛选
- **说明**: 统计数据始终显示全局信息，不受筛选条件影响
- **响应示例**:
  ```json
  {
    "total_notes": 100,
    "unused_notes": 60,
    "used_notes": 40,
    "usage_rate": 40.0
  }
  ```

## 商品下载功能 (/products-download)

### 23. 获取商品预览列表
- **端点**: `GET /products-download/preview`
- **认证**: 需要登录
- **查询参数**:
  - `skip`: int (默认0) - 跳过记录数
  - `limit`: int (默认20, 最大100) - 返回记录数
  - `q`: string (可选) - 搜索关键词

### 24. 获取商品统计
- **端点**: `GET /products-download/stats`
- **认证**: 需要登录
- **响应示例**:
  ```json
  {
    "total_products": 50,
    "products_with_notes": 30,
    "products_without_notes": 20,
    "total_images": 100,
    "coverage_rate": 60.0
  }
  ```

## 文件上传 (/upload)

### 25. 上传文件
- **端点**: `POST /upload/`
- **认证**: 需要登录
- **请求格式**: `multipart/form-data`
- **请求参数**:
  - `file`: File - 上传的文件

## 数据模型

### User（用户）
```json
{
  "id": 1,
  "username": "string",
  "role": "USER" | "ADMIN",
  "created_at": "2024-01-01T00:00:00",
  "updated_at": "2024-01-01T00:00:00"
}
```

### Product（商品）
```json
{
  "id": 1,
  "short_name": "string",
  "title": "string",
  "description": "string",
  "price": 0.0,
  "feature_analysis": "string",
  "main_images": ["string"],
  "detail_images": ["string"],
  "created_at": "2024-01-01T00:00:00",
  "updated_at": "2024-01-01T00:00:00",
  "owner_id": 1
}
```

### Note（笔记）
```json
{
  "id": 1,
  "title": "string",
  "body": "string",
  "tags": "string",
  "cover_images_candidate": ["string"],
  "selected_cover_image": "string",
  "note_images": ["string"],
  "videos": ["string"],
  "scheduled_at": "2024-01-01T00:00:00",
  "status": "待发布" | "已发布",
  "usage_status": "UNUSED" | "USED",
  "created_at": "2024-01-01T00:00:00",
  "updated_at": "2024-01-01T00:00:00",
  "deleted_at": "2024-01-01T00:00:00",
  "product_id": 1
}
```

## 错误处理

### HTTP状态码
- `200 OK` - 请求成功
- `201 Created` - 资源创建成功
- `400 Bad Request` - 请求参数错误
- `401 Unauthorized` - 认证失败
- `403 Forbidden` - 权限不足
- `404 Not Found` - 资源不存在
- `422 Unprocessable Entity` - 数据验证失败
- `500 Internal Server Error` - 服务器内部错误

### 错误响应格式
```json
{
  "detail": "错误描述信息"
}
```

## 权限控制

### 用户角色
- `USER` - 普通用户：只能管理自己的商品和笔记
- `ADMIN` - 管理员：可以管理所有用户的商品和笔记

### 访问控制规则
1. 所有API端点都需要认证（除了登录接口）
2. 普通用户只能访问自己创建的商品和笔记
3. 管理员可以访问所有商品和笔记
4. 用户管理功能仅管理员可访问

## 分页规范

所有列表类接口都支持分页：
- `skip`: 跳过的记录数（从0开始）
- `limit`: 返回的记录数（最大100）
- 响应中包含实际返回的记录数

## 搜索规范

支持模糊搜索的字段：
- 商品：`short_name`, `title`, `description`
- 笔记：`title`, `body`, `tags`
- 用户：`username`

## 文件处理

### 文件字段序列化
数据库中的文件列表字段（如`main_images`, `note_images`等）存储为JSON字符串，API响应时自动转换为字符串数组。

### 文件URL格式
- 相对路径自动转换为完整URL（添加`/uploads/`前缀）
- 已包含协议或`/uploads/`前缀的路径保持不变

## 软删除

笔记支持软删除机制：
- 删除操作设置`deleted_at`时间戳
- 所有查询操作自动排除已软删除的记录
- 管理员可通过回收站功能恢复删除的笔记