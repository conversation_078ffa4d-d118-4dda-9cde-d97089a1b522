# 文件上传大小配置快速参考

## 🎯 核心配置文件

### 1. 后端配置 (.env.production)
```bash
# 文件路径
/home/<USER>/xhs_notes_manager/backend/.env.production

# 关键配置项
MAX_FILE_SIZE=524288000  # 500MB in bytes
```

### 2. Nginx配置 (sites-available)
```bash
# 文件路径
/etc/nginx/sites-available/xhs-notes-manager

# 关键配置项
client_max_body_size 500M;
```

### 3. Gunicorn配置 (可选)
```bash
# 文件路径
/home/<USER>/xhs_notes_manager/backend/gunicorn.conf.py

# 关键配置项
timeout = 300  # 5分钟超时
```

## 📏 常用大小换算

| 大小 | 字节数 | Nginx格式 |
|------|--------|-----------|
| 10MB | 10,485,760 | 10M |
| 50MB | 52,428,800 | 50M |
| 100MB | 104,857,600 | 100M |
| 500MB | 524,288,000 | 500M |
| 1GB | 1,073,741,824 | 1G |

## ⚡ 快速修改命令

```bash
# 1. 修改后端配置
sudo nano /home/<USER>/xhs_notes_manager/backend/.env.production
# 修改: MAX_FILE_SIZE=524288000

# 2. 修改Nginx配置
sudo nano /etc/nginx/sites-available/xhs-notes-manager
# 修改: client_max_body_size 500M;

# 3. 验证Nginx配置
sudo nginx -t

# 4. 重启服务
sudo systemctl reload nginx
sudo systemctl restart xhs-backend
```

## 🔍 验证配置

```bash
# 检查服务状态
sudo systemctl status nginx
sudo systemctl status xhs-backend

# 测试API
curl http://127.0.0.1:8080/health

# 查看日志
tail -f /var/log/nginx/xhs-notes-manager.error.log
```

## 🚨 注意事项

1. **两个配置必须同时修改**：后端 + Nginx
2. **Nginx配置修改后必须reload**：`sudo systemctl reload nginx`
3. **后端配置修改后必须重启**：`sudo systemctl restart xhs-backend`
4. **验证语法**：修改Nginx配置后运行 `sudo nginx -t`
5. **监控磁盘空间**：大文件上传会占用更多存储空间

## 📋 故障排除

| 错误 | 原因 | 解决方案 |
|------|------|----------|
| 413 Request Entity Too Large | Nginx限制 | 增大 client_max_body_size |
| 504 Gateway Timeout | 上传超时 | 增大 gunicorn timeout |
| 500 Internal Server Error | 后端限制 | 检查 MAX_FILE_SIZE |

---
**快速参考完成** ✅
