# JWT Token过期时间修复指南

## 🔐 问题描述

**用户反馈**: 用户希望登录后1个月内都不需要重新登录，但现在过一会就需要重新登录。

**根本原因**: JWT Token过期时间设置过短（30分钟），导致用户需要频繁重新登录。

## 🎯 解决方案

将JWT Token过期时间从30分钟延长到43200分钟（30天），让用户1个月内无需重新登录。

### 计算公式
```
30天 × 24小时 × 60分钟 = 43200分钟
```

## 🔧 修复步骤

### 方法一：使用自动化脚本（推荐）

#### Linux/Mac环境：
```bash
# 进入项目根目录
cd /path/to/xhs_notes_manager

# 运行修复脚本
./scripts/fix-jwt-token-expiry.sh
```

#### Windows环境：
```cmd
# 进入项目根目录
cd C:\path\to\xhs_notes_manager

# 运行修复脚本
scripts\fix-jwt-token-expiry.bat
```

### 方法二：手动修改配置文件

#### 1. 检查当前配置
```bash
# 运行检查脚本
python scripts/check-jwt-config.py
```

#### 2. 修改配置文件

**开发环境** (`backend/.env`):
```env
# 修改这一行
ACCESS_TOKEN_EXPIRE_MINUTES=43200
```

**生产环境** (`backend/.env.production`):
```env
# 修改这一行
ACCESS_TOKEN_EXPIRE_MINUTES=43200
```

#### 3. 重启服务

**开发环境**:
```bash
# 重新启动开发服务器
cd backend
python main.py
```

**生产环境**:
```bash
# 重启后端服务
sudo systemctl restart xhs-backend

# 检查服务状态
sudo systemctl status xhs-backend
```

## ✅ 验证修复

### 1. 运行检查脚本
```bash
python scripts/check-jwt-config.py
```

期望输出：
```
🎉 所有配置都正确！
✅ JWT Token过期时间已设置为 30 天
✅ 用户可以1个月内无需重新登录
```

### 2. 测试用户体验
1. 清除浏览器缓存和localStorage
2. 重新登录系统
3. 验证1个月内无需重新登录

## 📋 配置文件对照表

| 环境 | 配置文件 | 配置项 | 修改前 | 修改后 |
|------|----------|--------|--------|--------|
| 开发 | `backend/.env` | `ACCESS_TOKEN_EXPIRE_MINUTES` | `30` | `43200` |
| 生产 | `backend/.env.production` | `ACCESS_TOKEN_EXPIRE_MINUTES` | `30` | `43200` |
| 代码 | `backend/app/core/config.py` | `access_token_expire_minutes` | `43200` | `43200` ✅ |

## 🔍 故障排除

### 问题1：配置修改后仍需频繁登录

**可能原因**:
- 服务未重启，配置未生效
- 浏览器缓存了旧的Token
- 配置文件路径错误

**解决方案**:
```bash
# 1. 确认配置文件正确
python scripts/check-jwt-config.py

# 2. 重启服务
sudo systemctl restart xhs-backend

# 3. 清除浏览器缓存
# 按F12 → Application → Storage → Clear storage
```

### 问题2：服务启动失败

**可能原因**:
- 配置文件语法错误
- 环境变量未正确加载

**解决方案**:
```bash
# 检查配置文件语法
cat backend/.env.production | grep ACCESS_TOKEN_EXPIRE_MINUTES

# 检查服务日志
sudo journalctl -u xhs-backend -f

# 手动测试配置加载
cd backend
python -c "from app.core.config import settings; print(f'Token过期时间: {settings.access_token_expire_minutes} 分钟')"
```

### 问题3：生产环境配置文件不存在

**解决方案**:
```bash
# 创建生产环境配置文件
cd backend
python fix_production_config.py
```

## ⚠️ 安全考虑

### 安全风险
- **Token泄露风险**: 延长过期时间意味着泄露的Token有效期更长
- **会话劫持**: 攻击者可能利用长期有效的Token

### 安全建议

1. **定期更换SECRET_KEY**:
   ```bash
   # 生成新的密钥
   openssl rand -hex 32
   
   # 更新配置文件中的SECRET_KEY
   ```

2. **监控异常登录**:
   - 记录用户登录IP和时间
   - 检测异常登录模式
   - 实现登录通知功能

3. **敏感操作重新验证**:
   - 删除数据时要求重新输入密码
   - 修改重要设置时进行二次确认
   - 考虑实现操作级别的权限控制

4. **考虑Refresh Token机制**:
   ```python
   # 未来可以考虑实现
   # - Access Token: 短期有效（1小时）
   # - Refresh Token: 长期有效（30天）
   # - 自动刷新机制
   ```

## 📊 用户体验改善

### 修复前
- ❌ 用户需要每30分钟重新登录
- ❌ 工作流程经常被打断
- ❌ 用户体验差，抱怨频繁

### 修复后
- ✅ 用户1个月内无需重新登录
- ✅ 工作流程连续性好
- ✅ 用户体验大幅提升
- ✅ 减少技术支持工作量

## 🚀 生产环境部署

### 快速部署命令

```bash
# 1. 拉取最新代码
cd /home/<USER>/xhs_notes_manager
git pull origin master

# 2. 检查并修复JWT配置
python scripts/check-jwt-config.py
./scripts/fix-jwt-token-expiry.sh

# 3. 重启服务
sudo systemctl restart xhs-backend
sudo systemctl status xhs-backend

# 4. 验证修复
python scripts/check-jwt-config.py
```

### 回滚方案

如果修复后出现问题，可以快速回滚：

```bash
# 1. 恢复配置文件
cp backend/.env.production.backup.* backend/.env.production

# 2. 重启服务
sudo systemctl restart xhs-backend

# 3. 验证回滚
python scripts/check-jwt-config.py
```

## 📞 技术支持

如果在修复过程中遇到问题：

1. **查看日志**: `sudo journalctl -u xhs-backend -f`
2. **检查配置**: `python scripts/check-jwt-config.py`
3. **验证服务**: `sudo systemctl status xhs-backend`
4. **测试API**: `curl http://127.0.0.1:8080/health`

---

**修复完成后，用户将享受1个月免登录的便捷体验！** 🎉
