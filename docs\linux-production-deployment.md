# Linux 生产环境部署指南

本指南详细介绍如何在Linux服务器上部署小红书笔记管理系统到生产环境。

## 📋 系统要求

### 硬件要求
- **CPU**: 2核心及以上
- **内存**: 4GB及以上（推荐8GB）
- **存储**: 20GB可用空间（推荐50GB）
- **网络**: 稳定的互联网连接

### 软件要求
- **操作系统**: Ubuntu 20.04+ / CentOS 8+ / Debian 11+
- **Python**: 3.11或更高版本
- **Node.js**: 18.0或更高版本
- **Git**: 最新版本
- **数据库**: MySQL 8.0+ 或 PostgreSQL 13+（可选，默认使用SQLite）

## 🛠️ 环境准备

### Ubuntu/Debian 系统

```bash
# 更新系统包
sudo apt update && sudo apt upgrade -y

# 安装基础工具
sudo apt install -y curl wget git build-essential

# 安装Python 3.11+
sudo apt install -y python3.11 python3.11-venv python3.11-dev python3-pip

# 安装Node.js 18+
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs

# 验证安装
python3.11 --version
node --version
npm --version
```

### CentOS/RHEL 系统

```bash
# 更新系统包
sudo dnf update -y

# 安装基础工具
sudo dnf install -y curl wget git gcc gcc-c++ make

# 安装Python 3.11+
sudo dnf install -y python3.11 python3.11-venv python3.11-devel python3-pip

# 安装Node.js 18+
curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
sudo dnf install -y nodejs

# 验证安装
python3.11 --version
node --version
npm --version
```

## 👤 创建部署用户

```bash
# 创建专用用户
sudo useradd -m -s /bin/bash xhsnotes
sudo usermod -aG sudo xhsnotes

# 切换到部署用户
sudo su - xhsnotes
```

## 📦 项目部署

### 1. 克隆项目

```bash
# 创建部署目录
mkdir -p ~/deploy
cd ~/deploy

# 克隆项目
git clone https://gitee.com/bin1874/xhs_notes_manager.git
cd xhs_notes_manager
```

### 2. 后端部署

#### 2.1 创建虚拟环境

```bash
cd backend

# 创建虚拟环境
python3.11 -m venv venv

# 激活虚拟环境
source venv/bin/activate

# 升级pip
pip install --upgrade pip

# 安装依赖
pip install -r requirements.txt

# 安装生产服务器
pip install gunicorn
```

#### 2.2 配置环境变量

创建 `backend/.env` 文件：

```bash
cat > .env << 'EOF'
# 数据库配置
DATABASE_URL=sqlite:///./xhs_notes.db
# 如果使用MySQL：DATABASE_URL=mysql+aiomysql://username:password@localhost/xhs_notes
# 如果使用PostgreSQL：DATABASE_URL=postgresql+asyncpg://username:password@localhost/xhs_notes

# JWT密钥（请更改为随机字符串）
SECRET_KEY=your-super-secret-key-change-this-in-production

# 服务器配置
HOST=0.0.0.0
PORT=8000

# 文件上传配置
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760  # 10MB

# 日志配置
LOG_LEVEL=INFO
EOF
```

#### 2.3 初始化数据库

```bash
# 确保在backend目录下且虚拟环境已激活
python init_db.py
```

### 3. 前端部署

```bash
cd ../frontend

# 安装依赖
npm install

# 构建生产版本
npm run build
```

## 🚀 系统服务配置

### 1. 创建Systemd服务

#### 后端服务配置

```bash
sudo tee /etc/systemd/system/xhsnotes-backend.service > /dev/null << 'EOF'
[Unit]
Description=XHS Notes Manager Backend
After=network.target

[Service]
Type=exec
User=xhsnotes
Group=xhsnotes
WorkingDirectory=/home/<USER>/deploy/xhs_notes_manager/backend
Environment=PATH=/home/<USER>/deploy/xhs_notes_manager/backend/venv/bin
ExecStart=/home/<USER>/deploy/xhs_notes_manager/backend/venv/bin/gunicorn -w 4 -k uvicorn.workers.UvicornWorker -b 0.0.0.0:8000 app.main:app
ExecReload=/bin/kill -s HUP $MAINPID
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
EOF
```

#### 启动并启用服务

```bash
# 重新加载systemd配置
sudo systemctl daemon-reload

# 启动服务
sudo systemctl start xhsnotes-backend

# 设置开机自启
sudo systemctl enable xhsnotes-backend

# 检查服务状态
sudo systemctl status xhsnotes-backend
```

## 🌐 Nginx配置

### 1. 安装Nginx

```bash
# Ubuntu/Debian
sudo apt install -y nginx

# CentOS/RHEL
sudo dnf install -y nginx
```

### 2. 配置Nginx

```bash
sudo tee /etc/nginx/sites-available/xhsnotes > /dev/null << 'EOF'
server {
    listen 80;
    server_name your-domain.com;  # 替换为您的域名
    
    # 前端静态文件
    location / {
        root /home/<USER>/deploy/xhs_notes_manager/frontend/dist;
        try_files $uri $uri/ /index.html;
        
        # 缓存静态资源
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # API代理
    location /api/ {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    
    # 文件上传
    location /uploads/ {
        alias /home/<USER>/deploy/xhs_notes_manager/backend/uploads/;
        
        # 安全配置
        location ~* \.(php|jsp|asp|sh|py)$ {
            deny all;
        }
    }
    
    # 安全配置
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    
    # 隐藏Nginx版本
    server_tokens off;
    
    # 文件上传大小限制
    client_max_body_size 10M;
}
EOF
```

### 3. 启用站点

```bash
# Ubuntu/Debian
sudo ln -s /etc/nginx/sites-available/xhsnotes /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default

# CentOS/RHEL
sudo ln -s /etc/nginx/sites-available/xhsnotes /etc/nginx/conf.d/xhsnotes.conf

# 测试配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx
sudo systemctl enable nginx
```

## 🔒 SSL证书配置

### 使用Let's Encrypt（免费）

```bash
# 安装Certbot
# Ubuntu/Debian
sudo apt install -y certbot python3-certbot-nginx

# CentOS/RHEL
sudo dnf install -y certbot python3-certbot-nginx

# 获取SSL证书
sudo certbot --nginx -d your-domain.com

# 设置自动续期
sudo crontab -e
# 添加以下行：
# 0 12 * * * /usr/bin/certbot renew --quiet
```

### 手动配置SSL

如果您有自己的SSL证书：

```bash
# 将证书文件放置到安全位置
sudo mkdir -p /etc/ssl/certs/xhsnotes
sudo cp your-cert.crt /etc/ssl/certs/xhsnotes/
sudo cp your-private.key /etc/ssl/private/xhsnotes/
sudo chmod 600 /etc/ssl/private/xhsnotes/your-private.key
```

更新Nginx配置添加SSL：

```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /etc/ssl/certs/xhsnotes/your-cert.crt;
    ssl_certificate_key /etc/ssl/private/xhsnotes/your-private.key;
    
    # SSL配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # 其他配置同上...
}

# HTTP重定向到HTTPS
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}
```

## 🔧 防火墙配置

### UFW (Ubuntu/Debian)

```bash
# 启用UFW
sudo ufw enable

# 允许SSH
sudo ufw allow ssh

# 允许HTTP和HTTPS
sudo ufw allow 80
sudo ufw allow 443

# 检查状态
sudo ufw status
```

### Firewalld (CentOS/RHEL)

```bash
# 启动firewalld
sudo systemctl start firewalld
sudo systemctl enable firewalld

# 允许HTTP和HTTPS
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https

# 重新加载配置
sudo firewall-cmd --reload

# 检查状态
sudo firewall-cmd --list-all
```

## 📊 监控和日志

### 1. 日志配置

```bash
# 创建日志目录
sudo mkdir -p /var/log/xhsnotes
sudo chown xhsnotes:xhsnotes /var/log/xhsnotes

# 配置logrotate
sudo tee /etc/logrotate.d/xhsnotes > /dev/null << 'EOF'
/var/log/xhsnotes/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 xhsnotes xhsnotes
    postrotate
        systemctl reload xhsnotes-backend
    endscript
}
EOF
```

### 2. 系统监控

安装监控工具：

```bash
# 安装htop和iotop
sudo apt install -y htop iotop  # Ubuntu/Debian
sudo dnf install -y htop iotop  # CentOS/RHEL

# 安装fail2ban防护
sudo apt install -y fail2ban  # Ubuntu/Debian
sudo dnf install -y fail2ban  # CentOS/RHEL

# 配置fail2ban
sudo tee /etc/fail2ban/jail.local > /dev/null << 'EOF'
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 5

[sshd]
enabled = true

[nginx-http-auth]
enabled = true
EOF

sudo systemctl restart fail2ban
```

### 3. 健康检查脚本

```bash
cat > ~/health-check.sh << 'EOF'
#!/bin/bash

# 检查后端服务
if ! curl -f http://localhost:8000/health > /dev/null 2>&1; then
    echo "Backend service is down! Restarting..."
    sudo systemctl restart xhsnotes-backend
    echo "Backend restarted at $(date)" >> ~/health-check.log
fi

# 检查Nginx
if ! sudo systemctl is-active --quiet nginx; then
    echo "Nginx is down! Restarting..."
    sudo systemctl restart nginx
    echo "Nginx restarted at $(date)" >> ~/health-check.log
fi

# 检查磁盘空间
DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "Disk usage is high: ${DISK_USAGE}%" >> ~/health-check.log
fi
EOF

chmod +x ~/health-check.sh

# 添加到crontab（每5分钟检查一次）
(crontab -l 2>/dev/null; echo "*/5 * * * * ~/health-check.sh") | crontab -
```

## 🔄 备份和恢复

### 1. 数据库备份脚本

```bash
cat > ~/backup.sh << 'EOF'
#!/bin/bash

BACKUP_DIR="/home/<USER>/backup"
DATE=$(date +%Y%m%d_%H%M%S)
PROJECT_DIR="/home/<USER>/deploy/xhs_notes_manager"

# 创建备份目录
mkdir -p $BACKUP_DIR/$DATE

# 备份数据库
cp $PROJECT_DIR/backend/xhs_notes.db $BACKUP_DIR/$DATE/

# 备份上传文件
cp -r $PROJECT_DIR/backend/uploads $BACKUP_DIR/$DATE/

# 备份配置文件
cp $PROJECT_DIR/backend/.env $BACKUP_DIR/$DATE/

# 压缩备份
cd $BACKUP_DIR
tar -czf xhsnotes_backup_$DATE.tar.gz $DATE/
rm -rf $DATE/

# 删除7天前的备份
find $BACKUP_DIR -name "xhsnotes_backup_*.tar.gz" -mtime +7 -delete

echo "Backup completed: xhsnotes_backup_$DATE.tar.gz"
EOF

chmod +x ~/backup.sh

# 添加到crontab（每天凌晨2点备份）
(crontab -l 2>/dev/null; echo "0 2 * * * ~/backup.sh") | crontab -
```

### 2. 恢复脚本

```bash
cat > ~/restore.sh << 'EOF'
#!/bin/bash

if [ $# -eq 0 ]; then
    echo "Usage: $0 <backup_file>"
    echo "Available backups:"
    ls -la ~/backup/xhsnotes_backup_*.tar.gz
    exit 1
fi

BACKUP_FILE=$1
PROJECT_DIR="/home/<USER>/deploy/xhs_notes_manager"
TEMP_DIR="/tmp/xhsnotes_restore"

# 停止服务
sudo systemctl stop xhsnotes-backend

# 解压备份
mkdir -p $TEMP_DIR
cd $TEMP_DIR
tar -xzf $BACKUP_FILE

# 恢复文件
BACKUP_DATE=$(ls)
cp $BACKUP_DATE/xhs_notes.db $PROJECT_DIR/backend/
cp -r $BACKUP_DATE/uploads/* $PROJECT_DIR/backend/uploads/
cp $BACKUP_DATE/.env $PROJECT_DIR/backend/

# 设置权限
chown -R xhsnotes:xhsnotes $PROJECT_DIR/backend/

# 启动服务
sudo systemctl start xhsnotes-backend

# 清理临时文件
rm -rf $TEMP_DIR

echo "Restore completed from $BACKUP_FILE"
EOF

chmod +x ~/restore.sh
```

## 🚨 故障排除

### 常见问题

1. **服务无法启动**
   ```bash
   # 查看服务状态
   sudo systemctl status xhsnotes-backend
   
   # 查看日志
   sudo journalctl -u xhsnotes-backend -f
   ```

2. **端口被占用**
   ```bash
   # 查看端口占用
   sudo netstat -tlnp | grep :8000
   sudo lsof -i :8000
   
   # 杀死进程
   sudo kill -9 <PID>
   ```

3. **权限问题**
   ```bash
   # 修复文件权限
   sudo chown -R xhsnotes:xhsnotes /home/<USER>/deploy/xhs_notes_manager/
   sudo chmod -R 755 /home/<USER>/deploy/xhs_notes_manager/
   ```

4. **Nginx配置错误**
   ```bash
   # 测试配置
   sudo nginx -t
   
   # 查看错误日志
   sudo tail -f /var/log/nginx/error.log
   ```

### 性能优化

1. **数据库优化**
   ```bash
   # 如果使用MySQL
   sudo mysql_secure_installation
   
   # 优化配置文件 /etc/mysql/mysql.conf.d/mysqld.cnf
   ```

2. **系统优化**
   ```bash
   # 增加文件描述符限制
   echo "* soft nofile 65536" | sudo tee -a /etc/security/limits.conf
   echo "* hard nofile 65536" | sudo tee -a /etc/security/limits.conf
   ```

## 📞 技术支持

如遇到部署问题，请：

1. 检查系统日志：`sudo journalctl -u xhsnotes-backend -f`
2. 检查Nginx日志：`sudo tail -f /var/log/nginx/error.log`
3. 查看故障排除部分
4. 提交Issue到项目仓库
5. 联系技术支持团队

---

**部署完成后，请访问您的域名验证系统是否正常运行！**
