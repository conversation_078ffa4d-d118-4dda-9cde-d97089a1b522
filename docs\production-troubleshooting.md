# 生产环境故障排除指南

本文档记录生产环境部署和更新过程中遇到的常见问题及解决方案。

## 🔧 构建和编译问题

### TypeScript编译错误：Cannot find namespace 'NodeJS'

**问题描述：**
```bash
src/views/ProductListView.vue:228:20 - error TS2503: Cannot find namespace 'NodeJS'.
228 let debounceTimer: NodeJS.Timeout | null = null
                       ~~~~~~
Found 1 error.
ERROR: "type-check" exited with 2.
Frontend build failed!
```

**原因分析：**
- 生产环境的TypeScript配置可能不包含Node.js类型定义
- `NodeJS.Timeout` 类型在某些环境中不可用
- 跨平台兼容性问题

**解决方案：**
```typescript
// ❌ 错误写法
let debounceTimer: NodeJS.Timeout | null = null

// ✅ 正确写法
let debounceTimer: number | null = null
```

**修复步骤：**
1. 找到使用 `NodeJS.Timeout` 的文件
2. 替换为 `number` 类型
3. 重新构建前端

```bash
# 搜索相关文件
grep -r "NodeJS.Timeout" frontend/src/

# 修复后重新构建
cd frontend
npm run build
```

**预防措施：**
- 在开发环境中使用与生产环境相同的TypeScript配置
- 避免使用平台特定的类型定义
- 使用通用的JavaScript类型

## 🚀 服务启动问题

### 后端服务启动失败

**常见错误：**
```bash
# 端口被占用
Error: [Errno 98] Address already in use

# 数据库连接失败
sqlalchemy.exc.OperationalError: (pymysql.err.OperationalError)

# 环境变量未加载
KeyError: 'SECRET_KEY'
```

**解决步骤：**

1. **检查端口占用：**
```bash
# 查看端口使用情况
sudo netstat -tlnp | grep :9000
sudo lsof -i :9000

# 杀死占用进程
sudo kill -9 <PID>
```

2. **检查数据库连接：**
```bash
# 测试数据库连接
mysql -u xhsnote_user -p'dhwdw6789jhgffHGF' xhsnote -e "SELECT 1;"

# 检查MySQL服务状态
sudo systemctl status mysql
```

3. **检查环境变量：**
```bash
# 验证环境配置文件
cat /home/<USER>/xhs_notes_manager/backend/.env.production

# 确保文件权限正确
chmod 600 /home/<USER>/xhs_notes_manager/backend/.env.production
```

### Nginx配置问题

**常见错误：**
```bash
# 配置语法错误
nginx: [emerg] unexpected "}" in /etc/nginx/sites-available/xhs-notes-manager:45

# 上游服务器连接失败
connect() failed (111: Connection refused) while connecting to upstream
```

**解决步骤：**

1. **验证配置语法：**
```bash
sudo nginx -t
```

2. **检查上游服务：**
```bash
# 测试后端服务
curl http://127.0.0.1:9000/health

# 检查服务状态
sudo systemctl status xhs-backend
```

3. **查看错误日志：**
```bash
sudo tail -f /var/log/nginx/xhs-notes-manager.error.log
```

## 📁 文件权限问题

### 上传目录权限不足

**问题表现：**
- 文件上传失败
- 500 Internal Server Error
- 权限拒绝错误

**解决方案：**
```bash
# 设置正确的目录权限
sudo chown -R ubuntu:ubuntu /home/<USER>/xhs_notes_manager
sudo chmod -R 755 /home/<USER>/xhs_notes_manager

# 特别设置上传目录权限
sudo chmod -R 777 /home/<USER>/xhs_notes_manager/backend/uploads
```

## 🔄 更新部署问题

### Git拉取失败

**常见问题：**
```bash
# 本地修改冲突
error: Your local changes to the following files would be overwritten by merge

# 权限问题
Permission denied (publickey)
```

**解决方案：**
```bash
# 备份本地修改
git stash

# 强制拉取最新代码
git fetch origin
git reset --hard origin/master

# 如果需要恢复本地修改
git stash pop
```

### 前端构建缓存问题

**问题表现：**
- 构建后仍显示旧版本
- 浏览器缓存问题

**解决方案：**
```bash
# 清理构建缓存
cd frontend
rm -rf node_modules/.cache
rm -rf dist

# 重新安装依赖
npm ci

# 重新构建
npm run build

# 强制刷新浏览器缓存
# Ctrl + Shift + R
```

## 📊 性能问题

### 大文件上传超时

**问题表现：**
- 504 Gateway Timeout
- 413 Request Entity Too Large

**解决方案：**

1. **调整Nginx配置：**
```nginx
client_max_body_size 500M;
proxy_read_timeout 300;
proxy_connect_timeout 300;
proxy_send_timeout 300;
```

2. **调整Gunicorn配置：**
```python
timeout = 300
keepalive = 5
```

3. **重启服务：**
```bash
sudo systemctl reload nginx
sudo systemctl restart xhs-backend
```

## 🔍 日志分析

### 关键日志位置

```bash
# 后端应用日志
/home/<USER>/xhs_notes_manager/backend/logs/error.log
/home/<USER>/xhs_notes_manager/backend/logs/access.log

# Nginx日志
/var/log/nginx/xhs-notes-manager.error.log
/var/log/nginx/xhs-notes-manager.access.log

# 系统服务日志
sudo journalctl -u xhs-backend -f
sudo journalctl -u nginx -f
```

### 日志分析技巧

```bash
# 查看最近的错误
tail -100 /var/log/nginx/xhs-notes-manager.error.log

# 实时监控日志
tail -f /home/<USER>/xhs_notes_manager/backend/logs/error.log

# 搜索特定错误
grep -i "error" /var/log/nginx/xhs-notes-manager.error.log | tail -20
```

## 🚨 紧急恢复

### 快速回滚

```bash
# 停止服务
sudo systemctl stop xhs-backend

# 回滚代码
git reset --hard HEAD~1

# 恢复配置（从备份）
sudo cp /home/<USER>/backups/latest_config.tar.gz /tmp/
cd /tmp && tar -xzf latest_config.tar.gz

# 重新构建前端
cd /home/<USER>/xhs_notes_manager/frontend
npm run build

# 重启服务
sudo systemctl start xhs-backend
sudo systemctl reload nginx
```

### 数据库恢复

```bash
# 从备份恢复数据库
mysql -u xhsnote_user -p'dhwdw6789jhgffHGF' xhsnote < /home/<USER>/backups/backup_database.sql

# 恢复上传文件
tar -xzf /home/<USER>/backups/backup_uploads.tar.gz -C /home/<USER>/xhs_notes_manager/backend/
```

## ✅ 健康检查清单

部署或更新后的验证步骤：

- [ ] 服务状态正常：`sudo systemctl status xhs-backend nginx`
- [ ] 端口监听正常：`sudo netstat -tlnp | grep -E "(8080|9000)"`
- [ ] 健康检查通过：`curl http://127.0.0.1:8080/health`
- [ ] 前端页面加载：`curl -I http://127.0.0.1:8080/`
- [ ] API文档可访问：`curl -I http://127.0.0.1:8080/docs`
- [ ] 登录功能正常：浏览器测试
- [ ] 文件上传功能：上传测试文件
- [ ] 数据库连接正常：`mysql -u xhsnote_user -p'dhwdw6789jhgffHGF' xhsnote -e "SELECT COUNT(*) FROM users;"`

---

**记住：** 遇到问题时，先查看日志，再根据错误信息对症下药。保持冷静，按步骤排查。
