# 生产环境代码更新指南

本文档说明如何在已部署的生产环境中更新代码，特别是涉及文件上传大小限制的配置修改。

## 📋 更新概述

**适用场景**: 已按照 `ubuntu24-nginx-mysql-deployment.md` 部署的生产环境
**更新内容**: 商品管理页面UI优化 + 文件上传大小配置调整
**影响范围**: 前端界面、后端配置、Nginx配置

## 🔄 代码更新步骤

### 步骤1：备份当前环境

```bash
# 进入项目目录
cd /home/<USER>/xhs_notes_manager

# 执行备份脚本
./scripts/backup.sh

# 验证备份
ls -la /home/<USER>/backups/
```

### 步骤2：拉取最新代码

```bash
# 停止服务
sudo systemctl stop xhs-backend

# 拉取最新代码
git pull origin master

# 检查更新内容
git log --oneline -5
```

### 步骤3：更新后端依赖（如有需要）

```bash
# 激活虚拟环境
cd /home/<USER>/xhs_notes_manager/backend
source venv/bin/activate

# 更新依赖（如果requirements.txt有变化）
pip install -r requirements.txt
```

### 步骤4：重新构建前端

```bash
# 进入前端目录
cd /home/<USER>/xhs_notes_manager/frontend

# 安装新依赖（如果package.json有变化）
npm install

# 重新构建生产版本
npm run build
```

**⚠️ 常见构建问题：**

如果遇到TypeScript编译错误：
```bash
error TS2503: Cannot find namespace 'NodeJS'
```

这是已知问题，已在最新代码中修复。如果仍遇到此问题：
1. 确保已拉取最新代码：`git pull origin master`
2. 清理缓存：`rm -rf node_modules/.cache`
3. 重新构建：`npm run build`

详细故障排除请参考：[生产环境故障排除指南](production-troubleshooting.md)

## ⚙️ 文件上传大小配置修改

### 需要修改的配置文件

#### 1. 后端环境配置文件

**文件路径**: `/home/<USER>/xhs_notes_manager/backend/.env.production`

```bash
# 编辑环境配置文件
sudo nano /home/<USER>/xhs_notes_manager/backend/.env.production
```

**需要修改的配置项**:
```env
# 文件上传大小限制 (500MB = 524288000 bytes)
MAX_FILE_SIZE=524288000

# 如果需要调整其他大小，计算公式：
# 10MB = 10485760
# 50MB = 52428800
# 100MB = 104857600
# 500MB = 524288000
# 1GB = 1073741824
```

#### 2. Nginx配置文件

**文件路径**: `/etc/nginx/sites-available/xhs-notes-manager`

```bash
# 编辑Nginx配置
sudo nano /etc/nginx/sites-available/xhs-notes-manager
```

**需要修改的配置项**:
```nginx
server {
    listen 8080;
    server_name ben localhost;

    # 客户端最大上传大小 - 修改这一行
    client_max_body_size 500M;  # 从原来的值改为500M
    
    # 其他配置保持不变...
}
```

**可选的上传大小值**:
- `10M` - 10MB
- `50M` - 50MB  
- `100M` - 100MB
- `500M` - 500MB
- `1G` - 1GB

#### 3. Gunicorn配置文件（可选）

**文件路径**: `/home/<USER>/xhs_notes_manager/backend/gunicorn.conf.py`

如果需要调整请求超时时间以适应大文件上传：

```bash
# 编辑Gunicorn配置
sudo nano /home/<USER>/xhs_notes_manager/backend/gunicorn.conf.py
```

**可能需要调整的配置**:
```python
# 超时时间（秒）- 大文件上传可能需要更长时间
timeout = 300  # 从30改为300（5分钟）

# 工作进程超时时间
keepalive = 5  # 从2改为5
```

## 🔧 配置验证和测试

### 验证配置文件语法

```bash
# 验证Nginx配置语法
sudo nginx -t

# 如果有错误，会显示具体的错误信息
```

### 重启服务

```bash
# 重新加载Nginx配置
sudo systemctl reload nginx

# 启动后端服务
sudo systemctl start xhs-backend

# 检查服务状态
sudo systemctl status xhs-backend
sudo systemctl status nginx
```

### 测试文件上传功能

```bash
# 检查后端健康状态
curl http://127.0.0.1:8080/health

# 检查API文档
curl -I http://127.0.0.1:8080/docs

# 通过浏览器测试
# 1. 访问 http://ben/products
# 2. 尝试上传一个大于原限制但小于新限制的文件
# 3. 验证上传是否成功
```

## 📊 监控和日志

### 查看相关日志

```bash
# 查看后端错误日志
./scripts/logs.sh backend

# 查看Nginx错误日志  
./scripts/logs.sh nginx

# 查看系统服务日志
./scripts/logs.sh systemd
```

### 监控磁盘空间

```bash
# 检查磁盘使用情况
df -h /home/<USER>

# 检查上传目录大小
du -sh /home/<USER>/xhs_notes_manager/backend/uploads/
```

## 🚨 故障排除

### 常见问题

1. **413 Request Entity Too Large**
   - 原因：Nginx的`client_max_body_size`设置过小
   - 解决：增大Nginx配置中的`client_max_body_size`值

2. **504 Gateway Timeout**
   - 原因：大文件上传超时
   - 解决：增加Gunicorn的`timeout`配置

3. **磁盘空间不足**
   - 原因：上传目录空间不够
   - 解决：清理旧文件或扩展磁盘空间

### 回滚方案

如果更新后出现问题，可以快速回滚：

```bash
# 停止服务
sudo systemctl stop xhs-backend

# 回滚到上一个commit
git reset --hard HEAD~1

# 恢复配置文件（从备份）
sudo cp /home/<USER>/backups/latest_config.tar.gz /tmp/
cd /tmp && tar -xzf latest_config.tar.gz

# 重新构建前端
cd /home/<USER>/xhs_notes_manager/frontend
npm run build

# 重启服务
sudo systemctl start xhs-backend
sudo systemctl reload nginx
```

## ✅ 更新完成检查清单

- [ ] 代码已成功拉取到最新版本
- [ ] 前端已重新构建（`npm run build`）
- [ ] 后端环境配置已更新（`.env.production`）
- [ ] Nginx配置已更新（`client_max_body_size`）
- [ ] Nginx配置语法验证通过（`nginx -t`）
- [ ] 所有服务已重启并运行正常
- [ ] 文件上传功能测试通过
- [ ] 商品管理页面筛选功能正常
- [ ] 日志中无错误信息
- [ ] 磁盘空间充足

## 🔍 配置文件对照表

### 文件上传大小配置对照

| 配置文件 | 配置项 | 当前值 | 说明 |
|---------|--------|--------|------|
| `.env.production` | `MAX_FILE_SIZE` | `524288000` | 后端文件大小限制(字节) |
| `nginx配置` | `client_max_body_size` | `500M` | Nginx上传大小限制 |
| `gunicorn.conf.py` | `timeout` | `300` | 请求超时时间(秒) |

### 字节换算参考

```
1MB = 1,048,576 bytes
10MB = 10,485,760 bytes
50MB = 52,428,800 bytes
100MB = 104,857,600 bytes
500MB = 524,288,000 bytes
1GB = 1,073,741,824 bytes
```

## 🔐 安全注意事项

### 文件上传安全

1. **文件类型限制**：
   ```env
   # 在.env.production中确认允许的文件类型
   ALLOWED_IMAGE_EXTENSIONS=jpg,jpeg,png,gif,webp
   ALLOWED_VIDEO_EXTENSIONS=mp4,mov,avi
   ```

2. **存储空间监控**：
   ```bash
   # 设置磁盘使用率告警（建议80%）
   df -h | awk '$5 > 80 {print $0}'
   ```

3. **定期清理**：
   ```bash
   # 添加到定时任务，清理30天前的临时文件
   find /home/<USER>/xhs_notes_manager/backend/uploads/temp -mtime +30 -delete
   ```

## 📈 性能优化建议

### 大文件上传优化

1. **启用Nginx缓存**：
   ```nginx
   # 在nginx配置中添加
   location /uploads/ {
       expires 1y;
       add_header Cache-Control "public, immutable";
   }
   ```

2. **调整工作进程数**：
   ```python
   # 在gunicorn.conf.py中根据服务器配置调整
   workers = min(multiprocessing.cpu_count() * 2 + 1, 8)
   ```

## 📞 技术支持

如果在更新过程中遇到问题：

1. 查看相关日志文件确定错误原因
2. 检查配置文件语法是否正确
3. 验证服务状态和端口占用情况
4. 必要时使用备份进行回滚

### 紧急联系方式

- **日志查看**: `./scripts/logs.sh [backend|nginx|systemd]`
- **服务状态**: `./scripts/status.sh`
- **快速重启**: `./scripts/restart.sh`

---

**更新完成！** 🎉

您的生产环境现在已经更新到最新版本，支持500MB的文件上传和优化的商品管理界面。
