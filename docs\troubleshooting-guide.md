# 故障排除指南

本文档记录了小红书笔记管理系统在部署和运行过程中遇到的常见问题及解决方案。

## 📋 目录

- [网络连接问题](#网络连接问题)
- [服务启动问题](#服务启动问题)
- [数据库连接问题](#数据库连接问题)
- [权限问题](#权限问题)
- [性能问题](#性能问题)

## 🌐 网络连接问题

### 问题1: 前端登录时出现 ERR_NETWORK 错误

**症状：**
- 前端页面正常加载
- 登录时出现 `Network Error` 和 `ERR_NETWORK`
- 控制台显示：`Failed to load resource: net::ERR_CONNECTION_REFUSED`
- 错误URL指向：`http://ben:9000/api/v1/auth/token`

**根本原因：**
前端生产环境配置错误，API请求直接指向了后端服务器地址（如 `ben:9000`），而客户端无法直接访问后端端口。

**解决方案：**

1. **检查前端环境配置**
   ```bash
   cat /home/<USER>/xhs_notes_manager/frontend/.env.production
   ```

2. **修正API基础URL配置**
   ```bash
   # 编辑 .env.production 文件
   # 将 VITE_API_BASE_URL=http://ben:9000 
   # 改为 VITE_API_BASE_URL=
   # 空值表示使用相对路径，通过Nginx代理访问
   ```

3. **重新构建前端**
   ```bash
   cd /home/<USER>/xhs_notes_manager/frontend
   npm run build
   ```

4. **重启Nginx**
   ```bash
   sudo systemctl reload nginx
   ```

**验证修复：**
```bash
# 测试API连接
curl http://127.0.0.1:8080/api/v1/auth/token -X POST \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=admin&password=your_password"
```

**预防措施：**
- 生产环境中，前端应始终通过Nginx代理访问API
- `VITE_API_BASE_URL` 在生产环境中应为空或相对路径
- 开发环境和生产环境的配置要分离

### 问题2: 代理配置端口不匹配

**症状：**
- 开发环境中前端无法连接后端
- Vite代理配置指向错误端口

**解决方案：**
1. 检查后端实际运行端口（生产环境通常是9000）
2. 确保Vite配置中的代理目标端口正确
3. 确保CORS配置包含前端端口

## 🔧 服务启动问题

### 问题1: 后端服务无法启动

**诊断命令：**
```bash
# 检查服务状态
sudo systemctl status xhs-backend

# 查看详细日志
sudo journalctl -u xhs-backend -f

# 检查端口占用
sudo netstat -tlnp | grep :9000
```

**常见解决方案：**
1. 检查Python虚拟环境是否正确激活
2. 检查数据库连接配置
3. 检查文件权限
4. 检查端口是否被占用

### 问题2: Nginx配置错误

**诊断命令：**
```bash
# 测试Nginx配置
sudo nginx -t

# 查看错误日志
sudo tail -f /var/log/nginx/xhs-notes-manager.error.log
```

## 🔍 调试工具和技巧

### 1. 网络连接调试

**使用Chrome开发者工具：**
1. 打开F12开发者工具
2. 查看Network标签页的请求详情
3. 查看Console标签页的错误信息

**使用curl测试：**
```bash
# 测试健康检查
curl -v http://127.0.0.1:8080/health

# 测试API端点
curl -v http://127.0.0.1:8080/api/v1/auth/token -X OPTIONS

# 测试登录
curl -X POST http://127.0.0.1:8080/api/v1/auth/token \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=admin&password=your_password"
```

### 2. 服务状态检查

**使用管理脚本：**
```bash
cd /home/<USER>/xhs_notes_manager

# 检查所有服务状态
./scripts/status.sh

# 查看日志
./scripts/logs.sh backend
./scripts/logs.sh nginx
./scripts/logs.sh systemd
```

### 3. 实时监控

**监控服务日志：**
```bash
# 后端服务日志
sudo journalctl -u xhs-backend -f

# Nginx访问日志
sudo tail -f /var/log/nginx/xhs-notes-manager.access.log

# Nginx错误日志
sudo tail -f /var/log/nginx/xhs-notes-manager.error.log
```

## 📝 问题报告模板

当遇到问题时，请收集以下信息：

### 环境信息
- 操作系统版本
- 服务器IP地址
- 访问方式（本地/局域网）

### 错误信息
- 具体错误消息
- 浏览器控制台日志
- 服务器日志

### 重现步骤
1. 具体操作步骤
2. 预期结果
3. 实际结果

### 系统状态
```bash
# 服务状态
./scripts/status.sh

# 端口监听
sudo netstat -tlnp | grep -E ":8080|:9000"

# 磁盘空间
df -h

# 内存使用
free -h
```

## 🔗 相关文档

- [Ubuntu 24 + Nginx + MySQL 部署指南](ubuntu24-nginx-mysql-deployment.md)
- [用户管理指南](user-management-guide.md)
- [Bug修复经验](bug-fix-experience.md)

---

**最后更新：** 2025-07-29
**维护者：** 开发团队
