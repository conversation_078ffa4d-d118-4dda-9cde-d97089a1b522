# 故障排除指南

本文档记录了小红书笔记管理系统常见问题的解决方案。

## 🔧 前端问题

### 复制功能失败

**问题描述**：
点击"复制标题"或"复制内容"按钮时出现错误：
```
TypeError: Cannot read properties of undefined (reading 'writeText')
```

**原因分析**：
- `navigator.clipboard` API 在某些浏览器环境下不可用
- HTTP环境下剪贴板API受限（需要HTTPS或localhost）
- 浏览器版本过旧不支持现代剪贴板API

**解决方案**：
1. **已修复**：系统已实现兼容性复制功能，包含：
   - 现代剪贴板API支持
   - 降级方案（document.execCommand）
   - 自动检测浏览器兼容性

2. **部署更新**：
   ```bash
   cd frontend
   npm run build
   sudo systemctl reload nginx
   ```

3. **清除浏览器缓存**：
   - 按 `Ctrl + Shift + R` 强制刷新
   - 或在开发者工具中选择"清空缓存并硬性重新加载"

**技术实现**：
- 创建了通用剪贴板工具 `src/utils/clipboard.ts`
- 支持现代API和传统方法的自动切换
- 完善的错误处理和用户提示

### 构建缓存问题

**问题描述**：
代码修改后重新构建，但浏览器仍显示旧版本错误。

**解决方案**：
1. **强制清除缓存**：
   ```bash
   # 浏览器快捷键
   Ctrl + Shift + R  # 或 Ctrl + F5
   ```

2. **检查文件更新**：
   ```bash
   ls -la frontend/dist/assets/NoteDetailView-*.js
   ```

3. **临时禁用Nginx缓存**（调试用）：
   ```nginx
   location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
       expires -1;
       add_header Cache-Control "no-cache, no-store, must-revalidate";
   }
   ```

## 🗄️ 后端问题

### 数据库连接失败

**问题描述**：
```
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) unable to open database file
```

**解决方案**：
1. 检查数据库文件权限：
   ```bash
   ls -la backend/xhs_notes.db
   chmod 664 backend/xhs_notes.db
   ```

2. 重新初始化数据库：
   ```bash
   cd backend
   python init_db.py
   ```

### 文件上传失败

**问题描述**：
上传文件时返回500错误。

**解决方案**：
1. 检查上传目录权限：
   ```bash
   mkdir -p backend/uploads
   chmod 755 backend/uploads
   ```

2. 检查磁盘空间：
   ```bash
   df -h
   ```

## 🌐 网络问题

### API请求失败

**问题描述**：
前端无法连接到后端API。

**解决方案**：
1. 检查后端服务状态：
   ```bash
   sudo systemctl status xhs-backend
   curl http://localhost:9000/health
   ```

2. 检查防火墙设置：
   ```bash
   sudo ufw status
   sudo ufw allow 9000
   ```

3. 检查Nginx代理配置：
   ```bash
   sudo nginx -t
   sudo systemctl reload nginx
   ```

## 🔄 部署问题

### 服务启动失败

**问题描述**：
systemd服务无法启动。

**解决方案**：
1. 查看服务日志：
   ```bash
   sudo journalctl -u xhs-backend -f
   ```

2. 检查配置文件：
   ```bash
   sudo systemctl daemon-reload
   sudo systemctl restart xhs-backend
   ```

### 权限问题

**问题描述**：
文件权限不足导致服务异常。

**解决方案**：
```bash
# 设置正确的文件权限
sudo chown -R www-data:www-data /home/<USER>/xhs_notes_manager
sudo chmod -R 755 /home/<USER>/xhs_notes_manager
sudo chmod -R 664 /home/<USER>/xhs_notes_manager/backend/xhs_notes.db
```

## 📊 性能问题

### 页面加载缓慢

**解决方案**：
1. 启用Nginx压缩：
   ```nginx
   gzip on;
   gzip_types text/plain text/css application/json application/javascript;
   ```

2. 优化静态资源缓存：
   ```nginx
   location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
       expires 1y;
       add_header Cache-Control "public, immutable";
   }
   ```

## 🔍 调试技巧

### 启用调试模式

1. **前端调试**：
   ```bash
   cd frontend
   npm run dev  # 开发模式
   ```

2. **后端调试**：
   ```bash
   cd backend
   python run.py  # 直接运行，查看详细日志
   ```

### 查看日志

```bash
# 后端日志
sudo journalctl -u xhs-backend -f

# Nginx日志
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log

# 应用日志
tail -f backend/logs/error.log
```

## 📞 获取帮助

如果以上解决方案无法解决问题，请：

1. 收集错误信息和日志
2. 记录复现步骤
3. 检查系统环境信息
4. 提交Issue或联系技术支持

---

**最后更新**：2025-01-30
**版本**：v1.0.0
