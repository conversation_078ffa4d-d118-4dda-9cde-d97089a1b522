# Ubuntu 24 + Nginx + MySQL 生产环境部署指南

本文档详细说明如何在 Ubuntu 24 服务器上使用 Nginx + MySQL 部署小红书笔记管理系统的生产环境。

## 📋 服务器配置信息

- **操作系统**: Ubuntu 24.04 LTS
- **服务器名称**: ben
- **项目目录**: `/home/<USER>/xhs_notes_manager`
- **数据库**: MySQL 8.0+
- **数据库名**: xhsnote
- **数据库用户**: xhsnote_user
- **数据库密码**: dhwdw6789jhgffHGF
- **Web服务器**: Nginx
- **进程管理**: systemd
- **Python版本**: 3.12+
- **Node.js版本**: 20+

## 🔌 端口配置 (避免冲突)

- **前端端口**: 8080 (Nginx，替代默认80端口)
- **后端端口**: 9000 (FastAPI，替代默认8000端口)
- **数据库端口**: 3306 (MySQL默认端口)

## 🏗️ 系统架构

```
Internet → Nginx (反向代理/静态文件) → Gunicorn (FastAPI后端)
                                              ↓
                                         MySQL 8.0 (数据库)
```

## 🛠️ 系统要求

### 硬件要求
- **CPU**: 2核心及以上
- **内存**: 4GB及以上（推荐8GB）
- **存储**: 20GB可用空间（推荐50GB）
- **网络**: 稳定的互联网连接

### 软件要求
- Ubuntu 24.04 LTS
- Python 3.12+
- Node.js 20+
- Nginx 1.24+
- MySQL 8.0+
- Git

## 🚀 部署步骤

### 步骤1：系统更新和基础软件安装

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装基础软件
sudo apt install -y curl wget git vim htop unzip

# 安装 Python 和相关工具
sudo apt install -y python3 python3-pip python3-venv python3-dev

# 安装 Node.js 20
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt install -y nodejs

# 验证安装
python3 --version
node --version
npm --version
```

### 步骤2：安装和配置 MySQL

```bash
# 安装 MySQL
sudo apt install -y mysql-server mysql-client

# 启动 MySQL 服务
sudo systemctl start mysql
sudo systemctl enable mysql

# 安全配置 MySQL
sudo mysql_secure_installation
```

#### 配置 MySQL 数据库

```bash
# 登录 MySQL
sudo mysql -u root -p

# 在 MySQL 中执行以下命令
```

```sql
-- 创建数据库
CREATE DATABASE xhsnote CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'xhsnote_user'@'localhost' IDENTIFIED BY 'dhwdw6789jhgffHGF';

-- 授权
GRANT ALL PRIVILEGES ON xhsnote.* TO 'xhsnote_user'@'localhost';

-- 刷新权限
FLUSH PRIVILEGES;

-- 退出
EXIT;
```

#### 测试数据库连接

```bash
# 测试连接
mysql -u xhsnote_user -p'dhwdw6789jhgffHGF' xhsnote -e "SELECT 1;"
```

### 步骤3：安装和配置 Nginx

```bash
# 安装 Nginx
sudo apt install -y nginx

# 启动 Nginx 服务
sudo systemctl start nginx
sudo systemctl enable nginx

# 检查状态
sudo systemctl status nginx
```

### 步骤4：创建项目目录和用户

```bash
# 创建项目目录
sudo mkdir -p /home/<USER>
sudo chown $USER:$USER /home/<USER>

# 切换到项目目录
cd /home/<USER>
```

### 步骤5：克隆项目代码

```bash
# 克隆项目（替换为你的实际仓库地址）
git clone https://github.com/your-username/xhs_notes_manager.git
cd xhs_notes_manager

# 或者如果已有代码，确保在正确目录
cd /home/<USER>/xhs_notes_manager
```

### 步骤6：配置后端环境

```bash
# 进入后端目录
cd /home/<USER>/xhs_notes_manager/backend

# 创建 Python 虚拟环境
python3 -m venv venv

# 激活虚拟环境
source venv/bin/activate

# 升级 pip
pip install --upgrade pip

# 安装依赖
pip install -r requirements.txt

# 安装生产环境依赖
pip install gunicorn uvicorn[standard] pymysql cryptography
```

#### 配置生产环境变量

```bash
# 创建生产环境配置文件
cat > /home/<USER>/xhs_notes_manager/backend/.env.production << 'EOF'
# Database Configuration
DATABASE_URL=mysql+pymysql://xhsnote_user:dhwdw6789jhgffHGF@localhost:3306/xhsnote

# Security
SECRET_KEY=your-super-secret-key-change-this-in-production-$(openssl rand -hex 32)
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=43200

# File Upload
UPLOAD_DIR=/home/<USER>/xhs_notes_manager/backend/uploads
MAX_FILE_SIZE=524288000
ALLOWED_IMAGE_EXTENSIONS=jpg,jpeg,png,gif,webp
ALLOWED_VIDEO_EXTENSIONS=mp4,mov,avi

# CORS - 添加你的域名
ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000,http://ben,https://ben

# Production
DEBUG=false
LOG_LEVEL=INFO
ENVIRONMENT=production
EOF
```

#### 创建数据库表

```bash
# 确保在虚拟环境中
source /home/<USER>/xhs_notes_manager/backend/venv/bin/activate

# 初始化数据库
cd /home/<USER>/xhs_notes_manager/backend
python init_db.py
```

### 步骤7：配置前端环境

```bash
# 进入前端目录
cd /home/<USER>/xhs_notes_manager/frontend

# 安装依赖
npm install

# 创建生产环境配置
cat > .env.production << 'EOF'
VITE_API_BASE_URL=http://ben:9000
VITE_APP_TITLE=小红书笔记管理系统
EOF

# 构建生产版本
npm run build
```

### 步骤8：配置 Gunicorn

```bash
# 创建 Gunicorn 配置文件
cat > /home/<USER>/xhs_notes_manager/backend/gunicorn.conf.py << 'EOF'
# Gunicorn configuration file
import multiprocessing

# Server socket
bind = "127.0.0.1:9000"
backlog = 2048

# Worker processes
workers = multiprocessing.cpu_count() * 2 + 1
worker_class = "uvicorn.workers.UvicornWorker"
worker_connections = 1000
timeout = 30
keepalive = 2

# Restart workers after this many requests, to help prevent memory leaks
max_requests = 1000
max_requests_jitter = 100

# Logging
accesslog = "/home/<USER>/xhs_notes_manager/backend/logs/access.log"
errorlog = "/home/<USER>/xhs_notes_manager/backend/logs/error.log"
loglevel = "info"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'

# Process naming
proc_name = "xhs_notes_manager"

# Daemon mode
daemon = False
pidfile = "/home/<USER>/xhs_notes_manager/backend/gunicorn.pid"
user = None
group = None
tmp_upload_dir = None

# SSL (if needed)
# keyfile = "/path/to/keyfile"
# certfile = "/path/to/certfile"
EOF

# 创建日志目录
mkdir -p /home/<USER>/xhs_notes_manager/backend/logs
```

### 步骤9：创建 systemd 服务

```bash
# 创建后端服务文件
sudo tee /etc/systemd/system/xhs-backend.service > /dev/null << 'EOF'
[Unit]
Description=XHS Notes Manager Backend
After=network.target mysql.service
Requires=mysql.service

[Service]
Type=notify
User=ubuntu
Group=ubuntu
WorkingDirectory=/home/<USER>/xhs_notes_manager/backend
Environment=PATH=/home/<USER>/xhs_notes_manager/backend/venv/bin
Environment=ENV_FILE=/home/<USER>/xhs_notes_manager/backend/.env.production
ExecStart=/home/<USER>/xhs_notes_manager/backend/venv/bin/gunicorn -c gunicorn.conf.py app.main:app
ExecReload=/bin/kill -s HUP $MAINPID
Restart=always
RestartSec=3
KillMode=mixed
TimeoutStopSec=5

[Install]
WantedBy=multi-user.target
EOF

# 重新加载 systemd
sudo systemctl daemon-reload

# 启动并启用服务
sudo systemctl enable xhs-backend
sudo systemctl start xhs-backend

# 检查状态
sudo systemctl status xhs-backend

### 步骤10：配置 Nginx

```bash
# 创建 Nginx 配置文件
sudo tee /etc/nginx/sites-available/xhs-notes-manager > /dev/null << 'EOF'
server {
    listen 8080;
    server_name ben localhost;

    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # 客户端最大上传大小
    client_max_body_size 500M;

    # 前端静态文件
    location / {
        root /home/<USER>/xhs_notes_manager/frontend/dist;
        try_files $uri $uri/ /index.html;

        # 静态文件缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # API 代理
    location /api/ {
        proxy_pass http://127.0.0.1:9000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300;
        proxy_connect_timeout 300;
        proxy_send_timeout 300;
    }

    # 文件上传代理
    location /uploads/ {
        proxy_pass http://127.0.0.1:9000;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # API 文档
    location /docs {
        proxy_pass http://127.0.0.1:9000;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 健康检查
    location /health {
        proxy_pass http://127.0.0.1:9000;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        access_log off;
    }

    # 日志配置
    access_log /var/log/nginx/xhs-notes-manager.access.log;
    error_log /var/log/nginx/xhs-notes-manager.error.log;
}
EOF

# 启用站点
sudo ln -sf /etc/nginx/sites-available/xhs-notes-manager /etc/nginx/sites-enabled/

# 删除默认站点
sudo rm -f /etc/nginx/sites-enabled/default

# 测试 Nginx 配置
sudo nginx -t

# 重新加载 Nginx
sudo systemctl reload nginx
```

### 步骤11：配置防火墙

```bash
# 安装 UFW（如果未安装）
sudo apt install -y ufw

# 配置防火墙规则
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw allow 80
sudo ufw allow 443

# 启用防火墙
sudo ufw --force enable

# 检查状态
sudo ufw status
```

### 步骤12：创建管理脚本

```bash
# 创建管理脚本目录
mkdir -p /home/<USER>/xhs_notes_manager/scripts

# 创建启动脚本
cat > /home/<USER>/xhs_notes_manager/scripts/start.sh << 'EOF'
#!/bin/bash
echo "Starting XHS Notes Manager..."

# 启动后端服务
sudo systemctl start xhs-backend

# 重新加载 Nginx
sudo systemctl reload nginx

echo "Services started successfully!"
echo "Frontend: http://ben"
echo "API Docs: http://ben/docs"
echo "Health Check: http://ben/health"
EOF

# 创建停止脚本
cat > /home/<USER>/xhs_notes_manager/scripts/stop.sh << 'EOF'
#!/bin/bash
echo "Stopping XHS Notes Manager..."

# 停止后端服务
sudo systemctl stop xhs-backend

echo "Services stopped successfully!"
EOF

# 创建重启脚本
cat > /home/<USER>/xhs_notes_manager/scripts/restart.sh << 'EOF'
#!/bin/bash
echo "Restarting XHS Notes Manager..."

# 重启后端服务
sudo systemctl restart xhs-backend

# 重新加载 Nginx
sudo systemctl reload nginx

echo "Services restarted successfully!"
EOF

# 创建状态检查脚本
cat > /home/<USER>/xhs_notes_manager/scripts/status.sh << 'EOF'
#!/bin/bash
echo "=== XHS Notes Manager Status ==="
echo

echo "Backend Service:"
sudo systemctl status xhs-backend --no-pager -l

echo
echo "Nginx Service:"
sudo systemctl status nginx --no-pager -l

echo
echo "MySQL Service:"
sudo systemctl status mysql --no-pager -l

echo
echo "Disk Usage:"
df -h /home/<USER>/xhs_notes_manager

echo
echo "Memory Usage:"
free -h

echo
echo "Process Information:"
ps aux | grep -E "(gunicorn|nginx|mysql)" | grep -v grep
EOF

# 创建日志查看脚本
cat > /home/<USER>/xhs_notes_manager/scripts/logs.sh << 'EOF'
#!/bin/bash

case "$1" in
    backend)
        echo "=== Backend Error Logs ==="
        sudo tail -f /home/<USER>/xhs_notes_manager/backend/logs/error.log
        ;;
    access)
        echo "=== Backend Access Logs ==="
        sudo tail -f /home/<USER>/xhs_notes_manager/backend/logs/access.log
        ;;
    nginx)
        echo "=== Nginx Error Logs ==="
        sudo tail -f /var/log/nginx/xhs-notes-manager.error.log
        ;;
    nginx-access)
        echo "=== Nginx Access Logs ==="
        sudo tail -f /var/log/nginx/xhs-notes-manager.access.log
        ;;
    mysql)
        echo "=== MySQL Error Logs ==="
        sudo tail -f /var/log/mysql/error.log
        ;;
    systemd)
        echo "=== Systemd Service Logs ==="
        sudo journalctl -u xhs-backend -f
        ;;
    *)
        echo "Usage: $0 {backend|access|nginx|nginx-access|mysql|systemd}"
        echo
        echo "Available log types:"
        echo "  backend      - Backend application error logs"
        echo "  access       - Backend application access logs"
        echo "  nginx        - Nginx error logs"
        echo "  nginx-access - Nginx access logs"
        echo "  mysql        - MySQL error logs"
        echo "  systemd      - Systemd service logs"
        ;;
esac
EOF

# 设置脚本权限
chmod +x /home/<USER>/xhs_notes_manager/scripts/*.sh
```

### 步骤13：创建备份脚本

```bash
# 创建备份脚本
cat > /home/<USER>/xhs_notes_manager/scripts/backup.sh << 'EOF'
#!/bin/bash

BACKUP_DIR="/home/<USER>/backups"
PROJECT_DIR="/home/<USER>/xhs_notes_manager"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="xhs_notes_manager_backup_$DATE"

echo "Creating backup: $BACKUP_NAME"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
echo "Backing up database..."
mysqldump -u xhsnote_user -p'dhwdw6789jhgffHGF' xhsnote > $BACKUP_DIR/${BACKUP_NAME}_database.sql

# 备份上传文件
echo "Backing up uploaded files..."
tar -czf $BACKUP_DIR/${BACKUP_NAME}_uploads.tar.gz -C $PROJECT_DIR/backend uploads/

# 备份配置文件
echo "Backing up configuration..."
tar -czf $BACKUP_DIR/${BACKUP_NAME}_config.tar.gz \
    $PROJECT_DIR/backend/.env.production \
    /etc/nginx/sites-available/xhs-notes-manager \
    /etc/systemd/system/xhs-backend.service

# 创建备份信息文件
cat > $BACKUP_DIR/${BACKUP_NAME}_info.txt << EOL
Backup Created: $(date)
Server: ben
Project Directory: $PROJECT_DIR
Database: xhsnote
Backup Files:
- ${BACKUP_NAME}_database.sql
- ${BACKUP_NAME}_uploads.tar.gz
- ${BACKUP_NAME}_config.tar.gz
EOL

echo "Backup completed: $BACKUP_DIR/$BACKUP_NAME*"

# 清理旧备份（保留最近7天）
find $BACKUP_DIR -name "xhs_notes_manager_backup_*" -mtime +7 -delete

echo "Old backups cleaned up (kept last 7 days)"
EOF

# 设置备份脚本权限
chmod +x /home/<USER>/xhs_notes_manager/scripts/backup.sh

# 创建定时备份任务
(crontab -l 2>/dev/null; echo "0 2 * * * /home/<USER>/xhs_notes_manager/scripts/backup.sh >> /var/log/xhs-backup.log 2>&1") | crontab -
```

### 步骤14：初始化管理员账户

```bash
# 进入后端目录并激活虚拟环境
cd /home/<USER>/xhs_notes_manager/backend
source venv/bin/activate

# 设置环境变量
export ENV_FILE=/home/<USER>/xhs_notes_manager/backend/.env.production

# 创建管理员账户
python reset_admin_password.py create admin admin123

# 验证账户创建
python reset_admin_password.py list
```

## 🔧 部署验证

### 验证服务状态

```bash
# 检查所有服务状态
/home/<USER>/xhs_notes_manager/scripts/status.sh
```

### 验证网络连接

```bash
# 测试后端健康检查
curl http://ben/health

# 测试API文档
curl -I http://ben/docs

# 测试前端页面
curl -I http://ben/
```

### 验证数据库连接

```bash
# 测试数据库连接
mysql -u xhsnote_user -p'dhwdw6789jhgffHGF' xhsnote -e "SHOW TABLES;"
```

## 📊 监控和维护

### 日志监控

```bash
# 查看不同类型的日志
/home/<USER>/xhs_notes_manager/scripts/logs.sh backend
/home/<USER>/xhs_notes_manager/scripts/logs.sh nginx
/home/<USER>/xhs_notes_manager/scripts/logs.sh mysql
```

### 性能监控

```bash
# 安装监控工具
sudo apt install -y htop iotop nethogs

# 监控系统资源
htop

# 监控磁盘I/O
sudo iotop

# 监控网络使用
sudo nethogs
```

### 定期维护任务

```bash
# 创建维护脚本
cat > /home/<USER>/xhs_notes_manager/scripts/maintenance.sh << 'EOF'
#!/bin/bash

echo "=== XHS Notes Manager Maintenance ==="
echo "Started at: $(date)"

# 清理日志文件（保留最近30天）
find /home/<USER>/xhs_notes_manager/backend/logs -name "*.log" -mtime +30 -delete
find /var/log/nginx -name "xhs-notes-manager.*.log" -mtime +30 -delete

# 清理临时文件
find /tmp -name "*xhs*" -mtime +1 -delete

# 优化数据库
mysql -u xhsnote_user -p'dhwdw6789jhgffHGF' xhsnote -e "OPTIMIZE TABLE notes, products, users, prompts;"

# 检查磁盘空间
df -h /home/<USER>

# 重启服务（如果需要）
if [ "$1" = "--restart" ]; then
    echo "Restarting services..."
    sudo systemctl restart xhs-backend
    sudo systemctl reload nginx
fi

echo "Maintenance completed at: $(date)"
EOF

chmod +x /home/<USER>/xhs_notes_manager/scripts/maintenance.sh

# 添加到定时任务（每周日凌晨3点执行）
(crontab -l 2>/dev/null; echo "0 3 * * 0 /home/<USER>/xhs_notes_manager/scripts/maintenance.sh >> /var/log/xhs-maintenance.log 2>&1") | crontab -
```

## 🔒 SSL/HTTPS 配置（可选）

### 使用 Let's Encrypt 免费证书

```bash
# 安装 Certbot
sudo apt install -y certbot python3-certbot-nginx

# 获取证书（替换为你的实际域名）
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加以下行：
# 0 12 * * * /usr/bin/certbot renew --quiet
```

## 🚨 故障排除

### 常见问题

1. **前端登录时出现网络错误**

   **症状：** 浏览器控制台显示 `ERR_NETWORK` 或 `ERR_CONNECTION_REFUSED`

   **原因：** 前端生产环境配置错误，API请求直接指向后端端口

   **解决方案：**
   ```bash
   # 1. 检查前端环境配置
   cat /home/<USER>/xhs_notes_manager/frontend/.env.production

   # 2. 确保 VITE_API_BASE_URL 为空（使用相对路径）
   # 编辑文件，将 VITE_API_BASE_URL=http://ben:9000 改为 VITE_API_BASE_URL=

   # 3. 重新构建前端
   cd /home/<USER>/xhs_notes_manager/frontend
   npm run build

   # 4. 重启Nginx
   sudo systemctl reload nginx
   ```

2. **服务无法启动**
   ```bash
   # 检查日志
   sudo journalctl -u xhs-backend -f

   # 检查配置文件
   sudo nginx -t
   ```

3. **数据库连接失败**
   ```bash
   # 检查MySQL状态
   sudo systemctl status mysql

   # 测试连接
   mysql -u xhsnote_user -p'dhwdw6789jhgffHGF' xhsnote
   ```

4. **权限问题**
   ```bash
   # 修复文件权限
   sudo chown -R ubuntu:ubuntu /home/<USER>/xhs_notes_manager
   sudo chmod -R 755 /home/<USER>/xhs_notes_manager
   ```

5. **端口被占用**
   ```bash
   # 查看端口使用情况
   sudo netstat -tlnp | grep :9000
   sudo netstat -tlnp | grep :8080
   ```

### 网络连接诊断

**快速诊断命令：**
```bash
# 检查服务状态
./scripts/status.sh

# 测试API连接
curl http://127.0.0.1:8080/api/v1/auth/token -X POST \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=admin&password=your_password"

# 检查Nginx代理
curl -v http://127.0.0.1:8080/health
```

**详细故障排除指南：** 请参考 [故障排除指南](troubleshooting-guide.md)

### 紧急恢复

```bash
# 从备份恢复数据库
mysql -u xhsnote_user -p'dhwdw6789jhgffHGF' xhsnote < /home/<USER>/backups/backup_database.sql

# 恢复上传文件
tar -xzf /home/<USER>/backups/backup_uploads.tar.gz -C /home/<USER>/xhs_notes_manager/backend/
```

## 📋 部署检查清单

### 基础环境配置
- [ ] 系统更新完成
- [ ] MySQL 安装并配置完成
- [ ] 数据库和用户创建完成
- [ ] Nginx 安装并配置完成
- [ ] 项目代码部署完成

### 后端配置
- [ ] Python 虚拟环境创建完成
- [ ] 后端依赖安装完成
- [ ] 环境变量配置完成（.env.production）
- [ ] Gunicorn 配置完成
- [ ] systemd 服务配置完成

### 前端配置
- [ ] 前端依赖安装完成
- [ ] **前端环境配置检查**：
  - [ ] `.env.production` 中 `VITE_API_BASE_URL` 为空或相对路径
  - [ ] 不能配置为 `http://ben:9000` 等直接后端地址
- [ ] 前端构建完成（`npm run build`）
- [ ] 构建产物正确部署到 `/home/<USER>/xhs_notes_manager/frontend/dist`

### 服务配置
- [ ] Nginx 站点配置完成
- [ ] 防火墙配置完成
- [ ] 管理脚本创建完成
- [ ] 备份脚本配置完成

### 验证测试
- [ ] 管理员账户创建完成
- [ ] 服务启动并运行正常
- [ ] **网络连接测试**：
  - [ ] 后端健康检查：`curl http://127.0.0.1:9000/health`
  - [ ] Nginx代理测试：`curl http://127.0.0.1:8080/health`
  - [ ] API端点测试：`curl http://127.0.0.1:8080/api/v1/auth/token -X OPTIONS`
  - [ ] 登录功能测试：浏览器访问并测试登录
- [ ] 日志监控配置完成

### 常见问题检查
- [ ] **前端API请求检查**：
  - [ ] 浏览器F12检查API请求URL是否正确
  - [ ] 确认没有直接请求后端端口（如ben:9000）
  - [ ] 确认API请求通过Nginx代理（如192.168.1.101:8080/api/...）
- [ ] **服务端口检查**：
  - [ ] 后端服务运行在9000端口
  - [ ] Nginx运行在8080端口
  - [ ] 防火墙允许8080端口访问

## 🎯 访问应用

部署完成后，你可以通过以下方式访问应用：

- **前端界面**: http://ben
- **API文档**: http://ben/docs
- **健康检查**: http://ben/health

**默认管理员账户**:
- 用户名: admin
- 密码: admin123

## � 最新功能更新

### 商品管理页面增强 (2025-01-03)

**新增功能：**

- ✅ **笔记统计显示**：每个商品显示详细的笔记统计信息
  - 总计笔记数量
  - 已使用笔记数量
  - 待使用笔记数量
- ✅ **高级筛选功能**：
  - 按商品所有人筛选（下拉选择）
  - 按待使用笔记数量范围筛选（最小值-最大值）
  - 支持多条件组合筛选
- ✅ **PC友好的交互设计**：
  - 横向布局的数值范围输入框
  - 简洁的下拉选择器
  - 防抖输入优化（500ms）
  - 美观的"至"字分隔符

**技术实现：**

- 后端API增强：支持owner_id和unused_notes_count范围筛选
- SQL优化：使用`func.count().filter()`进行高效的条件计数
- 前端UI优化：响应式布局和用户体验改进

**访问路径：** `http://ben/products`

### 笔记下载页面增强 (2025-01-02)

**新增功能：**

- ✅ **内容状态指示器**：显示笔记是否包含内容、封面图、笔记图片、视频
- ✅ **批量下载优化**：支持按内容类型筛选下载

**访问路径：** `http://ben/notes/download`

### 视频上传限制调整 (2025-01-02)

**配置更新：**

- ✅ **文件大小限制**：视频上传从10MB提升至500MB
- ✅ **相关配置同步更新**：
  - 后端FastAPI配置
  - 前端验证逻辑
  - Nginx客户端上传限制
  - 文档说明更新

## �📞 技术支持

如果在部署过程中遇到问题，请：

1. 检查相关日志文件
2. 运行状态检查脚本
3. 参考故障排除部分
4. 收集错误信息和系统状态

---

**部署完成！** 🎉

你的小红书笔记管理系统现在已经在 Ubuntu 24 + Nginx + MySQL 环境中成功部署并运行。
