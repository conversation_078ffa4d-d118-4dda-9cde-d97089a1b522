# 用户管理指南

## 📋 概述

本指南详细说明如何管理小红书笔记管理系统的用户账户，包括管理员和普通用户的创建、修改、删除等操作。

## 🔧 用户管理工具

系统提供了一个强大的命令行工具 `reset_admin_password.py` 来管理用户账户。

### 工具位置
```bash
cd /home/<USER>/xhs_notes_manager/backend
python reset_admin_password.py
```

## 📝 可用命令

### 1. 查看所有用户
```bash
python reset_admin_password.py list
```
**功能**: 显示系统中所有用户的信息，包括ID、用户名和角色。

**示例输出**:
```
📋 Current users:
--------------------------------------------------
ID: 1, Username: admin, Role: UserRole.ADMIN
ID: 2, Username: john, Role: UserRole.USER
ID: 3, Username: manager, Role: UserRole.ADMIN
--------------------------------------------------
```

### 2. 重置admin用户密码
```bash
python reset_admin_password.py reset <新密码>
```
**功能**: 重置默认admin用户的密码。如果admin用户不存在，会自动创建。

**示例**:
```bash
python reset_admin_password.py reset admin123
python reset_admin_password.py reset myNewSecurePassword2024
```

### 3. 创建新的管理员账户
```bash
python reset_admin_password.py create <用户名> <密码>
```
**功能**: 创建一个新的管理员账户。

**示例**:
```bash
python reset_admin_password.py create superadmin mypassword
python reset_admin_password.py create manager securepass123
```

### 4. 设置特定用户的密码 ⭐
```bash
python reset_admin_password.py setpass <用户名> <新密码>
```
**功能**: 为任何现有用户（管理员或普通用户）设置新密码。

**示例**:
```bash
python reset_admin_password.py setpass john newpassword123
python reset_admin_password.py setpass manager updatedpass456
```

### 5. 删除用户账户 ⭐
```bash
python reset_admin_password.py delete <用户名>
```
**功能**: 删除指定的用户账户。系统会要求确认操作。

**安全限制**:
- 不能删除系统中最后一个管理员账户
- 删除前会要求确认

**示例**:
```bash
python reset_admin_password.py delete olduser
```

**确认提示**:
```
⚠️ Are you sure you want to delete user 'olduser'? (y/N): y
✅ User 'olduser' (Role: UserRole.USER) has been deleted
```

### 6. 提升用户为管理员
```bash
python reset_admin_password.py promote <用户名>
```
**功能**: 将普通用户提升为管理员。

**示例**:
```bash
python reset_admin_password.py promote john
```

### 7. 降级管理员为普通用户
```bash
python reset_admin_password.py demote <用户名>
```
**功能**: 将管理员降级为普通用户。

**安全限制**:
- 不能降级系统中最后一个管理员

**示例**:
```bash
python reset_admin_password.py demote manager
```

## 🛡️ 安全注意事项

### 密码要求
- 建议使用强密码（至少8位，包含字母、数字）
- 避免使用常见密码如 "123456", "password" 等
- 定期更换密码

### 管理员账户保护
- 系统至少保留一个管理员账户
- 不能删除或降级最后一个管理员
- 建议创建多个管理员账户作为备份

### 操作前准备
- **重要**: 执行用户管理操作前，请停止后端服务
```bash
sudo systemctl stop xhs-backend
```

- 操作完成后，重新启动服务
```bash
sudo systemctl start xhs-backend
```

## 📚 常用操作场景

### 场景1: 忘记admin密码
```bash
cd /home/<USER>/xhs_notes_manager/backend
python reset_admin_password.py reset newpassword123
```

### 场景2: 为新员工创建管理员账户
```bash
python reset_admin_password.py create newmanager securepass456
```

### 场景3: 员工离职，删除账户
```bash
# 先查看所有用户
python reset_admin_password.py list

# 删除指定用户
python reset_admin_password.py delete formeremployee
```

### 场景4: 重置某个用户的密码
```bash
python reset_admin_password.py setpass username newpassword789
```

### 场景5: 提升信任用户为管理员
```bash
python reset_admin_password.py promote trusteduser
```

## ⚠️ 故障排除

### 问题1: 脚本执行失败
**解决方案**:
```bash
# 确保在正确的目录
cd /home/<USER>/xhs_notes_manager/backend

# 激活虚拟环境
source venv/bin/activate

# 检查数据库连接
python -c "from app.core.database import AsyncSessionLocal; print('Database connection OK')"
```

### 问题2: 权限错误
**解决方案**:
```bash
# 检查文件权限
ls -la reset_admin_password.py

# 如果需要，修改权限
chmod +x reset_admin_password.py
```

### 问题3: 数据库锁定
**解决方案**:
```bash
# 确保后端服务已停止
sudo systemctl stop xhs-backend

# 等待几秒后再执行脚本
python reset_admin_password.py list
```

## 📊 用户角色说明

### 管理员 (ADMIN)
- 可以管理所有用户账户
- 可以访问系统管理功能
- 可以查看和管理所有笔记和产品
- 可以访问系统统计信息

### 普通用户 (USER)
- 只能管理自己的笔记和产品
- 不能访问其他用户的数据
- 不能执行系统管理操作

## 🔄 最佳实践

1. **定期备份用户数据**
2. **为关键管理员创建备用账户**
3. **定期审查用户列表，删除不需要的账户**
4. **使用强密码策略**
5. **记录重要的用户管理操作**

---

**注意**: 所有用户管理操作都会立即生效，请谨慎操作。
