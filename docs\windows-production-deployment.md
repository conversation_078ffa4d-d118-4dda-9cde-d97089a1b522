# Windows 生产环境部署指南

本指南详细介绍如何在Windows服务器上部署小红书笔记管理系统到生产环境。

## 📋 系统要求

### 硬件要求
- **CPU**: 2核心及以上
- **内存**: 4GB及以上（推荐8GB）
- **存储**: 20GB可用空间（推荐50GB）
- **网络**: 稳定的互联网连接

### 软件要求
- **操作系统**: Windows Server 2019/2022 或 Windows 10/11
- **Python**: 3.11或更高版本
- **Node.js**: 18.0或更高版本
- **Git**: 最新版本
- **数据库**: MySQL 8.0+ 或 PostgreSQL 13+（可选，默认使用SQLite）

## 🛠️ 环境准备

### 1. 安装Python 3.11+

1. 访问 [Python官网](https://www.python.org/downloads/windows/)
2. 下载Python 3.11或更高版本
3. 安装时勾选"Add Python to PATH"
4. 验证安装：
```cmd
python --version
pip --version
```

### 2. 安装Node.js 18+

1. 访问 [Node.js官网](https://nodejs.org/)
2. 下载LTS版本（18.0+）
3. 安装并验证：
```cmd
node --version
npm --version
```

### 3. 安装Git

1. 访问 [Git官网](https://git-scm.com/download/win)
2. 下载并安装Git for Windows
3. 验证安装：
```cmd
git --version
```

## 📦 项目部署

### 1. 克隆项目

```cmd
# 创建部署目录
mkdir C:\deploy
cd C:\deploy

# 克隆项目
git clone https://gitee.com/bin1874/xhs_notes_manager.git
cd xhs_notes_manager
```

### 2. 后端部署

#### 2.1 安装Python依赖

```cmd
cd backend

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
venv\Scripts\activate

# 升级pip
python -m pip install --upgrade pip

# 安装依赖
pip install -r requirements.txt
```

#### 2.2 配置环境变量

创建 `backend\.env` 文件：

```env
# 数据库配置
DATABASE_URL=sqlite:///./xhs_notes.db
# 如果使用MySQL：DATABASE_URL=mysql+aiomysql://username:password@localhost/xhs_notes
# 如果使用PostgreSQL：DATABASE_URL=postgresql+asyncpg://username:password@localhost/xhs_notes

# JWT密钥（请更改为随机字符串）
SECRET_KEY=your-super-secret-key-change-this-in-production

# 服务器配置
HOST=0.0.0.0
PORT=8000

# 文件上传配置
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760  # 10MB

# 日志配置
LOG_LEVEL=INFO
```

#### 2.3 初始化数据库

```cmd
# 确保在backend目录下且虚拟环境已激活
python init_db.py
```

#### 2.4 安装生产服务器

```cmd
# 安装gunicorn（Windows推荐使用waitress）
pip install waitress
```

### 3. 前端部署

#### 3.1 安装依赖并构建

```cmd
cd ..\frontend

# 安装依赖
npm install

# 构建生产版本
npm run build
```

构建完成后，`dist` 目录包含所有前端静态文件。

## 🚀 生产环境运行

### 方法一：使用批处理脚本（推荐）

创建 `start-production.bat`：

```batch
@echo off
echo Starting XHS Notes Manager in Production Mode...

cd /d C:\deploy\xhs_notes_manager

echo Starting Backend Server...
cd backend
call venv\Scripts\activate
start /b waitress-serve --host=0.0.0.0 --port=8000 app.main:app

echo Backend started on http://localhost:8000

echo.
echo Frontend files are in: %cd%\..\frontend\dist
echo Please configure your web server to serve these files.

pause
```

### 方法二：手动启动

#### 启动后端服务

```cmd
cd C:\deploy\xhs_notes_manager\backend
venv\Scripts\activate
waitress-serve --host=0.0.0.0 --port=8000 app.main:app
```

## 🌐 Web服务器配置

### 使用IIS（推荐）

#### 1. 安装IIS和相关模块

1. 打开"控制面板" → "程序" → "启用或关闭Windows功能"
2. 勾选"Internet Information Services"
3. 安装URL Rewrite模块和Application Request Routing

#### 2. 配置网站

1. 打开IIS管理器
2. 右键"网站" → "添加网站"
3. 配置：
   - 网站名称：XHS Notes Manager
   - 物理路径：`C:\deploy\xhs_notes_manager\frontend\dist`
   - 端口：80（或443用于HTTPS）

#### 3. 配置反向代理

在网站根目录创建 `web.config`：

```xml
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <system.webServer>
        <rewrite>
            <rules>
                <!-- API代理规则 -->
                <rule name="API Proxy" stopProcessing="true">
                    <match url="^api/(.*)" />
                    <action type="Rewrite" url="http://localhost:8000/api/{R:1}" />
                </rule>
                <!-- SPA路由规则 -->
                <rule name="SPA Routes" stopProcessing="true">
                    <match url=".*" />
                    <conditions logicalGrouping="MatchAll">
                        <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
                        <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
                    </conditions>
                    <action type="Rewrite" url="/" />
                </rule>
            </rules>
        </rewrite>
    </system.webServer>
</configuration>
```

### 使用Nginx（替代方案）

如果选择使用Nginx，配置文件示例：

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # 前端静态文件
    location / {
        root C:/deploy/xhs_notes_manager/frontend/dist;
        try_files $uri $uri/ /index.html;
    }
    
    # API代理
    location /api/ {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 文件上传
    location /uploads/ {
        alias C:/deploy/xhs_notes_manager/backend/uploads/;
    }
}
```

## 🔧 系统服务配置

### 创建Windows服务

使用NSSM（Non-Sucking Service Manager）将后端应用注册为Windows服务：

#### 1. 下载并安装NSSM

1. 访问 [NSSM官网](https://nssm.cc/download)
2. 下载并解压到 `C:\nssm`

#### 2. 创建服务

```cmd
# 以管理员身份运行命令提示符
cd C:\nssm\win64

# 安装服务
nssm install XHSNotesBackend

# 配置服务
nssm set XHSNotesBackend Application C:\deploy\xhs_notes_manager\backend\venv\Scripts\python.exe
nssm set XHSNotesBackend AppParameters -m waitress --host=0.0.0.0 --port=8000 app.main:app
nssm set XHSNotesBackend AppDirectory C:\deploy\xhs_notes_manager\backend
nssm set XHSNotesBackend DisplayName "XHS Notes Manager Backend"
nssm set XHSNotesBackend Description "小红书笔记管理系统后端服务"

# 启动服务
nssm start XHSNotesBackend
```

## 🔒 安全配置

### 1. 防火墙配置

```cmd
# 允许8000端口（如果需要直接访问后端）
netsh advfirewall firewall add rule name="XHS Notes Backend" dir=in action=allow protocol=TCP localport=8000

# 允许80端口（HTTP）
netsh advfirewall firewall add rule name="HTTP" dir=in action=allow protocol=TCP localport=80

# 允许443端口（HTTPS）
netsh advfirewall firewall add rule name="HTTPS" dir=in action=allow protocol=TCP localport=443
```

### 2. SSL证书配置

#### 使用Let's Encrypt（免费）

1. 安装Certbot for Windows
2. 获取证书：
```cmd
certbot certonly --webroot -w C:\deploy\xhs_notes_manager\frontend\dist -d your-domain.com
```

#### 在IIS中配置SSL

1. 打开IIS管理器
2. 选择网站 → "绑定"
3. 添加HTTPS绑定，选择SSL证书

## 📊 监控和日志

### 1. 日志配置

后端日志位置：`backend\logs\`

### 2. 性能监控

使用Windows性能监视器监控：
- CPU使用率
- 内存使用率
- 磁盘I/O
- 网络流量

### 3. 应用监控

创建健康检查脚本 `health-check.bat`：

```batch
@echo off
echo Checking XHS Notes Manager Health...

# 检查后端服务
curl -f http://localhost:8000/health
if %errorlevel% neq 0 (
    echo Backend service is down!
    # 重启服务
    nssm restart XHSNotesBackend
)

# 检查前端服务
curl -f http://localhost/
if %errorlevel% neq 0 (
    echo Frontend service is down!
    # 重启IIS
    iisreset
)

echo Health check completed.
```

## 🔄 备份和恢复

### 1. 数据库备份

创建备份脚本 `backup.bat`：

```batch
@echo off
set BACKUP_DIR=C:\backup\xhs_notes
set DATE=%date:~0,4%%date:~5,2%%date:~8,2%

mkdir %BACKUP_DIR%\%DATE%

# 备份SQLite数据库
copy C:\deploy\xhs_notes_manager\backend\xhs_notes.db %BACKUP_DIR%\%DATE%\

# 备份上传文件
xcopy C:\deploy\xhs_notes_manager\backend\uploads %BACKUP_DIR%\%DATE%\uploads\ /E /I

echo Backup completed: %BACKUP_DIR%\%DATE%
```

### 2. 自动备份

使用Windows任务计划程序设置定期备份：

1. 打开"任务计划程序"
2. 创建基本任务
3. 设置触发器（如每日凌晨2点）
4. 设置操作：运行备份脚本

## 🚨 故障排除

### 常见问题

1. **端口被占用**
   ```cmd
   netstat -ano | findstr :8000
   taskkill /PID <PID> /F
   ```

2. **权限问题**
   - 确保IIS应用程序池有足够权限
   - 检查文件夹权限设置

3. **Python虚拟环境问题**
   ```cmd
   # 重新创建虚拟环境
   rmdir /s venv
   python -m venv venv
   venv\Scripts\activate
   pip install -r requirements.txt
   ```

4. **数据库连接问题**
   - 检查数据库服务状态
   - 验证连接字符串
   - 检查防火墙设置

### 日志查看

- **后端日志**: `backend\logs\app.log`
- **IIS日志**: `C:\inetpub\logs\LogFiles\`
- **Windows事件日志**: 事件查看器

## 📞 技术支持

如遇到部署问题，请：

1. 检查日志文件
2. 查看故障排除部分
3. 提交Issue到项目仓库
4. 联系技术支持团队

---

**部署完成后，请访问您的域名验证系统是否正常运行！**
