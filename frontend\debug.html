<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XHS Notes Manager - Network Debug</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        @media (max-width: 768px) {
            .grid { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <h1>🔧 XHS Notes Manager - Network Debug Tool</h1>
    
    <div class="container">
        <h2>📊 System Information</h2>
        <div id="systemInfo"></div>
    </div>

    <div class="grid">
        <div class="container">
            <h2>🌐 Connectivity Tests</h2>
            <button onclick="testConnectivity()">Test Backend Health</button>
            <button onclick="testCORS()">Test CORS</button>
            <button onclick="testPorts()">Test Ports</button>
            <div id="connectivityResults"></div>
        </div>

        <div class="container">
            <h2>🔐 Authentication Test</h2>
            <div>
                <label>Username: <input type="text" id="username" value="admin"></label><br><br>
                <label>Password: <input type="password" id="password" value="admin123"></label><br><br>
                <button onclick="testLogin()">Test Login</button>
            </div>
            <div id="authResults"></div>
        </div>
    </div>

    <div class="container">
        <h2>📝 Debug Log</h2>
        <button onclick="clearLog()">Clear Log</button>
        <button onclick="exportLog()">Export Log</button>
        <div id="debugLog" class="log"></div>
    </div>

    <script>
        // Debug logging
        const debugLog = document.getElementById('debugLog');
        const log = (message, type = 'info') => {
            const timestamp = new Date().toISOString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            debugLog.textContent += logEntry;
            debugLog.scrollTop = debugLog.scrollHeight;
            console.log(logEntry);
        };

        // System information
        const displaySystemInfo = () => {
            const info = {
                'Current URL': window.location.href,
                'Hostname': window.location.hostname,
                'Port': window.location.port || '80',
                'Protocol': window.location.protocol,
                'User Agent': navigator.userAgent,
                'Language': navigator.language,
                'Platform': navigator.platform,
                'Online': navigator.onLine,
                'Local Storage Available': typeof(Storage) !== "undefined"
            };

            const isLanMode = window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1';
            const backendHost = isLanMode ? window.location.hostname : '127.0.0.1';
            
            info['LAN Mode'] = isLanMode;
            info['Backend Host'] = backendHost;
            info['Backend URL'] = `http://${backendHost}:8000`;

            const systemInfoDiv = document.getElementById('systemInfo');
            systemInfoDiv.innerHTML = Object.entries(info)
                .map(([key, value]) => `<strong>${key}:</strong> ${value}`)
                .join('<br>');

            log(`System info loaded. LAN Mode: ${isLanMode}, Backend: ${backendHost}`);
        };

        // Connectivity tests
        const testConnectivity = async () => {
            const resultsDiv = document.getElementById('connectivityResults');
            resultsDiv.innerHTML = '<div class="info">Testing connectivity...</div>';
            
            const isLanMode = window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1';
            const backendHost = isLanMode ? window.location.hostname : '127.0.0.1';
            const backendUrl = `http://${backendHost}:8000`;

            log(`Testing connectivity to ${backendUrl}`);

            try {
                const response = await fetch(`${backendUrl}/health`, {
                    method: 'GET',
                    mode: 'cors',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });

                if (response.ok) {
                    const data = await response.json();
                    resultsDiv.innerHTML = `<div class="success">✅ Backend is healthy: ${JSON.stringify(data)}</div>`;
                    log(`Connectivity test passed: ${response.status} ${response.statusText}`);
                } else {
                    resultsDiv.innerHTML = `<div class="error">❌ Backend responded with error: ${response.status} ${response.statusText}</div>`;
                    log(`Connectivity test failed: ${response.status} ${response.statusText}`);
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">❌ Connection failed: ${error.message}</div>`;
                log(`Connectivity test error: ${error.message}`);
            }
        };

        const testCORS = async () => {
            const resultsDiv = document.getElementById('connectivityResults');
            const isLanMode = window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1';
            const backendHost = isLanMode ? window.location.hostname : '127.0.0.1';
            const backendUrl = `http://${backendHost}:8000`;

            log(`Testing CORS with origin: ${window.location.origin}`);

            try {
                const response = await fetch(`${backendUrl}/health`, {
                    method: 'GET',
                    mode: 'cors',
                    headers: {
                        'Origin': window.location.origin,
                        'Content-Type': 'application/json',
                    },
                });

                const corsHeaders = {
                    'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
                    'Access-Control-Allow-Credentials': response.headers.get('Access-Control-Allow-Credentials'),
                    'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
                };

                resultsDiv.innerHTML += `<div class="info">CORS Headers: ${JSON.stringify(corsHeaders, null, 2)}</div>`;
                log(`CORS test completed. Headers: ${JSON.stringify(corsHeaders)}`);
            } catch (error) {
                resultsDiv.innerHTML += `<div class="error">❌ CORS test failed: ${error.message}</div>`;
                log(`CORS test error: ${error.message}`);
            }
        };

        const testPorts = async () => {
            const resultsDiv = document.getElementById('connectivityResults');
            const isLanMode = window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1';
            const backendHost = isLanMode ? window.location.hostname : '127.0.0.1';

            const ports = [3000, 8000];
            const results = [];

            for (const port of ports) {
                try {
                    const response = await fetch(`http://${backendHost}:${port}/`, {
                        method: 'GET',
                        mode: 'no-cors',
                    });
                    results.push(`Port ${port}: ✅ Accessible`);
                    log(`Port ${port} test: accessible`);
                } catch (error) {
                    results.push(`Port ${port}: ❌ ${error.message}`);
                    log(`Port ${port} test: ${error.message}`);
                }
            }

            resultsDiv.innerHTML += `<div class="info">Port Tests:<br>${results.join('<br>')}</div>`;
        };

        // Authentication test
        const testLogin = async () => {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultsDiv = document.getElementById('authResults');
            
            resultsDiv.innerHTML = '<div class="info">Testing authentication...</div>';
            
            const isLanMode = window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1';
            const backendHost = isLanMode ? window.location.hostname : '127.0.0.1';
            const backendUrl = `http://${backendHost}:8000`;

            log(`Testing login for user: ${username}`);

            try {
                const response = await fetch(`${backendUrl}/api/v1/auth/token`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`
                });

                if (response.ok) {
                    const data = await response.json();
                    resultsDiv.innerHTML = `<div class="success">✅ Login successful! Token received (${data.access_token.substring(0, 20)}...)</div>`;
                    log(`Login test passed for user: ${username}`);
                } else {
                    const errorData = await response.json();
                    resultsDiv.innerHTML = `<div class="error">❌ Login failed: ${errorData.detail || response.statusText}</div>`;
                    log(`Login test failed: ${response.status} ${errorData.detail || response.statusText}`);
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">❌ Login request failed: ${error.message}</div>`;
                log(`Login test error: ${error.message}`);
            }
        };

        // Utility functions
        const clearLog = () => {
            debugLog.textContent = '';
            log('Debug log cleared');
        };

        const exportLog = () => {
            const logContent = debugLog.textContent;
            const blob = new Blob([logContent], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `xhs-debug-log-${new Date().toISOString().replace(/[:.]/g, '-')}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            log('Debug log exported');
        };

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            displaySystemInfo();
            log('Debug tool initialized');
        });
    </script>
</body>
</html>
