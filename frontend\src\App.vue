<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import AppLayout from '@/components/AppLayout.vue'

const route = useRoute()

// 不需要布局的页面（如登录页）
const noLayoutPages = ['/login']

const needsLayout = computed(() => {
  return !noLayoutPages.includes(route.path)
})
</script>

<template>
  <div id="app">
    <!-- 使用布局的页面 -->
    <AppLayout v-if="needsLayout">
      <router-view />
    </AppLayout>

    <!-- 不使用布局的页面（如登录页） -->
    <router-view v-else />
  </div>
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  width: 100%;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: #f7f8fa;
  overflow-x: hidden;
}

#app {
  width: 100%;
  min-height: 100vh;
}

/* 确保Vant组件不限制宽度 */
.van-nav-bar,
.van-cell-group,
.van-form {
  max-width: none !important;
}
</style>
