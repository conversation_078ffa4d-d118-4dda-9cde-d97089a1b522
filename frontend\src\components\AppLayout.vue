<template>
  <div class="app-layout">
    <!-- 左侧菜单栏 -->
    <div class="sidebar" :class="{ 'sidebar-collapsed': sidebarCollapsed }">
      <!-- Logo区域 -->
      <div class="sidebar-header">
        <div class="logo" @click="router.push('/')">
          <van-icon name="notes-o" />
          <span v-if="!sidebarCollapsed" class="logo-text">笔记管理</span>
        </div>
        <van-button 
          v-if="!isMobile"
          class="collapse-btn" 
          icon="arrow-left" 
          size="small"
          @click="toggleSidebar"
          :class="{ 'collapsed': sidebarCollapsed }"
        />
      </div>

      <!-- 用户信息 -->
      <div class="user-section" v-if="!sidebarCollapsed">
        <div class="user-card">
          <div class="user-avatar">
            <van-icon name="user-o" />
          </div>
          <div class="user-info">
            <div class="username">{{ authStore.user?.username }}</div>
            <van-tag
              size="medium"
              :type="authStore.user?.role === 'admin' ? 'primary' : 'success'"
            >
              {{ authStore.user?.role === 'admin' ? '管理员' : '普通用户' }}
            </van-tag>
          </div>
        </div>
      </div>

      <!-- 导航菜单 -->
      <nav class="sidebar-nav">
        <div class="nav-section">
          <div v-if="!sidebarCollapsed" class="nav-title">主要功能</div>
          
          <router-link 
            to="/" 
            class="nav-item"
            :class="{ active: $route.path === '/' }"
          >
            <van-icon name="wap-home-o" />
            <span v-if="!sidebarCollapsed">首页</span>
          </router-link>

          <router-link
            to="/products"
            class="nav-item"
            :class="{ active: $route.path === '/products' }"
          >
            <van-icon name="goods-collect-o" />
            <span v-if="!sidebarCollapsed">商品管理</span>
          </router-link>

          <router-link
            to="/notes/management"
            class="nav-item"
            :class="{ active: $route.path === '/notes/management' }"
          >
            <van-icon name="notes-o" />
            <span v-if="!sidebarCollapsed">笔记管理</span>
          </router-link>

          <router-link
            to="/prompts"
            class="nav-item"
            :class="{ active: $route.path.startsWith('/prompts') }"
          >
            <van-icon name="edit" />
            <span v-if="!sidebarCollapsed">AI提示词</span>
          </router-link>
        </div>

        <div class="nav-section">
          <div v-if="!sidebarCollapsed" class="nav-title">快捷操作</div>

          <router-link
            to="/products/create"
            class="nav-item quick-action"
            :class="{ active: $route.path === '/products/create' }"
          >
            <van-icon name="add-square" />
            <span v-if="!sidebarCollapsed">创建商品</span>
          </router-link>

          <router-link
            to="/notes/create"
            class="nav-item quick-action"
            :class="{ active: $route.path === '/notes/create' }"
          >
            <van-icon name="edit" />
            <span v-if="!sidebarCollapsed">创建笔记</span>
          </router-link>

          <router-link
            to="/notes/batch-create"
            class="nav-item quick-action"
            :class="{ active: $route.path === '/notes/batch-create' }"
          >
            <van-icon name="apps-o" />
            <span v-if="!sidebarCollapsed">批量创建笔记</span>
          </router-link>

          <router-link
            to="/notes/download"
            class="nav-item quick-action"
            :class="{ active: $route.path === '/notes/download' }"
          >
            <van-icon name="down" />
            <span v-if="!sidebarCollapsed">笔记下载</span>
          </router-link>

          <router-link
            to="/products/download"
            class="nav-item quick-action"
            :class="{ active: $route.path === '/products/download' }"
          >
            <van-icon name="photo-o" />
            <span v-if="!sidebarCollapsed">商品下载</span>
          </router-link>
        </div>

        <div class="nav-section" v-if="authStore.isAdmin">
          <div v-if="!sidebarCollapsed" class="nav-title">管理功能</div>

          <router-link
            to="/admin/users"
            class="nav-item"
            :class="{ active: $route.path.startsWith('/admin/users') }"
          >
            <van-icon name="friends-o" />
            <span v-if="!sidebarCollapsed">用户管理</span>
          </router-link>

          <router-link
            to="/recycle-bin"
            class="nav-item"
            :class="{ active: $route.path === '/recycle-bin' }"
          >
            <van-icon name="delete-o" />
            <span v-if="!sidebarCollapsed">回收站</span>
          </router-link>
        </div>

        <div class="nav-section">
          <div v-if="!sidebarCollapsed" class="nav-title">个人中心</div>
          
          <router-link 
            to="/profile" 
            class="nav-item"
            :class="{ active: $route.path === '/profile' }"
          >
            <van-icon name="user-o" />
            <span v-if="!sidebarCollapsed">个人资料</span>
          </router-link>

          <div class="nav-item logout-item" @click="handleLogout">
            <van-icon name="sign-out" />
            <span v-if="!sidebarCollapsed">退出登录</span>
          </div>
        </div>
      </nav>
    </div>

    <!-- 移动端遮罩 -->
    <div 
      v-if="isMobile && !sidebarCollapsed" 
      class="sidebar-overlay"
      @click="closeSidebar"
    ></div>

    <!-- 主内容区域 -->
    <div class="main-content" :class="{ 'sidebar-collapsed': sidebarCollapsed }">
      <!-- 移动端顶部栏 -->
      <div v-if="isMobile" class="mobile-header">
        <van-nav-bar
          :title="pageTitle"
          left-text="菜单"
          left-arrow
          @click-left="openSidebar"
        />
      </div>

      <!-- 页面内容 -->
      <div class="content-wrapper">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showConfirmDialog } from 'vant'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

const sidebarCollapsed = ref(false)
const isMobile = ref(false)

// 页面标题映射
const pageTitleMap: Record<string, string> = {
  '/': '首页',
  '/products': '商品管理',
  '/prompts': 'AI提示词',
  '/recycle-bin': '回收站',
  '/profile': '个人资料'
}

const pageTitle = computed(() => {
  const path = route.path
  for (const [key, title] of Object.entries(pageTitleMap)) {
    if (path.startsWith(key)) {
      return title
    }
  }
  return '笔记管理系统'
})

// 检测移动端
const checkMobile = () => {
  isMobile.value = window.innerWidth < 768
  if (isMobile.value) {
    sidebarCollapsed.value = true
  }
}

// 切换侧边栏
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

const openSidebar = () => {
  sidebarCollapsed.value = false
}

const closeSidebar = () => {
  if (isMobile.value) {
    sidebarCollapsed.value = true
  }
}

// 退出登录
const handleLogout = async () => {
  try {
    await showConfirmDialog({
      title: '确认退出',
      message: '确定要退出登录吗？'
    })
    
    authStore.logout()
    router.push('/login')
  } catch (error) {
    // 用户取消
  }
}

onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<style scoped>
.app-layout {
  display: flex;
  height: 100vh;
  overflow: hidden;
}

/* 侧边栏样式 */
.sidebar {
  width: 260px;
  background: #001529;
  color: white;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1000;
}

.sidebar-collapsed {
  width: 64px;
}

.sidebar-header {
  padding: 16px;
  border-bottom: 1px solid #1f1f1f;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  color: #1890ff;
  font-weight: bold;
  font-size: 18px;
}

.logo .van-icon {
  font-size: 24px;
}

.logo-text {
  white-space: nowrap;
}

.collapse-btn {
  background: transparent;
  border: 1px solid #434343;
  color: #fff;
  transition: transform 0.3s ease;
}

.collapse-btn.collapsed {
  transform: rotate(180deg);
}

.user-section {
  padding: 16px;
  border-bottom: 1px solid #1f1f1f;
}

.user-card {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  width: 40px;
  height: 40px;
  background: #1890ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.user-info {
  flex: 1;
  min-width: 0;
}

.username {
  font-weight: 500;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 导航菜单 */
.sidebar-nav {
  flex: 1;
  padding: 16px 0;
  overflow-y: auto;
}

.nav-section {
  margin-bottom: 24px;
}

.nav-title {
  padding: 0 16px 8px;
  font-size: 12px;
  color: #8c8c8c;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  color: #d9d9d9;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
}

.nav-item:hover {
  background: #1f1f1f;
  color: #1890ff;
}

.nav-item.active {
  background: #1890ff;
  color: white;
}

.nav-item .van-icon {
  font-size: 18px;
  min-width: 18px;
}

.nav-item.quick-action {
  background: rgba(24, 144, 255, 0.1);
  border-left: 3px solid #1890ff;
}

.nav-item.quick-action:hover {
  background: rgba(24, 144, 255, 0.2);
}

.logout-item {
  color: #ff4d4f;
}

.logout-item:hover {
  background: #2a1215;
  color: #ff7875;
}

/* 移动端遮罩 */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
}

/* 主内容区域 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: all 0.3s ease;
}

.mobile-header {
  display: none;
}

.content-wrapper {
  flex: 1;
  overflow-y: auto;
  background: #f7f8fa;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    transform: translateX(-100%);
    z-index: 1001;
  }

  .sidebar:not(.sidebar-collapsed) {
    transform: translateX(0);
  }

  .main-content {
    width: 100%;
  }

  .mobile-header {
    display: block;
  }

  .sidebar-collapsed .main-content {
    margin-left: 0;
  }
}

/* 桌面端侧边栏收起时的布局调整 */
@media (min-width: 769px) {
  .sidebar-collapsed + .main-content {
    margin-left: 0;
  }
}
</style>
