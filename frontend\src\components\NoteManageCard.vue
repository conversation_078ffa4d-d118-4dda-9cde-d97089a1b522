<template>
  <div class="note-manage-card">
    <div class="card-container">
      <!-- 缩略图 -->
      <div class="card-thumb">
        <van-image
          v-if="noteThumb"
          :src="noteThumb"
          width="100"
          height="100"
          fit="cover"
          radius="8"
        />
        <div v-else class="thumb-placeholder">
          <van-icon name="photo-o" size="32" color="#c8c9cc" />
        </div>
      </div>
      
      <!-- 内容区域 -->
      <div class="card-content">
        <div class="card-header">
          <h3 class="card-title">{{ note?.title || '无标题' }}</h3>
          <div class="card-badges">
            <span class="usage-status" :class="usageStatusClass">
              {{ usageStatusText }}
            </span>
            <span v-if="note?.product_name" class="product-badge">
              {{ note.product_name }}
            </span>
          </div>
        </div>
        
        <p class="card-desc">{{ noteDescription }}</p>
        
        <div class="card-meta">
          <span class="meta-item">
            <van-icon name="clock-o" size="14" />
            {{ formatDate(note?.created_at) }}
          </span>
          <span v-if="note?.tags" class="meta-item">
            <van-icon name="label-o" size="14" />
            {{ note.tags }}
          </span>
        </div>
        
        <!-- 图片预览 -->
        <div v-if="noteImages.length > 0" class="images-preview">
          <van-image
            v-for="(image, index) in noteImages.slice(0, 4)"
            :key="`image-${note?.id || 'unknown'}-${index}`"
            :src="image"
            width="40"
            height="40"
            fit="cover"
            radius="4"
            @click="previewImages(index)"
          />
          <div 
            v-if="noteImages.length > 4" 
            class="more-images"
            @click="previewImages(0)"
          >
            +{{ noteImages.length - 4 }}
          </div>
        </div>
        
        <!-- 视频预览 -->
        <div v-if="note?.videos && note.videos.length > 0" class="videos-preview">
          <span class="videos-count">
            <van-icon name="video-o" size="14" />
            {{ note.videos.length }}个视频
          </span>
        </div>
      </div>
      
      <!-- 操作按钮 -->
      <div class="card-actions">
        <van-button 
          size="small" 
          type="primary" 
          icon="edit"
          @click="editNote"
        >
          编辑
        </van-button>
        
        <van-button
          size="small"
          type="default"
          icon="eye-o"
          @click="viewDetail"
        >
          详情
        </van-button>
        
        <van-button
          size="small"
          :type="note?.usage_status === 'UNUSED' ? 'warning' : 'default'"
          :icon="note?.usage_status === 'UNUSED' ? 'success' : 'revoke'"
          @click="toggleUsageStatus"
        >
          {{ note?.usage_status === 'UNUSED' ? '标记已用' : '标记未用' }}
        </van-button>
        
        <van-button 
          size="small" 
          type="danger" 
          icon="delete-o"
          @click="deleteNote"
        >
          删除
        </van-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showImagePreview, showConfirmDialog } from 'vant'
import { buildImageUrl, buildImageUrls } from '@/utils/downloadUtils'

interface Note {
  id: number
  title: string
  body?: string
  tags?: string
  cover_images_candidate?: string[]
  selected_cover_image?: string
  note_images?: string[]
  videos?: string[]
  usage_status: 'UNUSED' | 'USED'
  created_at: string
  product_id?: number
  product_name?: string
}

interface Props {
  note: Note
}

interface Emits {
  (e: 'delete', id: number): void
  (e: 'update-usage-status', id: number, status: string): void
  (e: 'refresh'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()
const router = useRouter()

const noteDescription = computed(() => {
  if (!props.note?.body) return '暂无内容'
  
  const text = props.note.body.replace(/\n/g, ' ').trim()
  return text.length > 100 ? text.substring(0, 100) + '...' : text
})

const noteThumb = computed(() => {
  if (!props.note) return ''

  if (props.note.selected_cover_image) {
    return buildImageUrl(props.note.selected_cover_image)
  }

  if (props.note.cover_images_candidate && props.note.cover_images_candidate.length > 0) {
    return buildImageUrl(props.note.cover_images_candidate[0])
  }

  if (props.note.note_images && props.note.note_images.length > 0) {
    return buildImageUrl(props.note.note_images[0])
  }

  return ''
})

const usageStatusText = computed(() => {
  return props.note?.usage_status === 'UNUSED' ? '未使用' : '已使用'
})

const usageStatusClass = computed(() => {
  return props.note?.usage_status === 'UNUSED' ? 'status-unused' : 'status-used'
})

const noteImages = computed(() => {
  const images = props.note?.note_images || []
  // 确保返回的是字符串数组，而不是对象数组
  if (Array.isArray(images)) {
    const validImages = images.map((img, index) => {
      if (typeof img === 'string') {
        return img
      } else if (img && typeof img === 'object' && 'url' in img && (img as any).url) {
        return (img as any).url
      } else {
        return `placeholder-${index}`
      }
    }).filter(Boolean)

    return buildImageUrls(validImages)
  }
  return []
})

const formatDate = (dateString?: string) => {
  if (!dateString) return '未知时间'
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const editNote = () => {
  if (props.note?.id) {
    router.push(`/notes/${props.note.id}/edit`)
  }
}

const viewDetail = () => {
  if (props.note?.id) {
    router.push(`/notes/${props.note.id}/detail`)
  }
}

const toggleUsageStatus = () => {
  if (!props.note?.id) return
  const newStatus = props.note.usage_status === 'UNUSED' ? 'USED' : 'UNUSED'
  emit('update-usage-status', props.note.id, newStatus)
}

const deleteNote = async () => {
  try {
    await showConfirmDialog({
      title: '确认删除',
      message: `确定要删除笔记"${props.note?.title}"吗？此操作不可恢复。`,
    })
    
    if (props.note?.id) {
      emit('delete', props.note.id)
    }
  } catch {
    // 用户取消删除
  }
}

const previewImages = (startIndex: number = 0) => {
  if (noteImages.value.length === 0) return
  
  showImagePreview({
    images: noteImages.value,
    startPosition: startIndex,
    closeable: true
  })
}
</script>

<style scoped>
.note-manage-card {
  width: 100%;
  margin-bottom: 16px;
}

.card-container {
  display: flex;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  padding: 20px;
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
}

.card-container:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.card-thumb {
  flex-shrink: 0;
  margin-right: 20px;
}

.thumb-placeholder {
  width: 100px;
  height: 100px;
  background: #f7f8fa;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-content {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 16px;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #323233;
  margin: 0;
  line-height: 1.4;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.card-badges {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
  flex-direction: column;
  align-items: flex-end;
}

.usage-status {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
}

.status-unused {
  background: #e8f4fd;
  color: #1989fa;
}

.status-used {
  background: #fee;
  color: #ee0a24;
}

.product-badge {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  background: #e8f8e8;
  color: #07c160;
}

.card-desc {
  font-size: 14px;
  color: #646566;
  line-height: 1.5;
  margin: 0;
}

.card-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #969799;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.images-preview {
  display: flex;
  gap: 8px;
  align-items: center;
}

.more-images {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  background: #f7f8fa;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #969799;
  font-size: 11px;
  cursor: pointer;
  border: 1px dashed #dcdee0;
}

.videos-preview {
  font-size: 12px;
  color: #646566;
}

.videos-count {
  display: flex;
  align-items: center;
  gap: 4px;
}

.card-actions {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-left: 20px;
  min-width: 80px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .card-container {
    padding: 16px;
    flex-direction: column;
  }
  
  .card-thumb {
    margin-right: 0;
    margin-bottom: 16px;
    align-self: center;
  }
  
  .card-actions {
    margin-left: 0;
    margin-top: 16px;
    flex-direction: row;
    flex-wrap: wrap;
    min-width: auto;
  }
  
  .card-header {
    flex-direction: column;
    gap: 8px;
  }
  
  .card-badges {
    align-self: flex-start;
    flex-direction: row;
  }
}

@media (max-width: 480px) {
  .card-container {
    padding: 12px;
  }
  
  .card-thumb .van-image,
  .thumb-placeholder {
    width: 80px;
    height: 80px;
  }
  
  .card-title {
    font-size: 16px;
  }
  
  .card-meta {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
