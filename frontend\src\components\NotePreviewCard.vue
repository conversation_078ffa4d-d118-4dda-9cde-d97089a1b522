<template>
  <div class="note-preview-card">
    <div class="card-container" @click="viewDetail">
      <!-- 缩略图 -->
      <div class="card-thumb">
        <van-image
          v-if="noteThumb"
          :src="noteThumb"
          width="70"
          height="70"
          fit="cover"
          radius="8"
        />
        <div v-else class="thumb-placeholder">
          <van-icon name="photo-o" size="18" color="#c8c9cc" />
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="card-content">
        <div class="card-header">
          <h3 class="card-title">{{ note?.title || '无标题' }}</h3>
          <span class="usage-status" :class="usageStatusClass">
            {{ usageStatusText }}
          </span>
        </div>

        <!-- 商品名称突出显示 -->
        <div v-if="productName" class="product-info">
          <van-icon name="shop-o" size="14" color="#07c160" />
          <span class="product-name">{{ productName }}</span>
        </div>

        <!-- 内容状态指示器 -->
        <div class="content-indicators">
          <van-icon
            v-if="hasContent"
            name="notes-o"
            color="#1989fa"
            size="14"
            title="有内容"
          />
          <van-icon
            v-if="hasCoverImage"
            name="photo-o"
            color="#52c41a"
            size="14"
            title="有封面图"
          />
          <van-icon
            v-if="hasNoteImages"
            name="photograph"
            color="#ff976a"
            size="14"
            title="有笔记图"
          />
          <van-icon
            v-if="hasVideos"
            name="video-o"
            color="#722ed1"
            size="14"
            title="有视频"
          />
        </div>

        <p class="card-desc">{{ noteDescription }}</p>

        <div class="card-tags">
          <van-tag v-if="note?.tags" type="primary" size="medium">
            {{ note.tags }}
          </van-tag>
        </div>

        <!-- 图片预览 -->
        <div v-if="noteImages.length > 0" class="images-preview">
          <van-image
            v-for="(image, index) in noteImages.slice(0, 3)"
            :key="index"
            :src="image"
            width="24"
            height="24"
            fit="cover"
            radius="3"
            @click.stop="previewImages(index)"
          />
          <div
            v-if="noteImages.length > 3"
            class="more-images"
            @click.stop="previewImages(0)"
          >
            +{{ noteImages.length - 3 }}
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="card-actions">
        <van-button
          size="mini"
          type="default"
          icon="eye-o"
          @click.stop="viewDetail"
        />

        <van-button
          size="mini"
          :type="note.usage_status === 'UNUSED' ? 'default' : 'danger'"
          :icon="note.usage_status === 'UNUSED' ? 'success' : 'revoke'"
          @click.stop="toggleUsageStatus"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showImagePreview } from 'vant'
import { buildImageUrl, buildImageUrls } from '@/utils/downloadUtils'

interface Note {
  id: number
  title: string
  body?: string
  tags?: string
  cover_images_candidate?: string[]
  selected_cover_image?: string
  note_images?: string[]
  videos?: string[]
  usage_status: 'UNUSED' | 'USED'
  created_at: string
  product_id?: number
  product_title?: string
  product_short_name?: string
  product_name?: string
}

interface Props {
  note: Note
}

interface Emits {
  (e: 'update-usage-status', noteId: number, status: 'UNUSED' | 'USED'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()
const router = useRouter()

const noteDescription = computed(() => {
  if (!props.note?.body) return '暂无内容'

  // 截取前50个字符作为描述
  const text = props.note.body.replace(/\n/g, ' ').trim()
  return text.length > 50 ? text.substring(0, 50) + '...' : text
})

const noteThumb = computed(() => {
  if (!props.note) return ''

  // 优先使用选中的封面图片
  if (props.note.selected_cover_image) {
    return buildImageUrl(props.note.selected_cover_image)
  }

  // 其次使用候选封面图片的第一张
  if (props.note.cover_images_candidate && props.note.cover_images_candidate.length > 0) {
    return buildImageUrl(props.note.cover_images_candidate[0])
  }

  // 最后使用笔记图片的第一张
  if (props.note.note_images && props.note.note_images.length > 0) {
    return buildImageUrl(props.note.note_images[0])
  }

  return ''
})

const usageStatusText = computed(() => {
  return props.note?.usage_status === 'UNUSED' ? '未使用' : '已使用'
})

const usageStatusClass = computed(() => {
  return props.note?.usage_status === 'UNUSED' ? 'status-unused' : 'status-used'
})

const noteImages = computed(() => {
  if (!props.note?.note_images) return []
  return buildImageUrls(props.note.note_images)
})

const productName = computed(() => {
  if (!props.note) return ''

  // 优先使用 product_title，其次使用 product_short_name，最后使用 product_name
  return props.note.product_title || props.note.product_short_name || props.note.product_name || ''
})

// 内容状态判断
const hasContent = computed(() => {
  return props.note?.body && props.note.body.trim().length > 0
})

const hasCoverImage = computed(() => {
  return props.note?.selected_cover_image ||
         (props.note?.cover_images_candidate && props.note.cover_images_candidate.length > 0)
})

const hasNoteImages = computed(() => {
  return props.note?.note_images && props.note.note_images.length > 0
})

const hasVideos = computed(() => {
  return props.note?.videos && props.note.videos.length > 0
})


const previewImages = (startIndex: number = 0) => {
  if (!props.note.note_images || props.note.note_images.length === 0) return
  
  showImagePreview({
    images: props.note.note_images,
    startPosition: startIndex,
    closeable: true
  })
}

const viewDetail = () => {
  if (props.note?.id) {
    router.push(`/notes/${props.note.id}/detail`)
  }
}

const viewProduct = () => {
  if (props.note?.product_id) {
    router.push(`/products/${props.note.product_id}/detail`)
  }
}

const toggleUsageStatus = () => {
  if (!props.note?.id) return
  const newStatus = props.note.usage_status === 'UNUSED' ? 'USED' : 'UNUSED'
  emit('update-usage-status', props.note.id, newStatus)
}
</script>

<style scoped>
.note-preview-card {
  width: 100%;
  margin-bottom: 8px;
}

.card-container {
  display: flex;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
  min-height: 100px;
}

.card-container:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.card-thumb {
  flex-shrink: 0;
  margin-right: 16px;
}

.thumb-placeholder {
  width: 70px;
  height: 70px;
  background: #f7f8fa;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-content {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 8px;
}

.card-title {
  font-size: 15px;
  font-weight: 600;
  color: #323233;
  margin: 0;
  line-height: 1.4;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.usage-status {
  flex-shrink: 0;
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 10px;
  font-weight: 500;
}

.status-unused {
  background: #e8f4fd;
  color: #1989fa;
}

.status-used {
  background: #fee;
  color: #ee0a24;
}

.product-info {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 10px;
  background: #f0f9ff;
  border: 1px solid #e1f5fe;
  border-radius: 6px;
  margin: 2px 0;
}

.product-name {
  font-size: 13px;
  font-weight: 500;
  color: #07c160;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.content-indicators {
  display: flex;
  align-items: center;
  gap: 6px;
  margin: 6px 0;
  padding: 4px 0;
}

.content-indicators .van-icon {
  cursor: help;
  transition: transform 0.2s ease;
}

.content-indicators .van-icon:hover {
  transform: scale(1.1);
}

.card-desc {
  font-size: 13px;
  color: #646566;
  line-height: 1.5;
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.card-tags {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.images-preview {
  display: flex;
  gap: 3px;
  align-items: center;
}

.more-images {
  width: 24px;
  height: 24px;
  border-radius: 3px;
  background: #f7f8fa;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #969799;
  font-size: 9px;
  cursor: pointer;
  border: 1px dashed #dcdee0;
}

.card-actions {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-left: 8px;
}

:deep(.van-tag) {
  cursor: pointer;
}

:deep(.van-tag:hover) {
  opacity: 0.8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .card-container {
    padding: 14px;
  }

  .card-thumb {
    margin-right: 14px;
  }

  .card-thumb .van-image,
  .thumb-placeholder {
    width: 65px;
    height: 65px;
  }

  .card-title {
    font-size: 14px;
  }

  .card-desc {
    font-size: 12px;
  }

  .product-name {
    font-size: 12px;
  }

  .card-actions {
    margin-left: 14px;
  }
}

@media (max-width: 480px) {
  .card-container {
    padding: 12px;
  }

  .card-thumb {
    margin-right: 12px;
  }

  .card-thumb .van-image,
  .thumb-placeholder {
    width: 55px;
    height: 55px;
  }

  .card-header {
    gap: 8px;
  }

  .card-title {
    font-size: 13px;
  }

  .card-desc {
    font-size: 11px;
  }

  .product-name {
    font-size: 11px;
  }

  .card-actions {
    margin-left: 10px;
  }
}
</style>
