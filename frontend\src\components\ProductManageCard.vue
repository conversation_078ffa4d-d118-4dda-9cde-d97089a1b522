<template>
  <div class="product-manage-card">
    <div class="card-container">
      <!-- 缩略图 -->
      <div class="card-thumb">
        <van-image
          v-if="productThumb"
          :src="productThumb"
          width="100"
          height="100"
          fit="cover"
          radius="8"
        />
        <div v-else class="thumb-placeholder">
          <van-icon name="photo-o" size="32" color="#c8c9cc" />
        </div>
      </div>
      
      <!-- 内容区域 -->
      <div class="card-content">
        <div class="card-header">
          <h3 class="card-title">{{ product?.title || product?.short_name || '无标题' }}</h3>
          <div class="card-badges">
            <span v-if="product?.price" class="price-badge">
              ¥{{ product.price }}
            </span>
            <div class="notes-stats">
              <span class="notes-badge total">
                总计{{ product?.notes_count || 0 }}篇
              </span>
              <span class="notes-badge used">
                已用{{ product?.used_notes_count || 0 }}篇
              </span>
              <span class="notes-badge unused">
                待用{{ product?.unused_notes_count || 0 }}篇
              </span>
            </div>
          </div>
        </div>
        
        <p class="card-desc">{{ productDescription }}</p>
        
        <div class="card-meta">
          <span class="meta-item">
            <van-icon name="user-o" size="14" />
            {{ product?.owner_username || '未知用户' }}
          </span>
          <span class="meta-item">
            <van-icon name="clock-o" size="14" />
            {{ formatDate(product?.created_at) }}
          </span>
        </div>

        <!-- 图片预览 -->
        <div v-if="allImages.length > 0" class="images-preview">
          <van-image
            v-for="(image, index) in allImages.slice(0, 4)"
            :key="index"
            :src="image"
            width="40"
            height="40"
            fit="cover"
            radius="4"
            @click="previewImages(index)"
          />
          <div 
            v-if="allImages.length > 4" 
            class="more-images"
            @click="previewImages(0)"
          >
            +{{ allImages.length - 4 }}
          </div>
        </div>
      </div>
      
      <!-- 操作按钮 -->
      <div class="card-actions">
        <van-button 
          size="small" 
          type="primary" 
          icon="edit"
          @click="editProduct"
        >
          编辑
        </van-button>
        
        <van-button
          size="small"
          type="default"
          icon="eye-o"
          @click="viewDetail"
        >
          详情
        </van-button>
        
        <van-button 
          size="small" 
          type="warning" 
          icon="notes-o"
          @click="manageNotes"
        >
          笔记
        </van-button>
        
        <van-button 
          size="small" 
          type="danger" 
          icon="delete-o"
          @click="deleteProduct"
        >
          删除
        </van-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showImagePreview, showConfirmDialog } from 'vant'
import { buildImageUrl, buildImageUrls } from '@/utils/downloadUtils'

interface Product {
  id: number
  short_name: string
  title?: string
  description?: string
  price?: number
  feature_analysis?: string
  main_images?: string[]
  detail_images?: string[]
  owner_username?: string
  notes_count?: number
  unused_notes_count?: number
  used_notes_count?: number
  created_at: string
}

interface Props {
  product: Product
}

interface Emits {
  (e: 'delete', id: number): void
  (e: 'refresh'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()
const router = useRouter()

const productDescription = computed(() => {
  if (!props.product?.description) return '暂无描述'
  
  const text = props.product.description.replace(/\n/g, ' ').trim()
  return text.length > 80 ? text.substring(0, 80) + '...' : text
})

const productThumb = computed(() => {
  if (!props.product) return ''

  if (props.product.main_images && props.product.main_images.length > 0) {
    return buildImageUrl(props.product.main_images[0])
  }

  if (props.product.detail_images && props.product.detail_images.length > 0) {
    return buildImageUrl(props.product.detail_images[0])
  }

  return ''
})

const allImages = computed(() => {
  if (!props.product) return []

  const images = []
  if (props.product.main_images) {
    images.push(...buildImageUrls(props.product.main_images))
  }
  if (props.product.detail_images) {
    images.push(...buildImageUrls(props.product.detail_images))
  }
  return images
})

const formatDate = (dateString?: string) => {
  if (!dateString) return '未知时间'
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const editProduct = () => {
  if (props.product?.id) {
    router.push(`/products/${props.product.id}/edit`)
  }
}

const viewDetail = () => {
  if (props.product?.id) {
    router.push(`/products/${props.product.id}/detail`)
  }
}

const manageNotes = () => {
  if (props.product?.id) {
    router.push(`/products/${props.product.id}/notes`)
  }
}

const deleteProduct = async () => {
  try {
    await showConfirmDialog({
      title: '确认删除',
      message: `确定要删除商品"${props.product?.title || props.product?.short_name}"吗？此操作不可恢复。`,
    })
    
    if (props.product?.id) {
      emit('delete', props.product.id)
    }
  } catch {
    // 用户取消删除
  }
}

const previewImages = (startIndex: number = 0) => {
  if (allImages.value.length === 0) return
  
  showImagePreview({
    images: allImages.value,
    startPosition: startIndex,
    closeable: true
  })
}
</script>

<style scoped>
.product-manage-card {
  width: 100%;
  margin-bottom: 16px;
}

.card-container {
  display: flex;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  padding: 20px;
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
}

.card-container:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.card-thumb {
  flex-shrink: 0;
  margin-right: 20px;
}

.thumb-placeholder {
  width: 100px;
  height: 100px;
  background: #f7f8fa;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-content {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 16px;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #323233;
  margin: 0;
  line-height: 1.4;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.card-badges {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.price-badge {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 14px;
  font-weight: 600;
  background: #e8f8e8;
  color: #07c160;
}

.notes-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.notes-badge {
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 11px;
  font-weight: 500;
  white-space: nowrap;
}

.notes-badge.total {
  background: #f0f0f0;
  color: #646566;
}

.notes-badge.used {
  background: #e8f5e8;
  color: #52c41a;
}

.notes-badge.unused {
  background: #fff7e6;
  color: #fa8c16;
}

.card-desc {
  font-size: 14px;
  color: #646566;
  line-height: 1.5;
  margin: 0;
}

.card-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #969799;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.images-preview {
  display: flex;
  gap: 8px;
  align-items: center;
}

.more-images {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  background: #f7f8fa;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #969799;
  font-size: 11px;
  cursor: pointer;
  border: 1px dashed #dcdee0;
}

.card-actions {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-left: 20px;
  min-width: 80px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .card-container {
    padding: 16px;
    flex-direction: column;
  }
  
  .card-thumb {
    margin-right: 0;
    margin-bottom: 16px;
    align-self: center;
  }
  
  .card-actions {
    margin-left: 0;
    margin-top: 16px;
    flex-direction: row;
    flex-wrap: wrap;
    min-width: auto;
  }
  
  .card-header {
    flex-direction: column;
    gap: 8px;
  }
  
  .card-badges {
    align-self: flex-start;
  }
}

@media (max-width: 480px) {
  .card-container {
    padding: 12px;
  }
  
  .card-thumb .van-image,
  .thumb-placeholder {
    width: 80px;
    height: 80px;
  }
  
  .card-title {
    font-size: 16px;
  }
  
  .card-meta {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
