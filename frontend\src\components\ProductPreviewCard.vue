<template>
  <div class="product-preview-card">
    <div class="card-container" @click="viewDetail">
      <!-- 缩略图 -->
      <div class="card-thumb">
        <van-image
          v-if="productThumb"
          :src="productThumb"
          width="80"
          height="80"
          fit="cover"
          radius="8"
        />
        <div v-else class="thumb-placeholder">
          <van-icon name="photo-o" size="24" color="#c8c9cc" />
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="card-content">
        <div class="card-header">
          <h3 class="card-title">{{ product?.title || product?.short_name || '无标题' }}</h3>
          <span v-if="product?.price" class="price-tag">
            ¥{{ product.price }}
          </span>
        </div>

        <p class="card-desc">{{ productDescription }}</p>

        <div class="card-tags">
          <van-tag v-if="product?.owner_username" type="warning" size="medium">
            归属: {{ product.owner_username }}
          </van-tag>
          <van-tag type="primary" size="medium">
            {{ notesCountTag }}
          </van-tag>
        </div>

        <!-- 图片预览 -->
        <div v-if="allImages.length > 0" class="images-preview">
          <van-image
            v-for="(image, index) in allImages.slice(0, 3)"
            :key="index"
            :src="image"
            width="32"
            height="32"
            fit="cover"
            radius="4"
            @click.stop="previewImages(index)"
          />
          <div
            v-if="allImages.length > 3"
            class="more-images"
            @click.stop="previewImages(0)"
          >
            +{{ allImages.length - 3 }}
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="card-actions">
        <van-button
          v-if="product.feature_analysis"
          size="mini"
          type="warning"
          icon="notes-o"
          @click.stop="copyFeatureAnalysis"
        />

        <van-button
          size="mini"
          type="default"
          icon="eye-o"
          @click.stop="viewDetail"
        />

        <van-button
          v-if="hasImages"
          size="mini"
          type="default"
          icon="down"
          @click.stop="downloadAllImages"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showImagePreview } from 'vant'
import { downloadMultipleImagesWithTimestamp, buildImageUrl, buildImageUrls } from '@/utils/downloadUtils'
import { copyWithToast } from '@/utils/clipboard'

interface Product {
  id: number
  short_name: string
  title?: string
  description?: string
  price?: number
  feature_analysis?: string
  main_images?: string[]
  detail_images?: string[]
  owner_username?: string
  notes_count?: number
  created_at: string
}

interface Props {
  product: Product
}

const props = defineProps<Props>()
const router = useRouter()

const productDescription = computed(() => {
  if (!props.product?.description) return '暂无描述'

  // 截取前50个字符作为描述
  const text = props.product.description.replace(/\n/g, ' ').trim()
  return text.length > 50 ? text.substring(0, 50) + '...' : text
})

const productThumb = computed(() => {
  if (!props.product) return ''

  // 优先使用主图的第一张
  if (props.product.main_images && props.product.main_images.length > 0) {
    return buildImageUrl(props.product.main_images[0])
  }

  // 其次使用详情图的第一张
  if (props.product.detail_images && props.product.detail_images.length > 0) {
    return buildImageUrl(props.product.detail_images[0])
  }

  return ''
})

const notesCountTag = computed(() => {
  const count = props.product?.notes_count || 0
  return `${count}篇笔记`
})

const allImages = computed(() => {
  if (!props.product) return []

  const images = []
  if (props.product.main_images) {
    images.push(...buildImageUrls(props.product.main_images))
  }
  if (props.product.detail_images) {
    images.push(...buildImageUrls(props.product.detail_images))
  }
  return images
})

const hasImages = computed(() => {
  return allImages.value.length > 0
})



const copyFeatureAnalysis = async () => {
  const analysis = props.product?.feature_analysis || ''
  if (!analysis) {
    showToast('特点分析为空')
    return
  }
  await copyWithToast(analysis, showToast, showToast)
}

const viewDetail = () => {
  if (props.product?.id) {
    router.push(`/products/${props.product.id}/download-detail`)
  }
}

const downloadAllImages = () => {
  if (allImages.value.length === 0) {
    showToast('没有可下载的图片')
    return
  }

  const baseNamePrefix = `product_${props.product?.id || 'unknown'}_image`
  downloadMultipleImagesWithTimestamp(allImages.value, baseNamePrefix, showToast)
}

const previewImages = (startIndex: number = 0) => {
  if (allImages.value.length === 0) return
  
  showImagePreview({
    images: allImages.value,
    startPosition: startIndex,
    closeable: true
  })
}
</script>

<style scoped>
.product-preview-card {
  width: 100%;
  margin-bottom: 12px;
}

.card-container {
  display: flex;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
}

.card-container:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.card-thumb {
  flex-shrink: 0;
  margin-right: 16px;
}

.thumb-placeholder {
  width: 80px;
  height: 80px;
  background: #f7f8fa;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-content {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 12px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
  margin: 0;
  line-height: 1.4;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.price-tag {
  flex-shrink: 0;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  background: #e8f8e8;
  color: #07c160;
}

.card-desc {
  font-size: 14px;
  color: #646566;
  line-height: 1.4;
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.card-tags {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.images-preview {
  display: flex;
  gap: 6px;
  align-items: center;
}

.more-images {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  background: #f7f8fa;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #969799;
  font-size: 10px;
  cursor: pointer;
  border: 1px dashed #dcdee0;
}

.card-actions {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-left: 16px;
}

:deep(.van-tag) {
  cursor: pointer;
}

:deep(.van-tag:hover) {
  opacity: 0.8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .card-container {
    padding: 12px;
  }

  .card-thumb {
    margin-right: 12px;
  }

  .card-thumb .van-image,
  .thumb-placeholder {
    width: 60px;
    height: 60px;
  }

  .card-title {
    font-size: 14px;
  }

  .card-desc {
    font-size: 12px;
  }

  .card-actions {
    margin-left: 12px;
  }
}

@media (max-width: 480px) {
  .card-container {
    padding: 10px;
  }

  .card-thumb {
    margin-right: 10px;
  }

  .card-thumb .van-image,
  .thumb-placeholder {
    width: 50px;
    height: 50px;
  }

  .card-header {
    gap: 8px;
  }

  .card-title {
    font-size: 13px;
  }

  .card-desc {
    font-size: 11px;
  }

  .card-actions {
    margin-left: 8px;
  }
}
</style>
