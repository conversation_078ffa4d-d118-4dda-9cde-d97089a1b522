import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'login',
      component: () => import('@/views/LoginView.vue'),
      meta: { requiresAuth: false }
    },
    {
      path: '/debug',
      name: 'debug',
      component: () => import('@/views/DebugView.vue'),
      meta: { requiresAuth: false }
    },
    {
      path: '/',
      name: 'home',
      component: () => import('@/views/HomeView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/products',
      name: 'products',
      component: () => import('@/views/ProductListView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/products/create',
      name: 'product-create',
      component: () => import('@/views/ProductFormView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/products/:id/edit',
      name: 'product-edit',
      component: () => import('@/views/ProductFormView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/products/:id/detail',
      name: 'product-detail',
      component: () => import('@/views/NoteListView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/products/:productId/notes',
      name: 'product-notes',
      component: () => import('@/views/NoteListView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/notes/management',
      name: 'note-management',
      component: () => import('@/views/NoteManagementView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/products/:productId/notes/create',
      name: 'note-create',
      component: () => import('@/views/NoteFormView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/notes/:id/edit',
      name: 'note-edit',
      component: () => import('@/views/NoteFormView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/notes/create',
      name: 'note-create-standalone',
      component: () => import('@/views/NoteCreateView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/notes/batch-create',
      name: 'note-batch-create',
      component: () => import('@/views/NoteBatchCreateView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/notes/download',
      name: 'notes-download',
      component: () => import('@/views/NotesDownloadView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/notes/:id/detail',
      name: 'note-detail',
      component: () => import('@/views/NoteDetailView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/products/download',
      name: 'products-download',
      component: () => import('@/views/ProductsDownloadView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/products/:id/download-detail',
      name: 'product-download-detail',
      component: () => import('@/views/ProductDownloadDetailView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/prompts',
      name: 'prompts',
      component: () => import('@/views/PromptListView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/prompts/create',
      name: 'prompt-create',
      component: () => import('@/views/PromptFormView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/prompts/:id/edit',
      name: 'prompt-edit',
      component: () => import('@/views/PromptFormView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/profile',
      name: 'profile',
      component: () => import('@/views/ProfileView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/recycle-bin',
      name: 'recycle-bin',
      component: () => import('@/views/RecycleBinView.vue'),
      meta: { requiresAuth: true, requiresAdmin: true }
    },
    {
      path: '/admin/users',
      name: 'user-management',
      component: () => import('@/views/UserManagementView.vue'),
      meta: { requiresAuth: true, requiresAdmin: true }
    },
    {
      path: '/admin/users/create',
      name: 'user-create',
      component: () => import('@/views/UserFormView.vue'),
      meta: { requiresAuth: true, requiresAdmin: true }
    },
    {
      path: '/admin/users/:id/edit',
      name: 'user-edit',
      component: () => import('@/views/UserFormView.vue'),
      meta: { requiresAuth: true, requiresAdmin: true }
    }
  ]
})

// Navigation guard
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()

  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    next('/login')
  } else if (to.path === '/login' && authStore.isAuthenticated) {
    next('/')
  } else if (to.meta.requiresAdmin && authStore.user?.role !== 'admin') {
    // 需要管理员权限但用户不是管理员
    next('/')
  } else {
    next()
  }
})

export default router
