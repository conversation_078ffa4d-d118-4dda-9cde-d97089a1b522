import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { showToast } from 'vant'
import api, { networkDiagnostics, testLogin } from '@/utils/api'

export interface User {
  id: number
  username: string
  role: 'admin' | 'user'
}

export const useAuthStore = defineStore('auth', () => {
  const token = ref<string | null>(localStorage.getItem('token'))
  const user = ref<User | null>(null)

  const isAuthenticated = computed(() => !!token.value)
  const isAdmin = computed(() => user.value?.role === 'admin')

  // Token is handled by api interceptor

  const login = async (username: string, password: string) => {
    console.log('🚀 ========== 开始超详细登录调试 ==========')
    console.log('🔐 Starting login process for user:', username)
    console.log('🌐 Current environment:', {
      url: window.location.href,
      host: window.location.hostname,
      port: window.location.port,
      protocol: window.location.protocol,
      userAgent: navigator.userAgent,
      online: navigator.onLine,
      timestamp: new Date().toISOString()
    })

    try {
      // 🔍 步骤1: 网络诊断
      console.log('🔍 步骤1: 执行网络诊断...')
      const diagnostics = await networkDiagnostics()
      console.log('📊 网络诊断结果:', diagnostics)

      // 🧪 步骤2: 登录测试
      console.log('🧪 步骤2: 执行登录测试...')
      const loginTest = await testLogin(username, password)

      if (loginTest.success) {
        console.log('✅ 登录测试成功!')
        const { access_token } = loginTest.data

        token.value = access_token
        localStorage.setItem('token', access_token)
        console.log('💾 Token saved to localStorage:', {
          tokenLength: access_token.length,
          tokenPreview: `${access_token.substring(0, 20)}...`
        })

        // Get user info
        console.log('👤 Getting user info...')
        try {
          await getCurrentUser()
          console.log('🎉 Login process completed successfully')
        } catch (userError) {
          console.warn('⚠️ Failed to get user info, but login was successful:', userError)
          // 即使获取用户信息失败，登录仍然算成功，因为token是有效的
        }

        showToast('登录成功')
        console.log('🚀 ========== 登录调试完成 (成功) ==========')
        return true
      } else {
        console.error('❌ 登录测试失败:', loginTest.error)
        throw loginTest.error
      }
    } catch (error: any) {
      console.log('🚀 ========== 登录调试完成 (失败) ==========')
      console.error('❌ Login error:', error)
      console.error('Error response:', error.response)
      console.error('Error status:', error.response?.status)
      console.error('Error data:', error.response?.data)
      console.error('Error stack:', error.stack)

      // 🔍 错误分析
      console.log('🔍 错误分析:', {
        errorType: error.constructor.name,
        errorCode: error.code,
        errorMessage: error.message,
        hasResponse: !!error.response,
        hasRequest: !!error.request,
        networkOnline: navigator.onLine,
        timestamp: new Date().toISOString()
      })

      const errorMessage = error.response?.data?.detail || '用户名或密码错误'
      showToast(`登录失败：${errorMessage}`)
      return false
    }
  }

  const logout = () => {
    token.value = null
    user.value = null
    localStorage.removeItem('token')
    showToast('已退出登录')
  }

  const getCurrentUser = async () => {
    try {
      console.log('📤 Getting current user info from:', '/api/v1/users/me')
      console.log('🔑 Current token:', token.value ? `${token.value.substring(0, 20)}...` : 'None')

      const response = await api.get('/api/v1/users/me')
      console.log('✅ User info response:', response.status, response.data)

      user.value = response.data
      console.log('👤 User info set:', user.value)
    } catch (error: any) {
      console.error('❌ Get current user error:', error)
      console.error('Error response:', error.response)
      console.error('Error status:', error.response?.status)
      console.error('Error data:', error.response?.data)

      // 只有在token明确无效时才退出登录（401错误）
      if (error.response?.status === 401) {
        console.log('🚪 Token invalid, logging out')
        logout()
      } else {
        console.log('⚠️ User info error, but keeping login state')
        // 对于其他错误，保持登录状态，可能是网络问题
      }
      throw error // 重新抛出错误，让调用者知道失败了
    }
  }

  // Initialize user info if token exists
  if (token.value && !user.value) {
    getCurrentUser()
  }

  return {
    token,
    user,
    isAuthenticated,
    isAdmin,
    login,
    logout,
    getCurrentUser
  }
})
