import axios from 'axios'
import { showToast } from 'vant'

// Debug logging function
const debugLog = (message: string, data?: any) => {
  const timestamp = new Date().toISOString()
  console.log(`[${timestamp}] 🔧 API Debug: ${message}`, data || '')
}

// Detect if we're in LAN mode by checking the current host
const isLanMode = window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1'
const backendHost = isLanMode ? window.location.hostname : '127.0.0.1'
const baseURL = `http://${backendHost}:8000`

debugLog('API Configuration', {
  isLanMode,
  currentHost: window.location.hostname,
  backendHost,
  baseURL,
  userAgent: navigator.userAgent
})

// Create axios instance with debug logging
const api = axios.create({
  baseURL,
  timeout: 15000, // Increased timeout for LAN
  headers: {
    'Content-Type': 'application/json',
  }
})

// Request interceptor with detailed logging
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')
    
    debugLog('Request Starting', {
      method: config.method?.toUpperCase(),
      url: config.url,
      fullURL: `${config.baseURL}${config.url}`,
      hasToken: !!token,
      headers: config.headers,
      data: config.data
    })
    
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
      debugLog('Token Added', { tokenLength: token.length })
    }
    
    return config
  },
  (error) => {
    debugLog('Request Error', {
      message: error.message,
      code: error.code,
      config: error.config
    })
    return Promise.reject(error)
  }
)

// Response interceptor with detailed logging
api.interceptors.response.use(
  (response) => {
    debugLog('Response Success', {
      status: response.status,
      statusText: response.statusText,
      url: response.config.url,
      dataType: typeof response.data,
      dataSize: JSON.stringify(response.data).length,
      headers: response.headers
    })
    return response
  },
  (error) => {
    const errorInfo = {
      message: error.message,
      code: error.code,
      url: error.config?.url,
      method: error.config?.method,
      baseURL: error.config?.baseURL,
      timeout: error.config?.timeout,
      response: error.response ? {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data,
        headers: error.response.headers
      } : null,
      request: error.request ? {
        readyState: error.request.readyState,
        status: error.request.status,
        responseURL: error.request.responseURL
      } : null
    }
    
    debugLog('Response Error', errorInfo)
    
    // Enhanced error handling with more specific messages
    if (error.code === 'ECONNABORTED') {
      showToast('请求超时，请检查网络连接')
      debugLog('Timeout Error', { timeout: error.config?.timeout })
    } else if (error.code === 'ERR_NETWORK') {
      showToast('网络错误，请检查服务器连接')
      debugLog('Network Error', { 
        isLanMode,
        backendHost,
        suggestion: isLanMode ? 'Check firewall and CORS settings' : 'Check if backend is running'
      })
    } else if (error.response?.status === 401) {
      localStorage.removeItem('token')
      window.location.href = '/login'
      showToast('登录已过期，请重新登录')
      debugLog('Authentication Error', { redirecting: true })
    } else if (error.response?.status === 403) {
      showToast('权限不足')
      debugLog('Permission Error')
    } else if (error.response?.status === 404) {
      showToast('请求的资源不存在')
      debugLog('Not Found Error')
    } else if (error.response?.status >= 500) {
      showToast('服务器错误，请稍后重试')
      debugLog('Server Error', { status: error.response.status })
    } else if (error.response?.data?.detail) {
      showToast(error.response.data.detail)
      debugLog('API Error', { detail: error.response.data.detail })
    } else {
      showToast('未知错误，请稍后重试')
      debugLog('Unknown Error')
    }
    
    return Promise.reject(error)
  }
)

// Add a test function for connectivity
export const testConnectivity = async () => {
  debugLog('Testing Connectivity')
  
  try {
    const response = await api.get('/health')
    debugLog('Connectivity Test Success', response.data)
    return { success: true, data: response.data }
  } catch (error) {
    debugLog('Connectivity Test Failed', error)
    return { success: false, error }
  }
}

// Add a test function for authentication
export const testAuth = async (username: string, password: string) => {
  debugLog('Testing Authentication', { username })
  
  try {
    const response = await api.post('/api/v1/auth/token', 
      `username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`,
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    )
    debugLog('Authentication Test Success', { hasToken: !!response.data.access_token })
    return { success: true, data: response.data }
  } catch (error) {
    debugLog('Authentication Test Failed', error)
    return { success: false, error }
  }
}

export default api
