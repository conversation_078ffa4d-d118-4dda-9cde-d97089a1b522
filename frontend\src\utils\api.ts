import axios from 'axios'
import { showToast } from 'vant'

// 🔧 超详细调试日志函数
const debugLog = (level: string, message: string, data?: any) => {
  const timestamp = new Date().toISOString()
  const emoji = level === 'ERROR' ? '❌' : level === 'WARN' ? '⚠️' : level === 'SUCCESS' ? '✅' : '🔧'
  console.log(`[${timestamp}] ${emoji} API ${level}: ${message}`)
  if (data) {
    console.log(`[${timestamp}] 📊 Data:`, data)
  }
}

// 🌐 环境检测
const currentHost = window.location.hostname
const currentPort = window.location.port
const currentProtocol = window.location.protocol
const isLocalhost = currentHost === 'localhost' || currentHost === '127.0.0.1'
const baseURL = import.meta.env.VITE_API_BASE_URL || ''

debugLog('INFO', '🚀 API 初始化配置', {
  currentURL: window.location.href,
  currentHost,
  currentPort,
  currentProtocol,
  isLocalhost,
  baseURL,
  envApiBaseUrl: import.meta.env.VITE_API_BASE_URL,
  userAgent: navigator.userAgent,
  timestamp: new Date().toISOString()
})

// Create axios instance
const api = axios.create({
  baseURL,
  timeout: 15000, // 增加超时时间
})

// 🔍 超详细请求拦截器
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')

    debugLog('INFO', '📤 发起请求', {
      method: config.method?.toUpperCase(),
      url: config.url,
      baseURL: config.baseURL,
      fullURL: `${config.baseURL}${config.url}`,
      headers: { ...config.headers },
      data: config.data,
      hasToken: !!token,
      tokenPreview: token ? `${token.substring(0, 20)}...` : 'None',
      timeout: config.timeout,
      timestamp: new Date().toISOString()
    })

    if (token) {
      config.headers.Authorization = `Bearer ${token}`
      debugLog('INFO', '🔑 添加认证令牌', { tokenLength: token.length })
    }

    return config
  },
  (error) => {
    debugLog('ERROR', '📤 请求拦截器错误', {
      message: error.message,
      code: error.code,
      stack: error.stack
    })
    return Promise.reject(error)
  }
)

// 🔍 超详细响应拦截器
api.interceptors.response.use(
  (response) => {
    debugLog('SUCCESS', '📥 响应成功', {
      status: response.status,
      statusText: response.statusText,
      url: response.config.url,
      method: response.config.method?.toUpperCase(),
      headers: response.headers,
      dataType: typeof response.data,
      dataSize: response.data ? JSON.stringify(response.data).length : 0,
      timestamp: new Date().toISOString()
    })
    return response
  },
  (error) => {
    // 🚨 超详细错误分析
    const errorInfo = {
      message: error.message,
      code: error.code,
      name: error.name,
      stack: error.stack,
      config: error.config ? {
        method: error.config.method,
        url: error.config.url,
        baseURL: error.config.baseURL,
        timeout: error.config.timeout,
        headers: error.config.headers
      } : null,
      response: error.response ? {
        status: error.response.status,
        statusText: error.response.statusText,
        headers: error.response.headers,
        data: error.response.data
      } : null,
      request: error.request ? {
        readyState: error.request.readyState,
        status: error.request.status,
        responseURL: error.request.responseURL,
        responseText: error.request.responseText?.substring(0, 500)
      } : null,
      timestamp: new Date().toISOString(),
      networkInfo: {
        online: navigator.onLine,
        connection: (navigator as any).connection ? {
          effectiveType: (navigator as any).connection.effectiveType,
          downlink: (navigator as any).connection.downlink,
          rtt: (navigator as any).connection.rtt
        } : 'Not available'
      }
    }

    debugLog('ERROR', '📥 响应错误', errorInfo)

    // 🎯 具体错误处理
    if (error.code === 'ERR_NETWORK') {
      debugLog('ERROR', '🌐 网络连接错误 - 详细分析', {
        possibleCauses: [
          '后端服务未启动',
          '端口配置错误',
          '防火墙阻止连接',
          'CORS配置问题',
          '代理配置错误'
        ],
        checkList: [
          '检查后端是否在9000端口运行',
          '检查前端代理配置是否正确',
          '检查CORS配置是否包含当前域名',
          '检查防火墙设置'
        ]
      })
      showToast('网络连接失败，请检查服务器状态')
    } else if (error.code === 'ECONNABORTED') {
      debugLog('ERROR', '⏰ 请求超时', { timeout: error.config?.timeout })
      showToast('请求超时，请稍后重试')
    } else if (error.response?.status === 401) {
      debugLog('WARN', '🔐 认证失败', { redirecting: true })
      localStorage.removeItem('token')
      window.location.href = '/login'
      showToast('登录已过期，请重新登录')
    } else if (error.response?.status === 403) {
      debugLog('WARN', '🚫 权限不足')
      showToast('权限不足')
    } else if (error.response?.status === 404) {
      debugLog('WARN', '🔍 资源未找到')
      showToast('请求的资源不存在')
    } else if (error.response?.status >= 500) {
      debugLog('ERROR', '🔥 服务器错误', { status: error.response.status })
      showToast('服务器错误，请稍后重试')
    } else if (error.response?.data?.detail) {
      debugLog('WARN', '📋 API错误', { detail: error.response.data.detail })
      showToast(error.response.data.detail)
    } else {
      debugLog('ERROR', '❓ 未知错误')
      showToast('未知错误，请稍后重试')
    }

    return Promise.reject(error)
  }
)

// 🔧 网络诊断工具
export const networkDiagnostics = async () => {
  debugLog('INFO', '🔍 开始网络诊断')

  const results = {
    timestamp: new Date().toISOString(),
    environment: {
      url: window.location.href,
      host: window.location.hostname,
      port: window.location.port,
      protocol: window.location.protocol,
      userAgent: navigator.userAgent,
      online: navigator.onLine
    },
    tests: [] as any[]
  }

  // 测试1: 基础连通性
  try {
    debugLog('INFO', '🧪 测试1: 基础连通性检查')
    const response = await fetch('/api/health', {
      method: 'GET',
      cache: 'no-cache'
    })
    results.tests.push({
      name: '基础连通性',
      success: response.ok,
      status: response.status,
      statusText: response.statusText,
      url: response.url
    })
    debugLog('SUCCESS', '✅ 基础连通性测试通过')
  } catch (error: any) {
    results.tests.push({
      name: '基础连通性',
      success: false,
      error: error.message,
      code: error.code
    })
    debugLog('ERROR', '❌ 基础连通性测试失败', error)
  }

  // 测试2: API端点检查
  try {
    debugLog('INFO', '🧪 测试2: API端点检查')
    const response = await fetch('/api/v1/auth/token', {
      method: 'OPTIONS',
      cache: 'no-cache'
    })
    results.tests.push({
      name: 'API端点检查',
      success: response.ok,
      status: response.status,
      headers: Object.fromEntries(response.headers.entries())
    })
    debugLog('SUCCESS', '✅ API端点检查通过')
  } catch (error: any) {
    results.tests.push({
      name: 'API端点检查',
      success: false,
      error: error.message,
      code: error.code
    })
    debugLog('ERROR', '❌ API端点检查失败', error)
  }

  // 测试3: CORS预检
  try {
    debugLog('INFO', '🧪 测试3: CORS预检')
    const response = await fetch('/api/v1/auth/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: 'username=test&password=test',
      cache: 'no-cache'
    })
    results.tests.push({
      name: 'CORS预检',
      success: true,
      status: response.status,
      note: response.status === 401 ? '认证失败但连接正常' : '连接正常'
    })
    debugLog('SUCCESS', '✅ CORS预检通过')
  } catch (error: any) {
    results.tests.push({
      name: 'CORS预检',
      success: false,
      error: error.message,
      code: error.code
    })
    debugLog('ERROR', '❌ CORS预检失败', error)
  }

  debugLog('INFO', '📊 网络诊断完成', results)
  return results
}

// 🧪 登录测试工具
export const testLogin = async (username: string, password: string) => {
  debugLog('INFO', '🧪 开始登录测试', { username })

  try {
    const formData = new URLSearchParams()
    formData.append('username', username)
    formData.append('password', password)

    debugLog('INFO', '📤 发送登录请求', {
      url: '/api/v1/auth/token',
      method: 'POST',
      contentType: 'application/x-www-form-urlencoded',
      dataSize: formData.toString().length
    })

    const response = await api.post('/api/v1/auth/token', formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    })

    debugLog('SUCCESS', '✅ 登录测试成功', {
      status: response.status,
      hasToken: !!response.data.access_token,
      tokenPreview: response.data.access_token ? `${response.data.access_token.substring(0, 20)}...` : 'None'
    })

    return { success: true, data: response.data }
  } catch (error: any) {
    debugLog('ERROR', '❌ 登录测试失败', error)
    return { success: false, error }
  }
}

export default api
