/**
 * 剪贴板工具函数
 * 提供兼容性良好的复制到剪贴板功能
 */

/**
 * 降级复制函数，用于不支持现代剪贴板API的浏览器
 * 使用传统的document.execCommand方法
 */
const fallbackCopyToClipboard = (text: string): void => {
  // 创建一个临时的textarea元素
  const textArea = document.createElement('textarea')
  textArea.value = text
  
  // 设置样式使其不可见但可操作
  textArea.style.position = 'fixed'
  textArea.style.top = '0'
  textArea.style.left = '0'
  textArea.style.width = '2em'
  textArea.style.height = '2em'
  textArea.style.padding = '0'
  textArea.style.border = 'none'
  textArea.style.outline = 'none'
  textArea.style.boxShadow = 'none'
  textArea.style.background = 'transparent'
  textArea.style.opacity = '0'
  textArea.style.zIndex = '-1'
  
  // 添加到DOM
  document.body.appendChild(textArea)
  
  try {
    // 选择文本
    textArea.focus()
    textArea.select()
    
    // 对于移动设备，确保选择范围正确
    if (textArea.setSelectionRange) {
      textArea.setSelectionRange(0, text.length)
    }
    
    // 执行复制命令
    const successful = document.execCommand('copy')
    if (!successful) {
      throw new Error('document.execCommand("copy") 执行失败')
    }
  } finally {
    // 清理：移除临时元素
    document.body.removeChild(textArea)
  }
}

/**
 * 检查是否支持现代剪贴板API
 */
const isClipboardAPISupported = (): boolean => {
  return !!(navigator.clipboard && navigator.clipboard.writeText)
}

/**
 * 检查是否在安全上下文中（HTTPS或localhost）
 * 现代剪贴板API需要安全上下文
 */
const isSecureContext = (): boolean => {
  return window.isSecureContext || location.protocol === 'https:' || location.hostname === 'localhost'
}

/**
 * 通用复制到剪贴板函数
 * 自动选择最佳的复制方法
 * 
 * @param text 要复制的文本
 * @param options 选项
 * @returns Promise<boolean> 复制是否成功
 */
export const copyToClipboard = async (
  text: string, 
  options: {
    fallbackMessage?: string
    successMessage?: string
    errorMessage?: string
  } = {}
): Promise<boolean> => {
  const {
    fallbackMessage = '使用降级方案复制',
    successMessage = '复制成功',
    errorMessage = '复制失败'
  } = options

  // 检查输入
  if (!text || typeof text !== 'string') {
    console.warn('copyToClipboard: 无效的文本输入')
    return false
  }

  try {
    // 优先使用现代剪贴板API
    if (isClipboardAPISupported() && isSecureContext()) {
      try {
        await navigator.clipboard.writeText(text)
        console.log(`${successMessage} (现代API)`)
        return true
      } catch (clipboardError) {
        console.warn('现代剪贴板API失败，尝试降级方案:', clipboardError)
        // 继续执行降级方案
      }
    }

    // 降级方案：使用document.execCommand
    try {
      fallbackCopyToClipboard(text)
      console.log(`${successMessage} (${fallbackMessage})`)
      return true
    } catch (fallbackError) {
      console.error('降级复制方案也失败:', fallbackError)
      throw fallbackError
    }

  } catch (error) {
    console.error(`${errorMessage}:`, error)
    return false
  }
}

/**
 * 获取剪贴板支持信息
 * 用于调试和用户反馈
 */
export const getClipboardInfo = () => {
  return {
    hasClipboardAPI: isClipboardAPISupported(),
    isSecureContext: isSecureContext(),
    userAgent: navigator.userAgent,
    protocol: location.protocol,
    hostname: location.hostname,
    supportsExecCommand: document.queryCommandSupported ? document.queryCommandSupported('copy') : false
  }
}

/**
 * 简化的复制函数，带有默认的成功/失败提示
 * 
 * @param text 要复制的文本
 * @param successToast 成功时的提示函数
 * @param errorToast 失败时的提示函数
 */
export const copyWithToast = async (
  text: string,
  successToast: (message: string) => void,
  errorToast: (message: string) => void
): Promise<void> => {
  const success = await copyToClipboard(text)
  
  if (success) {
    successToast('已复制到剪贴板')
  } else {
    errorToast('复制失败，请手动复制')
  }
}

export default {
  copyToClipboard,
  copyWithToast,
  getClipboardInfo,
  isClipboardAPISupported,
  isSecureContext
}
