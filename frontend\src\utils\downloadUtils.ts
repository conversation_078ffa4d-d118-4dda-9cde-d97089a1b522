/**
 * 文件下载和URL处理工具函数
 */

/**
 * 构建完整的图片URL
 * @param imagePath 图片路径（可能是相对路径或完整URL）
 * @returns 完整的图片URL
 */
export const buildImageUrl = (imagePath: string): string => {
  if (!imagePath) {
    return ''
  }

  // 如果已经是完整URL，直接返回
  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    return imagePath
  }

  // 获取基础URL
  const baseUrl = import.meta.env.VITE_API_BASE_URL || window.location.origin

  // 如果路径以 /uploads/ 开头，直接拼接
  if (imagePath.startsWith('/uploads/')) {
    return `${baseUrl}${imagePath}`
  }

  // 否则添加 /uploads/ 前缀
  return `${baseUrl}/uploads/${imagePath}`
}

/**
 * 批量构建图片URL
 * @param imagePaths 图片路径数组
 * @returns 完整的图片URL数组
 */
export const buildImageUrls = (imagePaths: string[]): string[] => {
  if (!imagePaths || !Array.isArray(imagePaths)) {
    return []
  }

  return imagePaths.map(path => buildImageUrl(path)).filter(url => url)
}

/**
 * 生成带时间戳的文件名
 * @param baseName 基础文件名（不含扩展名）
 * @param extension 文件扩展名（如 'jpg', 'png'）
 * @returns 带时间戳的完整文件名
 */
export const generateTimestampedFilename = (baseName: string, extension: string = 'jpg'): string => {
  const now = new Date()
  const timestamp = now.toISOString()
    .replace(/[:.]/g, '-')  // 替换冒号和点号为连字符
    .replace('T', '_')      // 替换T为下划线
    .slice(0, 19)           // 只保留到秒，去掉毫秒部分
  
  return `${baseName}_${timestamp}.${extension}`
}

/**
 * 从URL中提取文件扩展名
 * @param url 文件URL
 * @returns 文件扩展名（小写，不含点号）
 */
export const getFileExtensionFromUrl = (url: string): string => {
  try {
    // 移除查询参数和锚点
    const cleanUrl = url.split('?')[0].split('#')[0]
    const parts = cleanUrl.split('.')
    if (parts.length > 1) {
      return parts[parts.length - 1].toLowerCase()
    }
  } catch (error) {
    console.warn('Failed to extract extension from URL:', url, error)
  }
  return 'jpg' // 默认扩展名
}

/**
 * 下载单个图片文件
 * @param imageUrl 图片URL
 * @param baseName 基础文件名
 * @param showToast 显示提示的函数
 */
export const downloadImageWithTimestamp = (
  imageUrl: string, 
  baseName: string, 
  showToast?: (message: string) => void
): void => {
  try {
    const extension = getFileExtensionFromUrl(imageUrl)
    const filename = generateTimestampedFilename(baseName, extension)
    
    const link = document.createElement('a')
    link.href = imageUrl
    link.download = filename
    link.target = '_blank'
    
    // 添加到DOM，点击，然后移除
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    if (showToast) {
      showToast(`开始下载: ${filename}`)
    }
  } catch (error) {
    console.error('Download failed:', error)
    if (showToast) {
      showToast('下载失败')
    }
  }
}

/**
 * 批量下载图片文件
 * @param imageUrls 图片URL数组
 * @param baseNamePrefix 基础文件名前缀
 * @param showToast 显示提示的函数
 */
export const downloadMultipleImagesWithTimestamp = (
  imageUrls: string[], 
  baseNamePrefix: string, 
  showToast?: (message: string) => void
): void => {
  if (imageUrls.length === 0) {
    if (showToast) {
      showToast('没有可下载的图片')
    }
    return
  }

  try {
    imageUrls.forEach((imageUrl, index) => {
      // 为每个文件添加延迟，避免浏览器阻止多个下载
      setTimeout(() => {
        const baseName = `${baseNamePrefix}_${index + 1}`
        downloadImageWithTimestamp(imageUrl, baseName, undefined) // 不显示单个文件的提示
      }, index * 100) // 每个文件间隔100ms
    })

    if (showToast) {
      showToast(`开始下载 ${imageUrls.length} 张图片`)
    }
  } catch (error) {
    console.error('Batch download failed:', error)
    if (showToast) {
      showToast('批量下载失败')
    }
  }
}

/**
 * 下载视频文件
 * @param videoUrl 视频URL
 * @param baseName 基础文件名
 * @param showToast 显示提示的函数
 */
export const downloadVideoWithTimestamp = (
  videoUrl: string, 
  baseName: string, 
  showToast?: (message: string) => void
): void => {
  try {
    const extension = getFileExtensionFromUrl(videoUrl) || 'mp4'
    const filename = generateTimestampedFilename(baseName, extension)
    
    const link = document.createElement('a')
    link.href = videoUrl
    link.download = filename
    link.target = '_blank'
    
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    if (showToast) {
      showToast(`开始下载: ${filename}`)
    }
  } catch (error) {
    console.error('Video download failed:', error)
    if (showToast) {
      showToast('下载失败')
    }
  }
}
