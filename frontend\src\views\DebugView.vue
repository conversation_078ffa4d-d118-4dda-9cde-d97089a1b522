<template>
  <div class="debug-container">
    <van-nav-bar title="网络调试工具" left-arrow @click-left="$router.back()" />
    
    <div class="debug-content">
      <van-cell-group title="环境信息">
        <van-cell title="当前URL" :value="envInfo.url" />
        <van-cell title="主机名" :value="envInfo.host" />
        <van-cell title="端口" :value="envInfo.port || '默认'" />
        <van-cell title="协议" :value="envInfo.protocol" />
        <van-cell title="在线状态" :value="envInfo.online ? '在线' : '离线'" />
      </van-cell-group>

      <van-cell-group title="调试工具">
        <van-cell title="网络诊断" is-link @click="runNetworkDiagnostics" />
        <van-cell title="登录测试" is-link @click="showLoginTest = true" />
        <van-cell title="清除日志" is-link @click="clearLogs" />
      </van-cell-group>

      <van-cell-group title="实时日志" v-if="logs.length > 0">
        <div class="logs-container">
          <div 
            v-for="(log, index) in logs" 
            :key="index" 
            :class="['log-item', `log-${log.level.toLowerCase()}`]"
          >
            <div class="log-time">{{ log.time }}</div>
            <div class="log-message">{{ log.message }}</div>
            <div v-if="log.data" class="log-data">
              <pre>{{ JSON.stringify(log.data, null, 2) }}</pre>
            </div>
          </div>
        </div>
      </van-cell-group>
    </div>

    <!-- 登录测试弹窗 -->
    <van-popup v-model:show="showLoginTest" position="center" style="padding: 20px; border-radius: 8px;">
      <div style="width: 300px;">
        <h3 style="text-align: center; margin-bottom: 20px;">登录测试</h3>
        <van-field v-model="testUsername" label="用户名" placeholder="请输入用户名" />
        <van-field v-model="testPassword" label="密码" type="password" placeholder="请输入密码" />
        <div style="margin-top: 20px; display: flex; gap: 10px;">
          <van-button type="primary" block @click="runLoginTest" :loading="testing">测试登录</van-button>
          <van-button block @click="showLoginTest = false">取消</van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { networkDiagnostics, testLogin } from '@/utils/api'
import { showToast } from 'vant'

const envInfo = reactive({
  url: window.location.href,
  host: window.location.hostname,
  port: window.location.port,
  protocol: window.location.protocol,
  online: navigator.onLine
})

const logs = ref<Array<{
  time: string
  level: string
  message: string
  data?: any
}>>([])

const showLoginTest = ref(false)
const testUsername = ref('admin')
const testPassword = ref('admin123')
const testing = ref(false)

// 拦截console.log来显示日志
const originalConsoleLog = console.log
const originalConsoleError = console.error
const originalConsoleWarn = console.warn

const addLog = (level: string, message: string, data?: any) => {
  logs.value.unshift({
    time: new Date().toLocaleTimeString(),
    level,
    message,
    data
  })
  
  // 限制日志数量
  if (logs.value.length > 100) {
    logs.value = logs.value.slice(0, 100)
  }
}

// 重写console方法
console.log = (...args) => {
  originalConsoleLog(...args)
  if (args[0] && typeof args[0] === 'string' && args[0].includes('API')) {
    addLog('INFO', args[0], args[1])
  }
}

console.error = (...args) => {
  originalConsoleError(...args)
  if (args[0] && typeof args[0] === 'string' && args[0].includes('API')) {
    addLog('ERROR', args[0], args[1])
  }
}

console.warn = (...args) => {
  originalConsoleWarn(...args)
  if (args[0] && typeof args[0] === 'string' && args[0].includes('API')) {
    addLog('WARN', args[0], args[1])
  }
}

const runNetworkDiagnostics = async () => {
  try {
    addLog('INFO', '开始网络诊断...')
    const result = await networkDiagnostics()
    addLog('SUCCESS', '网络诊断完成', result)
    showToast('网络诊断完成，请查看日志')
  } catch (error) {
    addLog('ERROR', '网络诊断失败', error)
    showToast('网络诊断失败')
  }
}

const runLoginTest = async () => {
  if (!testUsername.value || !testPassword.value) {
    showToast('请输入用户名和密码')
    return
  }

  testing.value = true
  try {
    addLog('INFO', `开始登录测试: ${testUsername.value}`)
    const result = await testLogin(testUsername.value, testPassword.value)
    
    if (result.success) {
      addLog('SUCCESS', '登录测试成功', result.data)
      showToast('登录测试成功')
    } else {
      addLog('ERROR', '登录测试失败', result.error)
      showToast('登录测试失败')
    }
  } catch (error) {
    addLog('ERROR', '登录测试异常', error)
    showToast('登录测试异常')
  } finally {
    testing.value = false
    showLoginTest.value = false
  }
}

const clearLogs = () => {
  logs.value = []
  showToast('日志已清除')
}

onMounted(() => {
  addLog('INFO', '调试工具已启动', envInfo)
})
</script>

<style scoped>
.debug-container {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.debug-content {
  padding: 16px;
}

.logs-container {
  max-height: 400px;
  overflow-y: auto;
  background: #fff;
  border-radius: 8px;
  padding: 12px;
}

.log-item {
  margin-bottom: 12px;
  padding: 8px;
  border-radius: 4px;
  border-left: 4px solid #ddd;
}

.log-info {
  border-left-color: #1989fa;
  background-color: #f0f9ff;
}

.log-success {
  border-left-color: #07c160;
  background-color: #f0fff4;
}

.log-error {
  border-left-color: #ee0a24;
  background-color: #fff5f5;
}

.log-warn {
  border-left-color: #ff976a;
  background-color: #fff8f0;
}

.log-time {
  font-size: 12px;
  color: #969799;
  margin-bottom: 4px;
}

.log-message {
  font-size: 14px;
  color: #323233;
  margin-bottom: 4px;
}

.log-data {
  font-size: 12px;
  color: #646566;
  background: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  overflow-x: auto;
}

.log-data pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
}
</style>
