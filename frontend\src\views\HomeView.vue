<template>
  <div class="home-container">
    <!-- Welcome Section -->
    <div class="welcome-section">
      <div class="welcome-card">
        <div class="welcome-icon">
          <van-icon name="smile-o" />
        </div>
        <div class="welcome-content">
          <h2>欢迎使用小红书笔记管理系统</h2>
          <p class="welcome-desc">高效管理您的商品和笔记内容</p>
        </div>
      </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="quick-actions">
      <h3 class="section-title">
        <van-icon name="apps-o" />
        快捷操作
      </h3>
      <div class="action-grid">
        <div class="action-item" @click="router.push('/products')">
          <div class="action-icon" style="background: linear-gradient(135deg, #ff6b6b, #ff8e8e);">
            <van-icon name="goods-collect-o" />
          </div>
          <span class="action-text">商品管理</span>
        </div>
        <div class="action-item" @click="router.push('/notes/management')">
          <div class="action-icon" style="background: linear-gradient(135deg, #4ecdc4, #6ee7db);">
            <van-icon name="notes-o" />
          </div>
          <span class="action-text">笔记管理</span>
        </div>
        <div class="action-item" @click="router.push('/prompts')">
          <div class="action-icon" style="background: linear-gradient(135deg, #a8e6cf, #88d8a3);">
            <van-icon name="edit" />
          </div>
          <span class="action-text">AI提示词</span>
        </div>
        <div class="action-item" @click="router.push('/notes/download')">
          <div class="action-icon" style="background: linear-gradient(135deg, #45b7d1, #6cc5e0);">
            <van-icon name="down" />
          </div>
          <span class="action-text">笔记下载</span>
        </div>
        <div class="action-item" @click="router.push('/products/download')">
          <div class="action-icon" style="background: linear-gradient(135deg, #ff9a9e, #fecfef);">
            <van-icon name="photo-o" />
          </div>
          <span class="action-text">商品下载</span>
        </div>
        <div class="action-item" @click="router.push('/profile')">
          <div class="action-icon" style="background: linear-gradient(135deg, #9c88ff, #b3a0ff);">
            <van-icon name="user-o" />
          </div>
          <span class="action-text">个人资料</span>
        </div>
        <div
          v-if="authStore.isAdmin"
          class="action-item"
          @click="router.push('/recycle-bin')"
        >
          <div class="action-icon" style="background: linear-gradient(135deg, #96ceb4, #b3d9c7);">
            <van-icon name="delete-o" />
          </div>
          <span class="action-text">回收站</span>
        </div>
      </div>
    </div>
    
    <!-- Statistics and Recent Activity Grid -->
    <div class="content-grid">
      <!-- Statistics -->
      <div class="statistics">
        <h3 class="section-title">
          <van-icon name="chart-trending-o" />
          数据统计
        </h3>
        <van-cell-group inset>
          <van-cell title="商品总数" :value="stats.products" icon="goods-collect-o" />
          <van-cell title="笔记总数" :value="stats.notes" icon="edit" />
          <van-cell title="待发布笔记" :value="stats.pendingNotes" icon="clock-o" />
        </van-cell-group>
      </div>

      <!-- Recent Activity -->
      <div class="recent-activity">
        <h3 class="section-title">
          <van-icon name="clock-o" />
          最近活动
        </h3>
        <van-cell-group inset>
          <van-cell
            v-for="activity in recentActivities"
            :key="activity.id"
            :title="activity.title"
            :label="activity.time"
            :value="activity.type"
          />
          <van-cell
            v-if="recentActivities.length === 0"
            title="暂无活动记录"
            label="开始创建您的第一个商品吧！"
          />
        </van-cell-group>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { showToast } from 'vant'
import api from '@/utils/api'

const router = useRouter()
const authStore = useAuthStore()

const stats = ref({
  products: 0,
  notes: 0,
  pendingNotes: 0
})

const recentActivities = ref<any[]>([])



const loadStats = async () => {
  try {
    // Load products stats
    const productsResponse = await api.get('/api/v1/products-download/stats')
    stats.value.products = productsResponse.data.total_products || 0

    // Load notes stats
    const notesResponse = await api.get('/api/v1/notes-download/stats')
    stats.value.notes = notesResponse.data.total_notes || 0
    stats.value.pendingNotes = notesResponse.data.unused_notes || 0
  } catch (error) {
    console.error('Failed to load stats:', error)
    // Set default values on error
    stats.value.products = 0
    stats.value.notes = 0
    stats.value.pendingNotes = 0
  }
}

onMounted(() => {
  loadStats()
})
</script>

<style scoped>
.home-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
  width: 100%;
}

/* 桌面端布局优化 */
@media (min-width: 768px) {
  .home-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }
}

@media (min-width: 1200px) {
  .home-container {
    max-width: 1400px;
    padding: 0 40px;
  }
}



.welcome-section {
  padding: 20px 16px;
}

/* 桌面端欢迎区域优化 */
@media (min-width: 768px) {
  .welcome-section {
    padding: 30px 20px;
  }

  .welcome-card {
    max-width: 600px;
    margin: 0 auto;
  }
}

.welcome-card {
  background: white;
  border-radius: 20px;
  padding: 32px 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.8);
  gap: 16px;
}

.welcome-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #4ecdc4, #45b7d1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(78, 205, 196, 0.3);
}

.welcome-icon .van-icon {
  font-size: 36px;
  color: white;
}

.welcome-content h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.welcome-desc {
  margin: 0;
  color: #6c757d;
  font-size: 16px;
}

.quick-actions,
.statistics,
.recent-activity {
  padding: 0 16px 20px;
}

/* 桌面端内容区域布局 */
@media (min-width: 768px) {
  .quick-actions,
  .statistics,
  .recent-activity {
    padding: 0 20px 30px;
  }

  /* 统计和活动区域并排显示 */
  .content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    max-width: 1000px;
    margin: 0 auto;
  }

  .statistics,
  .recent-activity {
    padding: 0;
  }
}

@media (min-width: 1200px) {
  .content-grid {
    max-width: 1200px;
    gap: 60px;
  }
}

.section-title {
  display: flex;
  align-items: center;
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.section-title .van-icon {
  margin-right: 8px;
  font-size: 20px;
  color: #4ecdc4;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

/* 桌面端快速操作网格优化 */
@media (min-width: 768px) {
  .action-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    max-width: 800px;
    margin: 0 auto;
  }
}

@media (min-width: 1200px) {
  .action-grid {
    max-width: 1000px;
    gap: 24px;
  }
}

.action-item {
  background: white;
  border-radius: 16px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.8);
}

.action-item:active {
  transform: translateY(1px);
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.1);
}

.action-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.action-icon .van-icon {
  font-size: 24px;
  color: white;
}

.action-text {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

/* 统计和活动区域样式 */
:deep(.statistics .van-cell-group),
:deep(.recent-activity .van-cell-group) {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
}

:deep(.van-cell) {
  padding: 16px;
}

/* 移动端响应式设计 */
@media (max-width: 767px) {
  .content-grid {
    display: block !important;
  }

  .statistics,
  .recent-activity {
    padding: 0 16px 20px !important;
  }
}

@media (max-width: 480px) {
  .welcome-card {
    padding: 20px;
  }

  .welcome-icon {
    width: 60px;
    height: 60px;
  }

  .welcome-icon .van-icon {
    font-size: 28px;
  }

  .welcome-content h2 {
    font-size: 20px;
  }

  .action-grid {
    gap: 12px;
  }

  .action-item {
    padding: 16px;
  }

  .action-icon {
    width: 45px;
    height: 45px;
  }

  .action-icon .van-icon {
    font-size: 20px;
  }
}
</style>
