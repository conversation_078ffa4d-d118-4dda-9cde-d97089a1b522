<template>
  <div class="login-container">
    <!-- 背景装饰 -->
    <div class="bg-decoration">
      <div class="circle circle-1"></div>
      <div class="circle circle-2"></div>
      <div class="circle circle-3"></div>

      <!-- 桌面端额外装饰 -->
      <div class="desktop-decoration">
        <div class="wave wave-1"></div>
        <div class="wave wave-2"></div>
        <div class="geometric-shape shape-1"></div>
        <div class="geometric-shape shape-2"></div>
        <div class="geometric-shape shape-3"></div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="login-content">
      <!-- Logo和标题 -->
      <div class="login-header">
        <div class="logo">
          <div class="logo-icon">📝</div>
        </div>
        <h1 class="title">test</h1>
        <p class="subtitle">test2</p>
      </div>

      <!-- 登录表单 -->
      <div class="login-form-wrapper">
        <van-form @submit="handleLogin" class="login-form">
          <div class="form-item">
            <van-field
              v-model="form.username"
              name="username"
              placeholder="请输入用户名"
              :rules="[{ required: true, message: '请输入用户名' }]"
              :border="false"
              class="custom-field"
            >
              <template #left-icon>
                <van-icon name="user-o" class="field-icon" />
              </template>
            </van-field>
          </div>

          <div class="form-item">
            <van-field
              v-model="form.password"
              type="password"
              name="password"
              placeholder="请输入密码"
              :rules="[{ required: true, message: '请输入密码' }]"
              :border="false"
              class="custom-field"
            >
              <template #left-icon>
                <van-icon name="lock" class="field-icon" />
              </template>
            </van-field>
          </div>

          <div class="login-button">
            <van-button
              round
              block
              type="primary"
              native-type="submit"
              :loading="loading"
              loading-text="登录中..."
              class="custom-button"
            >
              立即登录
            </van-button>
          </div>
        </van-form>
      </div>

      <!-- 提示信息 -->
      <div class="login-tips">
        <div class="tip-item">
          <van-icon name="phone-o" />
          <span>联系管理员创建账户</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const loading = ref(false)
const form = reactive({
  username: '',
  password: ''
})

const handleLogin = async () => {
  loading.value = true
  try {
    const success = await authStore.login(form.username, form.password)
    if (success) {
      router.push('/')
    }
  } finally {
    loading.value = false
  }
}


</script>

<style scoped>
.login-container {
  min-height: 100vh;
  width: 100vw;
  background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 50%, #45b7d1 100%);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  margin: 0;
  box-sizing: border-box;
}

/* 桌面端全屏背景优化 */
@media (min-width: 768px) {
  .login-container {
    background-attachment: fixed;
    background-size: cover;
  }

  .login-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 20% 80%, rgba(255, 107, 107, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(78, 205, 196, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(69, 183, 209, 0.2) 0%, transparent 50%);
    z-index: 0;
  }
}

/* 背景装饰 */
.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
  backdrop-filter: blur(2px);
}

.circle-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  left: 5%;
  animation-delay: 0s;
  background: rgba(255, 255, 255, 0.08);
}

.circle-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 8%;
  animation-delay: 2s;
  background: rgba(255, 255, 255, 0.06);
}

.circle-3 {
  width: 100px;
  height: 100px;
  top: 25%;
  right: 20%;
  animation-delay: 4s;
  background: rgba(255, 255, 255, 0.1);
}

/* 添加更多装饰元素 */
.circle::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 60%;
  height: 60%;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 50%;
  transform: translate(-50%, -50%);
}

/* 桌面端添加更多装饰圆圈 */
@media (min-width: 768px) {
  .bg-decoration::before {
    content: '';
    position: absolute;
    width: 300px;
    height: 300px;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 50%;
    top: 5%;
    right: 5%;
    animation: float 8s ease-in-out infinite reverse;
  }

  .bg-decoration::after {
    content: '';
    position: absolute;
    width: 180px;
    height: 180px;
    background: rgba(255, 255, 255, 0.04);
    border-radius: 50%;
    bottom: 10%;
    left: 15%;
    animation: float 10s ease-in-out infinite;
    animation-delay: 1s;
  }

  .circle-1 {
    width: 250px;
    height: 250px;
  }

  .circle-2 {
    width: 180px;
    height: 180px;
  }

  .circle-3 {
    width: 120px;
    height: 120px;
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

/* 主要内容 */
.login-content {
  width: 100%;
  max-width: 400px;
  position: relative;
  z-index: 1;
}

/* 桌面端优化 */
@media (min-width: 768px) {
  .login-content {
    max-width: 450px;
    transform: scale(1.1);
  }
}

@media (min-width: 1200px) {
  .login-content {
    max-width: 500px;
    transform: scale(1.2);
  }
}

/* Logo和标题 */
.login-header {
  text-align: center;
  margin-bottom: 40px;
}

.logo {
  margin-bottom: 20px;
}

.logo-icon {
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36px;
  margin: 0 auto;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.title {
  font-size: 32px;
  font-weight: 700;
  color: white;
  margin: 20px 0 8px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.subtitle {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  font-weight: 400;
}

/* 表单样式 */
.login-form-wrapper {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24px;
  padding: 32px 24px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.form-item {
  margin-bottom: 20px;
}

.form-item:last-of-type {
  margin-bottom: 0;
}

:deep(.custom-field) {
  background: #f8f9fa;
  border-radius: 16px;
  padding: 4px 0;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

:deep(.custom-field:focus-within) {
  border-color: #4ecdc4;
  background: white;
  box-shadow: 0 0 0 4px rgba(78, 205, 196, 0.1);
}

:deep(.custom-field .van-field__control) {
  font-size: 16px;
  padding: 16px 20px;
  color: #333;
}

:deep(.custom-field .van-field__control::placeholder) {
  color: #999;
}

.field-icon {
  color: #666;
  font-size: 20px;
  margin-left: 20px;
  margin-right: 12px;
}

.login-button {
  margin-top: 24px;
}

:deep(.custom-button) {
  height: 56px;
  font-size: 18px;
  font-weight: 600;
  background: linear-gradient(135deg, #4ecdc4 0%, #45b7d1 100%);
  border: none;
  box-shadow: 0 8px 24px rgba(78, 205, 196, 0.3);
  transition: all 0.3s ease;
}

:deep(.custom-button:active) {
  transform: translateY(1px);
  box-shadow: 0 4px 12px rgba(78, 205, 196, 0.3);
}

/* 提示信息 */
.login-tips {
  text-align: center;
}

.tip-item {
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  margin: 12px 0;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  backdrop-filter: blur(10px);
}

.tip-item .van-icon {
  margin-right: 8px;
  font-size: 16px;
}

.debug-tip {
  cursor: pointer;
  transition: all 0.3s ease;
}

.debug-tip:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

/* 桌面端装饰元素 */
.desktop-decoration {
  display: none;
}

@media (min-width: 768px) {
  .desktop-decoration {
    display: block;
  }

  /* 波浪装饰 */
  .wave {
    position: absolute;
    width: 200px;
    height: 200px;
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    animation: pulse 4s ease-in-out infinite;
  }

  .wave-1 {
    top: 15%;
    left: 15%;
    animation-delay: 0s;
  }

  .wave-2 {
    bottom: 20%;
    right: 20%;
    animation-delay: 2s;
    width: 150px;
    height: 150px;
  }

  /* 几何形状装饰 */
  .geometric-shape {
    position: absolute;
    background: rgba(255, 255, 255, 0.05);
    animation: rotate 15s linear infinite;
  }

  .shape-1 {
    width: 60px;
    height: 60px;
    top: 20%;
    right: 10%;
    clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
    animation-delay: 0s;
  }

  .shape-2 {
    width: 40px;
    height: 40px;
    bottom: 30%;
    left: 10%;
    clip-path: polygon(25% 0%, 100% 0%, 75% 100%, 0% 100%);
    animation-delay: 5s;
  }

  .shape-3 {
    width: 50px;
    height: 50px;
    top: 60%;
    right: 30%;
    clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
    animation-delay: 10s;
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.1;
  }
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-container {
    padding: 16px;
  }

  .title {
    font-size: 28px;
  }

  .login-form-wrapper {
    padding: 24px 20px;
  }

  .circle-1 {
    width: 80px;
    height: 80px;
  }

  .circle-2 {
    width: 60px;
    height: 60px;
  }

  .circle-3 {
    width: 40px;
    height: 40px;
  }
}
</style>
