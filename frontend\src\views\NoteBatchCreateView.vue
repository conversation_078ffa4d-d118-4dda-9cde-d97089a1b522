<template>
  <div class="page-container">
    <div class="page-header">
      <van-button
        icon="arrow-left"
        @click="router.back()"
        class="back-btn"
      >
        返回
      </van-button>
      <h1 class="page-title">批量创建笔记</h1>
      <div class="header-spacer"></div>
    </div>

    <div class="content">
      <div v-if="!selectedProduct" class="product-selection">
        <van-cell-group inset>
          <van-cell title="选择商品" is-link @click="showProductPicker = true">
            <template #value>
              <span class="placeholder">请选择要批量创建笔记的商品</span>
            </template>
          </van-cell>
        </van-cell-group>
        
        <div class="recent-products" v-if="recentProducts.length > 0">
          <h3>最近使用的商品</h3>
          <van-cell-group inset>
            <van-cell
              v-for="product in recentProducts"
              :key="product.id"
              :title="product.title || product.short_name"
              :label="product.description"
              is-link
              @click="selectProduct(product)"
            >
              <template #icon>
                <van-image
                  v-if="product.main_images?.[0]"
                  :src="product.main_images[0]"
                  width="40"
                  height="40"
                  fit="cover"
                  round
                />
                <div v-else class="placeholder-icon">
                  <van-icon name="goods-collect-o" />
                </div>
              </template>
            </van-cell>
          </van-cell-group>
        </div>
      </div>

      <div v-else class="batch-create-section">
        <van-cell-group inset>
          <van-cell
            :title="selectedProduct.title || selectedProduct.short_name"
            :label="selectedProduct.description"
          >
            <template #icon>
              <van-image
                v-if="selectedProduct.main_images?.[0]"
                :src="selectedProduct.main_images[0]"
                width="40"
                height="40"
                fit="cover"
                round
              />
              <div v-else class="placeholder-icon">
                <van-icon name="goods-collect-o" />
              </div>
            </template>
            <template #right-icon>
              <van-button size="mini" @click="selectedProduct = null">
                重新选择
              </van-button>
            </template>
          </van-cell>
        </van-cell-group>

        <!-- 批量创建表单 -->
        <div class="batch-form">
          <div class="form-header">
            <h3>批量创建笔记</h3>
            <div class="title-info">
              <span class="title-count">标题数量: {{ titleCount }}</span>
              <span v-if="duplicateCount > 0" class="warning-text">
                检测到 {{ duplicateCount }} 个重复标题
              </span>
            </div>
          </div>
          
          <div class="textarea-container">
            <div class="line-numbers">
              <div
                v-for="n in Math.max(lineCount, 10)"
                :key="n"
                class="line-number"
              >
                {{ n }}
              </div>
            </div>
            <textarea
              v-model="batchTitles"
              class="titles-textarea"
              placeholder="请输入笔记标题，每行一个&#10;支持无限制数量的笔记创建"
              @input="updateTitleCount"
              @scroll="syncScroll"
              ref="textareaRef"
            ></textarea>
          </div>
          
          <div class="action-buttons">
            <van-button
              type="primary"
              block
              round
              :loading="creating"
              :disabled="titleCount === 0"
              @click="handleBatchCreate"
            >
              创建 {{ titleCount }} 个笔记
            </van-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 商品选择器 -->
    <van-popup v-model:show="showProductPicker" position="bottom" :style="{ height: '70%' }">
      <div class="product-picker">
        <div class="picker-header">
          <h3>选择商品</h3>
          <van-button size="mini" @click="showProductPicker = false">关闭</van-button>
        </div>
        
        <van-search
          v-model="searchQuery"
          placeholder="搜索商品"
          @search="loadProducts"
          @clear="loadProducts"
        />
        
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="onLoad"
        >
          <van-cell
            v-for="product in products"
            :key="product.id"
            :title="product.title || product.short_name"
            :label="product.description"
            is-link
            @click="selectProduct(product)"
          >
            <template #icon>
              <van-image
                v-if="product.main_images?.[0]"
                :src="product.main_images[0]"
                width="40"
                height="40"
                fit="cover"
                round
              />
              <div v-else class="placeholder-icon">
                <van-icon name="goods-collect-o" />
              </div>
            </template>
          </van-cell>
        </van-list>
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showLoadingToast, closeToast } from 'vant'
import api from '@/utils/api'

const router = useRouter()

const selectedProduct = ref<any>(null)
const showProductPicker = ref(false)
const searchQuery = ref('')
const products = ref<any[]>([])
const recentProducts = ref<any[]>([])
const loading = ref(false)
const finished = ref(false)
const page = ref(1)
const pageSize = 20

// 批量创建相关
const batchTitles = ref('')
const titleCount = ref(0)
const duplicateCount = ref(0)
const creating = ref(false)
const textareaRef = ref<HTMLTextAreaElement>()

const lineCount = computed(() => {
  return Math.max(batchTitles.value.split('\n').length, 10)
})

const updateTitleCount = () => {
  const titles = batchTitles.value
    .split('\n')
    .map(t => t.trim())
    .filter(t => t)
  
  titleCount.value = titles.length
  duplicateCount.value = titles.length - new Set(titles).size
}

const syncScroll = () => {
  const textarea = textareaRef.value
  if (!textarea) return
  
  const lineNumbers = document.querySelector('.line-numbers') as HTMLElement
  if (lineNumbers) {
    lineNumbers.scrollTop = textarea.scrollTop
  }
}

const loadProducts = async (isRefresh = false) => {
  if (isRefresh) {
    page.value = 1
    finished.value = false
    products.value = []
  }
  
  if (loading.value || finished.value) return
  
  loading.value = true
  
  try {
    const params = {
      skip: (page.value - 1) * pageSize,
      limit: pageSize,
      q: searchQuery.value || undefined
    }
    
    const response = await api.get('/api/v1/products/', { params })
    const newProducts = response.data
    
    if (isRefresh) {
      products.value = newProducts
    } else {
      products.value.push(...newProducts)
    }
    
    if (newProducts.length < pageSize) {
      finished.value = true
    } else {
      page.value++
    }
  } catch (error) {
    console.error('Failed to load products:', error)
    showToast('加载商品失败')
  } finally {
    loading.value = false
  }
}

const loadRecentProducts = async () => {
  try {
    const response = await api.get('/api/v1/products/', { 
      params: { limit: 5, sort_by: 'updated_at', order: 'desc' } 
    })
    recentProducts.value = response.data
  } catch (error) {
    console.error('Failed to load recent products:', error)
  }
}

const onLoad = () => {
  loadProducts()
}

const selectProduct = (product: any) => {
  selectedProduct.value = product
  showProductPicker.value = false
}

const handleBatchCreate = async () => {
  const titles = batchTitles.value
    .split('\n')
    .map(t => t.trim())
    .filter(t => t)
  
  if (titles.length === 0) {
    showToast('请输入至少一个标题')
    return
  }
  
  creating.value = true
  
  try {
    showLoadingToast({
      message: '正在创建笔记...',
      forbidClick: true,
    })
    
    const response = await api.post(
      `/api/v1/products/${selectedProduct.value.id}/notes/batch`,
      { titles }
    )
    
    closeToast()
    
    if (response.data.success) {
      showToast(`成功创建 ${response.data.created_count} 个笔记`)
      
      // 跳转到商品的笔记列表页面
      router.push(`/products/${selectedProduct.value.id}/notes`)
    } else {
      showToast('创建失败')
    }
  } catch (error: any) {
    closeToast()
    console.error('Failed to batch create notes:', error)
    
    if (error.response?.data?.detail) {
      showToast(error.response.data.detail)
    } else {
      showToast('创建失败')
    }
  } finally {
    creating.value = false
  }
}

onMounted(() => {
  loadRecentProducts()
  loadProducts(true)
})
</script>

<style scoped>
.content {
  padding: 0;
}

.product-selection {
  padding: 16px 0;
}

.placeholder {
  color: #969799;
}

.recent-products {
  margin-top: 24px;
}

.recent-products h3 {
  margin: 0 16px 12px;
  font-size: 16px;
  font-weight: 500;
  color: #323233;
}

.batch-create-section {
  padding: 16px 0;
}

.placeholder-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #f7f8fa;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #969799;
}

.batch-form {
  margin-top: 24px;
  padding: 0 16px;
}

.form-header {
  margin-bottom: 16px;
}

.form-header h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 500;
  color: #323233;
}

.title-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.title-count {
  color: #1989fa;
  font-weight: 500;
}

.warning-text {
  color: #ff976a;
}

.textarea-container {
  position: relative;
  border: 1px solid #ebedf0;
  border-radius: 8px;
  overflow: hidden;
  background: white;
  display: flex;
  min-height: 400px;
  max-height: 60vh;
}

.line-numbers {
  width: 40px;
  background: #f7f8fa;
  border-right: 1px solid #ebedf0;
  padding: 12px 8px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 20px;
  color: #969799;
  overflow: hidden;
  user-select: none;
}

.line-number {
  text-align: right;
  height: 20px;
}

.titles-textarea {
  flex: 1;
  border: none;
  outline: none;
  padding: 12px;
  font-family: inherit;
  font-size: 14px;
  line-height: 20px;
  resize: none;
  background: transparent;
}

.action-buttons {
  margin-top: 24px;
}

.product-picker {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #ebedf0;
}

.picker-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.van-search {
  padding: 12px 16px;
}

.van-list {
  flex: 1;
  overflow-y: auto;
}
</style>
