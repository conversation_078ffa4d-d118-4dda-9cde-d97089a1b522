<template>
  <div class="page-container">
    <div class="page-header">
      <van-button
        icon="arrow-left"
        @click="router.back()"
        class="back-btn"
      >
        返回
      </van-button>
      <h1 class="page-title">创建笔记</h1>
      <div class="header-spacer"></div>
    </div>

    <div class="content">
      <div v-if="!selectedProduct" class="product-selection">
        <van-cell-group inset>
          <van-cell title="选择商品" is-link @click="showProductPicker = true">
            <template #value>
              <span class="placeholder">请选择要创建笔记的商品</span>
            </template>
          </van-cell>
        </van-cell-group>
        
        <div class="recent-products" v-if="recentProducts.length > 0">
          <h3>最近使用的商品</h3>
          <van-cell-group inset>
            <van-cell
              v-for="product in recentProducts"
              :key="product.id"
              :title="product.title || product.short_name"
              :label="getProductLabel(product)"
              is-link
              @click="selectProduct(product)"
            >
              <template #icon>
                <van-image
                  v-if="product.main_images?.[0]"
                  :src="product.main_images[0]"
                  width="40"
                  height="40"
                  fit="cover"
                  round
                />
                <div v-else class="placeholder-icon">
                  <van-icon name="goods-collect-o" />
                </div>
              </template>
            </van-cell>
          </van-cell-group>
        </div>
      </div>

      <div v-else class="selected-product">
        <van-cell-group inset>
          <van-cell
            :title="selectedProduct.title || selectedProduct.short_name"
            :label="getProductLabel(selectedProduct)"
          >
            <template #icon>
              <van-image
                v-if="selectedProduct.main_images?.[0]"
                :src="selectedProduct.main_images[0]"
                width="40"
                height="40"
                fit="cover"
                round
              />
              <div v-else class="placeholder-icon">
                <van-icon name="goods-collect-o" />
              </div>
            </template>
            <template #right-icon>
              <van-button size="mini" @click="selectedProduct = null">
                重新选择
              </van-button>
            </template>
          </van-cell>
        </van-cell-group>

        <div class="action-buttons">
          <van-button
            type="primary"
            block
            round
            @click="createNote"
          >
            创建笔记
          </van-button>
        </div>
      </div>
    </div>

    <!-- 商品选择器 -->
    <van-popup v-model:show="showProductPicker" position="bottom" :style="{ height: '70%' }">
      <div class="product-picker">
        <div class="picker-header">
          <h3>选择商品</h3>
          <van-button size="mini" @click="showProductPicker = false">关闭</van-button>
        </div>
        
        <van-search
          v-model="searchQuery"
          placeholder="搜索商品"
          @search="loadProducts"
          @clear="loadProducts"
        />
        
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="onLoad"
        >
          <van-cell
            v-for="product in products"
            :key="product.id"
            :title="product.title || product.short_name"
            :label="getProductLabel(product)"
            is-link
            @click="selectProduct(product)"
          >
            <template #icon>
              <van-image
                v-if="product.main_images?.[0]"
                :src="product.main_images[0]"
                width="40"
                height="40"
                fit="cover"
                round
              />
              <div v-else class="placeholder-icon">
                <van-icon name="goods-collect-o" />
              </div>
            </template>
          </van-cell>
        </van-list>
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import api from '@/utils/api'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const selectedProduct = ref<any>(null)
const showProductPicker = ref(false)
const searchQuery = ref('')
const products = ref<any[]>([])
const recentProducts = ref<any[]>([])
const loading = ref(false)
const finished = ref(false)
const page = ref(1)
const pageSize = 20

const loadProducts = async (isRefresh = false) => {
  if (isRefresh) {
    page.value = 1
    finished.value = false
    products.value = []
  }
  
  if (loading.value || finished.value) return
  
  loading.value = true
  
  try {
    const params = {
      skip: (page.value - 1) * pageSize,
      limit: pageSize,
      q: searchQuery.value || undefined
    }
    
    const response = await api.get('/api/v1/products/', { params })
    const newProducts = response.data
    
    if (isRefresh) {
      products.value = newProducts
    } else {
      products.value.push(...newProducts)
    }
    
    if (newProducts.length < pageSize) {
      finished.value = true
    } else {
      page.value++
    }
  } catch (error) {
    console.error('Failed to load products:', error)
    showToast('加载商品失败')
  } finally {
    loading.value = false
  }
}

const loadRecentProducts = async () => {
  try {
    const response = await api.get('/api/v1/products/', { 
      params: { limit: 5, sort_by: 'updated_at', order: 'desc' } 
    })
    recentProducts.value = response.data
  } catch (error) {
    console.error('Failed to load recent products:', error)
  }
}

const onLoad = () => {
  loadProducts()
}

const selectProduct = (product: any) => {
  selectedProduct.value = product
  showProductPicker.value = false
}

const getProductLabel = (product: any) => {
  let label = product.description || ''

  // 如果是管理员，显示归属用户信息
  if (authStore.isAdmin && product.owner_username) {
    label = label ? `${label} • 归属: ${product.owner_username}` : `归属: ${product.owner_username}`
  }

  return label
}

const createNote = () => {
  if (!selectedProduct.value) {
    showToast('请先选择商品')
    return
  }

  router.push(`/products/${selectedProduct.value.id}/notes/create`)
}

onMounted(() => {
  loadRecentProducts()
  loadProducts(true)
})
</script>

<style scoped>
.content {
  padding: 0;
}

.product-selection {
  padding: 16px 0;
}

.placeholder {
  color: #969799;
}

.recent-products {
  margin-top: 24px;
}

.recent-products h3 {
  margin: 0 16px 12px;
  font-size: 16px;
  font-weight: 500;
  color: #323233;
}

.selected-product {
  padding: 16px 0;
}

.action-buttons {
  margin-top: 24px;
  padding: 0 16px;
}

.placeholder-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #f7f8fa;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #969799;
}

.product-picker {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #ebedf0;
}

.picker-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.van-search {
  padding: 12px 16px;
}

.van-list {
  flex: 1;
  overflow-y: auto;
}
</style>
