<template>
  <div class="note-detail-container">
    <div class="page-header">
      <van-button
        icon="arrow-left"
        @click="router.back()"
        class="back-btn"
      >
        返回
      </van-button>
      <h1 class="page-title">笔记详情</h1>
      <div class="header-spacer"></div>
    </div>

    <div v-if="loading" class="loading-container">
      <van-loading size="24px">加载中...</van-loading>
    </div>

    <div v-else-if="note" class="note-content">
      <!-- 笔记基本信息 -->
      <van-cell-group inset>
        <van-cell title="基本信息" />
        <van-cell title="标题" :value="note.title" />
        <van-cell title="使用状态">
          <template #value>
            <van-tag :type="note.usage_status === 'UNUSED' ? 'primary' : 'success'">
              {{ note.usage_status === 'UNUSED' ? '未使用' : '已使用' }}
            </van-tag>
          </template>
        </van-cell>
        <van-cell title="创建时间" :value="formatDate(note.created_at)" />
        <van-cell v-if="note.tags" title="标签" :value="note.tags" />
        <van-cell v-if="note.product_name" title="关联商品" is-link @click="viewProduct">
          <template #value>
            <span class="product-link">{{ note.product_name }}</span>
          </template>
        </van-cell>
      </van-cell-group>

      <!-- 操作按钮 -->
      <van-cell-group inset>
        <van-cell title="操作" />
        <div class="action-buttons">
          <van-button
            type="primary"
            size="small"
            icon="edit"
            @click="editNote"
          >
            编辑笔记
          </van-button>

          <van-button
            type="success"
            size="small"
            icon="copy-o"
            @click="copyTitle"
          >
            复制标题
          </van-button>
          
          <van-button
            type="default"
            size="small"
            icon="description-o"
            @click="copyContent"
          >
            复制内容
          </van-button>
          
          <van-button 
            :type="note.usage_status === 'UNUSED' ? 'warning' : 'default'"
            size="small"
            :icon="note.usage_status === 'UNUSED' ? 'success' : 'revoke'"
            @click="toggleUsageStatus"
            :loading="statusLoading"
          >
            {{ note.usage_status === 'UNUSED' ? '标记已用' : '标记未用' }}
          </van-button>
        </div>
      </van-cell-group>

      <!-- 封面图片 -->
      <van-cell-group v-if="coverImages && coverImages.length > 0" inset>
        <van-cell title="封面候选图片" />
        <div class="images-section">
          <div class="images-grid">
            <div
              v-for="(image, index) in coverImages"
              :key="index"
              class="image-item"
            >
              <van-image
                :src="image"
                width="120"
                height="120"
                fit="cover"
                radius="8"
                @click="previewImages(coverImages, index)"
              />
              <van-button 
                size="mini" 
                type="primary"
                icon="down"
                @click="downloadImage(image, `cover_${index + 1}`)"
                class="download-btn"
              >
                下载
              </van-button>
            </div>
          </div>
        </div>
      </van-cell-group>

      <!-- 笔记图片 -->
      <van-cell-group v-if="noteImages && noteImages.length > 0" inset>
        <van-cell title="笔记图片" />
        <div class="images-section">
          <div class="images-grid">
            <div
              v-for="(image, index) in noteImages"
              :key="index"
              class="image-item"
            >
              <van-image
                :src="image"
                width="120"
                height="120"
                fit="cover"
                radius="8"
                @click="previewImages(noteImages, index)"
              />
              <van-button 
                size="mini" 
                type="primary"
                icon="down"
                @click="downloadImage(image, `note_${index + 1}`)"
                class="download-btn"
              >
                下载
              </van-button>
            </div>
          </div>
        </div>
      </van-cell-group>

      <!-- 视频 -->
      <van-cell-group v-if="noteVideos && noteVideos.length > 0" inset>
        <van-cell title="视频" />
        <div class="videos-section">
          <div
            v-for="(video, index) in noteVideos"
            :key="index"
            class="video-item"
          >
            <video
              :src="video"
              controls
              width="100%"
              height="200"
              class="video-player"
            >
              您的浏览器不支持视频播放
            </video>
            <van-button 
              size="small" 
              type="primary"
              icon="down"
              @click="downloadVideo(video, `video_${index + 1}`)"
              class="download-btn"
            >
              下载视频
            </van-button>
          </div>
        </div>
      </van-cell-group>

      <!-- 笔记正文 -->
      <van-cell-group v-if="note.body" inset>
        <van-cell title="笔记正文" />
        <div class="note-body">
          <div class="body-content">{{ note.body }}</div>
        </div>
      </van-cell-group>
    </div>

    <div v-else class="error-container">
      <van-empty description="笔记不存在或已被删除" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showToast, showImagePreview, showLoadingToast, closeToast } from 'vant'
import api from '@/utils/api'
import { copyWithToast } from '@/utils/clipboard'
import { downloadImageWithTimestamp, downloadVideoWithTimestamp, buildImageUrl, buildImageUrls } from '@/utils/downloadUtils'

const router = useRouter()
const route = useRoute()

const loading = ref(true)
const statusLoading = ref(false)
const note = ref<any>(null)

// 计算属性：处理图片URL
const coverImages = computed(() => {
  if (!note.value?.cover_images_candidate) return []
  return buildImageUrls(note.value.cover_images_candidate)
})

const noteImages = computed(() => {
  if (!note.value?.note_images) return []
  return buildImageUrls(note.value.note_images)
})

const noteVideos = computed(() => {
  if (!note.value?.videos) return []
  return buildImageUrls(note.value.videos) // 视频也使用相同的URL构建逻辑
})

const loadNoteDetail = async () => {
  try {
    loading.value = true
    const noteId = route.params.id
    const response = await api.get(`/api/v1/notes-download/${noteId}/content`)
    note.value = response.data
  } catch (error) {
    console.error('Failed to load note detail:', error)
    showToast('加载笔记详情失败')
  } finally {
    loading.value = false
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}



const viewProduct = () => {
  if (note.value?.product_id) {
    router.push(`/products/${note.value.product_id}/detail`)
  }
}

const editNote = () => {
  if (note.value?.id) {
    router.push(`/notes/${note.value.id}/edit`)
  }
}

const copyTitle = async () => {
  const title = note.value.title
  if (!title) {
    showToast('标题为空')
    return
  }
  await copyWithToast(title, showToast, showToast)
}

const copyContent = async () => {
  const content = note.value.body || note.value.title
  if (!content) {
    showToast('内容为空')
    return
  }
  await copyWithToast(content, showToast, showToast)
}

const downloadImage = (imageUrl: string, filename: string) => {
  downloadImageWithTimestamp(imageUrl, filename, showToast)
}

const downloadVideo = (videoUrl: string, filename: string) => {
  downloadVideoWithTimestamp(videoUrl, filename, showToast)
}

const previewImages = (images: string[], startIndex: number = 0) => {
  showImagePreview({
    images,
    startPosition: startIndex,
    closeable: true
  })
}

const toggleUsageStatus = async () => {
  try {
    statusLoading.value = true
    showLoadingToast({
      message: '更新中...',
      forbidClick: true,
    })

    const newStatus = note.value.usage_status === 'UNUSED' ? 'USED' : 'UNUSED'
    await api.put(`/api/v1/notes-download/${note.value.id}/usage-status`, {
      usage_status: newStatus
    })

    closeToast()
    note.value.usage_status = newStatus
    showToast(`已标记为${newStatus === 'UNUSED' ? '未使用' : '已使用'}`)
  } catch (error) {
    closeToast()
    console.error('Failed to update usage status:', error)
    showToast('更新失败')
  } finally {
    statusLoading.value = false
  }
}

onMounted(() => {
  loadNoteDetail()
})
</script>

<style scoped>
.note-detail-container {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.loading-container, .error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.note-content {
  padding: 0 16px 16px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  padding: 16px;
}

.images-section, .videos-section {
  padding: 16px;
}

.images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 16px;
}

.image-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.download-btn {
  width: 100%;
}

.video-item {
  margin-bottom: 16px;
}

.video-player {
  border-radius: 8px;
  margin-bottom: 8px;
}

.note-body {
  padding: 16px;
}

.body-content {
  line-height: 1.6;
  color: #323233;
  white-space: pre-wrap;
  word-break: break-word;
}

.product-link {
  color: #1989fa;
  font-weight: 500;
}
</style>
