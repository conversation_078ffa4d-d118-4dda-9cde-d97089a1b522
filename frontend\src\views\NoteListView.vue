<template>
  <div class="note-list-container">
    <div class="page-header">
      <van-button
        icon="arrow-left"
        @click="router.back()"
        class="back-btn"
      >
        返回
      </van-button>
      <h1 class="page-title">笔记管理</h1>
      <div class="header-actions">
        <van-button
          type="primary"
          icon="plus"
          @click="createNote"
          class="create-btn"
          size="small"
        >
          新建
        </van-button>
        <van-button
          type="success"
          icon="apps-o"
          @click="showBatchCreateDialog"
          class="batch-btn"
          size="small"
        >
          批量创建
        </van-button>
      </div>
    </div>
    
    <div class="product-info" v-if="product">
      <van-cell-group inset>
        <van-cell
          :title="product.title || product.short_name"
          :label="product.description"
          icon="goods-collect-o"
        />
      </van-cell-group>
    </div>
    
    <div class="note-list">
      <van-cell-group inset>
        <van-cell
          v-for="(note, index) in notes"
          :key="note.id"
          :title="`${index + 1}. ${note.title}`"
          :label="`状态：${note.status} | 创建时间：${formatDate(note.created_at)}`"
          is-link
          @click="editNote(note.id)"
        >
          <template #value>
            <div class="note-indicators">
              <van-icon
                v-if="hasContent(note)"
                name="notes-o"
                color="#1989fa"
                size="16"
                title="有内容"
              />
              <van-icon
                v-if="hasCoverImage(note)"
                name="photo-o"
                color="#52c41a"
                size="16"
                title="有封面图"
              />
              <van-icon
                v-if="hasNoteImages(note)"
                name="photograph"
                color="#ff976a"
                size="16"
                title="有笔记图"
              />
              <van-icon
                v-if="hasVideos(note)"
                name="video-o"
                color="#722ed1"
                size="16"
                title="有视频"
              />
            </div>
          </template>
          <template #right-icon>
            <van-tag :type="note.status === '已发布' ? 'success' : 'warning'">
              {{ note.status }}
            </van-tag>
          </template>
        </van-cell>
        
        <van-empty
          v-if="notes.length === 0"
          description="暂无笔记数据"
        >
          <van-button type="primary" @click="createNote">
            创建第一篇笔记
          </van-button>
        </van-empty>
      </van-cell-group>
    </div>

    <!-- 批量创建对话框 -->
    <van-popup
      v-model:show="showBatchDialog"
      position="center"
      :style="{
        width: dialogWidth + 'px',
        height: dialogHeight + 'px',
        maxWidth: '90vw',
        maxHeight: '90vh'
      }"
      round
      closeable
      close-icon-position="top-right"
    >
      <div class="batch-create-dialog" ref="dialogRef">
        <div class="dialog-header">
          <h3>批量创建笔记</h3>
          <div class="title-info">
            <span class="title-count">标题数量: {{ titleCount }}</span>
            <span v-if="duplicateCount > 0" class="warning-text">
              检测到 {{ duplicateCount }} 个重复标题
            </span>
          </div>
        </div>

        <div class="dialog-content">
          <div class="textarea-container">
            <div class="line-numbers">
              <div
                v-for="n in Math.max(lineCount, 20)"
                :key="n"
                class="line-number"
              >
                {{ n }}
              </div>
            </div>
            <textarea
              v-model="batchTitles"
              class="titles-textarea"
              placeholder="请输入笔记标题，每行一个&#10;支持无限制数量的笔记创建"
              @input="updateTitleCount"
              @scroll="syncScroll"
              ref="textareaRef"
            ></textarea>
          </div>
        </div>

        <div class="dialog-footer">
          <van-button @click="closeBatchDialog">取消</van-button>
          <van-button
            type="primary"
            :loading="batchCreating"
            :disabled="titleCount === 0"
            @click="handleBatchCreate"
          >
            创建 {{ titleCount }} 个笔记
          </van-button>
        </div>

        <!-- 拖拽调整大小的手柄 -->
        <div
          class="resize-handle resize-handle-se"
          @mousedown="startResize"
        ></div>
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showToast, showLoadingToast, closeToast } from 'vant'
import api from '@/utils/api'

const router = useRouter()
const route = useRoute()

const product = ref<any>(null)
const notes = ref<any[]>([])

// 批量创建相关状态
const showBatchDialog = ref(false)
const batchTitles = ref('')
const titleCount = ref(0)
const duplicateCount = ref(0)
const batchCreating = ref(false)

// 对话框大小和拖拽
const dialogWidth = ref(600)
const dialogHeight = ref(500)
const dialogRef = ref<HTMLElement>()
const textareaRef = ref<HTMLTextAreaElement>()
const isResizing = ref(false)

const loadProduct = async () => {
  try {
    const response = await api.get(`/api/v1/products/${route.params.productId}`)
    product.value = response.data
  } catch (error) {
    console.error('Failed to load product:', error)
    showToast('加载商品信息失败')
  }
}

const loadNotes = async () => {
  try {
    const response = await api.get(`/api/v1/products/${route.params.productId}/notes/`)
    notes.value = response.data
  } catch (error) {
    console.error('Failed to load notes:', error)
    showToast('加载笔记失败')
  }
}

const createNote = () => {
  router.push(`/products/${route.params.productId}/notes/create`)
}

const editNote = (noteId: number) => {
  router.push(`/notes/${noteId}/edit`)
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 判断笔记是否有内容
const hasContent = (note: any) => {
  return note.body && note.body.trim().length > 0
}

// 判断笔记是否有封面图
const hasCoverImage = (note: any) => {
  return note.selected_cover_image || (note.cover_images_candidate && note.cover_images_candidate.length > 0)
}

// 判断笔记是否有笔记图
const hasNoteImages = (note: any) => {
  return note.note_images && note.note_images.length > 0
}

// 判断笔记是否有视频
const hasVideos = (note: any) => {
  return note.videos && note.videos.length > 0
}

// 计算属性
const lineCount = computed(() => {
  return Math.max(batchTitles.value.split('\n').length, 20)
})

// 批量创建相关函数
const showBatchCreateDialog = () => {
  showBatchDialog.value = true
  batchTitles.value = ''
  titleCount.value = 0
  duplicateCount.value = 0

  // 设置初始对话框大小
  const screenWidth = window.innerWidth
  const screenHeight = window.innerHeight
  dialogWidth.value = Math.min(800, screenWidth * 0.9)
  dialogHeight.value = Math.min(600, screenHeight * 0.9)
}

const closeBatchDialog = () => {
  showBatchDialog.value = false
  batchTitles.value = ''
  titleCount.value = 0
  duplicateCount.value = 0
}

const updateTitleCount = () => {
  const titles = batchTitles.value
    .split('\n')
    .map(t => t.trim())
    .filter(t => t)

  titleCount.value = titles.length
  duplicateCount.value = titles.length - new Set(titles).size
}

const syncScroll = () => {
  const textarea = textareaRef.value
  if (!textarea) return

  const lineNumbers = document.querySelector('.line-numbers') as HTMLElement
  if (lineNumbers) {
    lineNumbers.scrollTop = textarea.scrollTop
  }
}

// 拖拽调整大小
const startResize = (e: MouseEvent) => {
  e.preventDefault()
  isResizing.value = true

  const startX = e.clientX
  const startY = e.clientY
  const startWidth = dialogWidth.value
  const startHeight = dialogHeight.value

  const handleMouseMove = (e: MouseEvent) => {
    if (!isResizing.value) return

    const deltaX = e.clientX - startX
    const deltaY = e.clientY - startY

    dialogWidth.value = Math.max(400, startWidth + deltaX)
    dialogHeight.value = Math.max(300, startHeight + deltaY)
  }

  const handleMouseUp = () => {
    isResizing.value = false
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
  }

  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

const handleBatchCreate = async () => {
  const titles = batchTitles.value
    .split('\n')
    .map(t => t.trim())
    .filter(t => t)

  if (titles.length === 0) {
    showToast('请输入至少一个标题')
    return
  }

  batchCreating.value = true

  try {
    showLoadingToast({
      message: '正在创建笔记...',
      forbidClick: true,
    })

    const response = await api.post(
      `/api/v1/products/${route.params.productId}/notes/batch`,
      { titles }
    )

    closeToast()

    if (response.data.success) {
      showToast(`成功创建 ${response.data.created_count} 个笔记`)

      // 重新加载笔记列表
      await loadNotes()

      // 关闭对话框
      closeBatchDialog()
    } else {
      showToast('创建失败')
    }
  } catch (error: any) {
    closeToast()
    console.error('Failed to batch create notes:', error)

    if (error.response?.data?.detail) {
      showToast(error.response.data.detail)
    } else {
      showToast('创建失败')
    }
  } finally {
    batchCreating.value = false
  }
}

onMounted(() => {
  loadProduct()
  loadNotes()
})
</script>

<style scoped>
.note-list-container {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.batch-btn {
  background-color: #52c41a;
  border-color: #52c41a;
}

.product-info {
  padding: 16px;
}

.note-list {
  padding: 0 16px;
}

.note-indicators {
  display: flex;
  gap: 6px;
  align-items: center;
  margin-right: 8px;
}

.note-indicators .van-icon {
  opacity: 0.8;
}

/* 批量创建对话框样式 */
.batch-create-dialog {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.dialog-header {
  padding: 16px;
  border-bottom: 1px solid #ebedf0;
  flex-shrink: 0;
}

.dialog-header h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 500;
  color: #323233;
}

.title-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.title-count {
  color: #1989fa;
  font-weight: 500;
}

.warning-text {
  color: #ff976a;
}

.dialog-content {
  flex: 1;
  padding: 16px;
  overflow: hidden;
}

.textarea-container {
  position: relative;
  border: 1px solid #ebedf0;
  border-radius: 8px;
  overflow: hidden;
  background: white;
  display: flex;
  height: 100%;
}

.line-numbers {
  width: 50px;
  background: #f7f8fa;
  border-right: 1px solid #ebedf0;
  padding: 12px 8px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 20px;
  color: #969799;
  overflow: hidden;
  user-select: none;
}

.line-number {
  text-align: right;
  height: 20px;
}

.titles-textarea {
  flex: 1;
  border: none;
  outline: none;
  padding: 12px;
  font-family: inherit;
  font-size: 14px;
  line-height: 20px;
  resize: none;
  background: transparent;
}

.dialog-footer {
  padding: 16px;
  border-top: 1px solid #ebedf0;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  flex-shrink: 0;
}

/* 拖拽调整大小手柄 */
.resize-handle {
  position: absolute;
  background: transparent;
}

.resize-handle-se {
  bottom: 0;
  right: 0;
  width: 20px;
  height: 20px;
  cursor: se-resize;
}

.resize-handle-se::after {
  content: '';
  position: absolute;
  bottom: 3px;
  right: 3px;
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-bottom: 8px solid #ddd;
}

.resize-handle-se:hover::after {
  border-bottom-color: #999;
}
</style>
