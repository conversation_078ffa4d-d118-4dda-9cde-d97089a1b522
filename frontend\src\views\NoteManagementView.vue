<template>
  <div class="note-management-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>笔记管理</h1>
      <van-button type="primary" @click="createNote">
        新建笔记
      </van-button>
    </div>

    <!-- 统计信息 -->
    <div class="stats-section" v-if="!statsLoading">
      <van-row gutter="16">
        <van-col span="6">
          <div class="stat-card">
            <div class="stat-number">{{ stats.total_notes || 0 }}</div>
            <div class="stat-label">总笔记</div>
          </div>
        </van-col>
        <van-col span="6">
          <div class="stat-card">
            <div class="stat-number">{{ stats.unused_notes || 0 }}</div>
            <div class="stat-label">未使用</div>
          </div>
        </van-col>
        <van-col span="6">
          <div class="stat-card">
            <div class="stat-number">{{ stats.used_notes || 0 }}</div>
            <div class="stat-label">已使用</div>
          </div>
        </van-col>
        <van-col span="6">
          <div class="stat-card">
            <div class="stat-number">{{ Math.round(stats.usage_rate || 0) }}%</div>
            <div class="stat-label">使用率</div>
          </div>
        </van-col>
      </van-row>
    </div>

    <!-- 搜索和筛选 -->
    <div class="filter-section">
      <van-search
        v-model="searchKeyword"
        placeholder="搜索笔记标题"
        @search="handleSearch"
        @clear="handleSearch"
      />

      <!-- 商品搜索 -->
      <van-search
        v-model="productSearchKeyword"
        placeholder="搜索商品名称"
        @search="handleProductSearch"
        @clear="handleProductSearch"
        style="margin-top: 12px;"
      />

      <van-row gutter="16" style="margin-top: 16px;">
        <van-col span="12">
          <van-field
            v-model="usageFilterText"
            is-link
            readonly
            label="使用状态"
            placeholder="选择状态"
            @click="showUsageStatusPicker = true"
          />
        </van-col>
        <van-col span="12">
          <van-field
            v-model="productFilterText"
            is-link
            readonly
            label="关联商品"
            placeholder="选择商品"
            @click="showProductPicker = true"
          />
        </van-col>
      </van-row>
    </div>

    <!-- 笔记列表 -->
    <div class="notes-section">
      <van-loading v-if="loading && notes.length === 0" size="24px">
        加载中...
      </van-loading>

      <div v-else-if="notes.length > 0" class="notes-grid">
        <div
          v-for="note in notes"
          :key="note.id"
          class="note-card"
          @click="viewNote(note.id)"
        >
          <div class="note-header">
            <h4 class="note-title">{{ note.title || '无标题' }}</h4>
            <div class="note-badges">
              <van-tag
                :type="note.usage_status === 'UNUSED' ? 'warning' : 'success'"
                size="medium"
                class="usage-badge"
              >
                <van-icon
                  :name="note.usage_status === 'UNUSED' ? 'clock-o' : 'success'"
                  size="12"
                />
                {{ note.usage_status === 'UNUSED' ? '未使用' : '已使用' }}
              </van-tag>
              <van-tag
                v-if="note.product_name"
                type="primary"
                class="product-badge"
              >
                {{ note.product_name }}
              </van-tag>
            </div>
          </div>

          <div class="note-content">
            <p class="note-desc">{{ getShortDesc(note.body) }}</p>
          </div>

          <div class="note-footer">
            <span class="meta-time">{{ formatDate(note.created_at) }}</span>
          </div>

          <div class="note-actions" @click.stop>
            <van-button size="mini" plain @click="editNote(note.id)">编辑</van-button>
            <van-button
              size="mini"
              plain
              :type="note.usage_status === 'UNUSED' ? 'warning' : 'success'"
              @click="toggleUsageStatus(note)"
            >
              {{ note.usage_status === 'UNUSED' ? '标记已用' : '标记未用' }}
            </van-button>
            <van-button
              size="mini"
              plain
              type="danger"
              @click="deleteNote(note.id)"
            >
              删除
            </van-button>
          </div>
        </div>
      </div>

      <van-empty v-else description="暂无笔记数据">
        <van-button type="primary" @click="createNote">
          创建第一篇笔记
        </van-button>
      </van-empty>
    </div>

    <!-- 使用状态选择器 -->
    <van-popup v-model:show="showUsageStatusPicker" position="bottom">
      <van-picker
        :columns="usageStatusOptions"
        @confirm="onUsageStatusConfirm"
        @cancel="showUsageStatusPicker = false"
      />
    </van-popup>

    <!-- 商品选择器 -->
    <van-popup v-model:show="showProductPicker" position="bottom">
      <van-picker
        :columns="productOptions"
        @confirm="onProductConfirm"
        @cancel="showProductPicker = false"
      />
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showConfirmDialog } from 'vant'
import api from '@/utils/api'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const statsLoading = ref(false)
const searchKeyword = ref('')
const productSearchKeyword = ref('')
const usageFilter = ref('all')
const productFilter = ref('all')
const usageFilterText = ref('全部状态')
const productFilterText = ref('全部商品')
const showUsageStatusPicker = ref(false)
const showProductPicker = ref(false)

const notes = ref<any[]>([])
const products = ref<any[]>([])
const stats = ref({
  total_notes: 0,
  unused_notes: 0,
  used_notes: 0,
  usage_rate: 0
})

// 选择器选项
const usageStatusOptions = [
  { text: '全部状态', value: 'all' },
  { text: '未使用', value: 'UNUSED' },
  { text: '已使用', value: 'USED' }
]

const productOptions = ref([
  { text: '全部商品', value: 'all' }
])

// 工具函数
const formatDate = (dateString: string) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const getShortDesc = (content: string) => {
  if (!content) return '暂无内容'
  return content.length > 50 ? content.substring(0, 50) + '...' : content
}

// API调用
const loadStats = async () => {
  try {
    statsLoading.value = true
    const response = await api.get('/api/v1/notes-download/stats')
    stats.value = response.data
  } catch (error) {
    console.error('Failed to load stats:', error)
    showToast('加载统计信息失败')
  } finally {
    statsLoading.value = false
  }
}

const loadNotes = async () => {
  try {
    loading.value = true
    const params: any = {
      skip: 0,
      limit: 100
    }

    // 构建搜索关键词：笔记搜索 + 商品搜索
    let searchQuery = ''
    if (searchKeyword.value) {
      searchQuery += searchKeyword.value
    }
    if (productSearchKeyword.value) {
      if (searchQuery) {
        searchQuery += ' ' + productSearchKeyword.value
      } else {
        searchQuery = productSearchKeyword.value
      }
    }

    if (searchQuery) {
      params.q = searchQuery
    }

    if (usageFilter.value !== 'all') {
      params.usage_status = usageFilter.value
    }

    if (productFilter.value !== 'all') {
      params.product_id = productFilter.value
    }

    const response = await api.get('/api/v1/notes-download/preview', { params })
    notes.value = Array.isArray(response.data) ? response.data : []
  } catch (error) {
    console.error('Failed to load notes:', error)
    showToast('加载笔记失败')
    notes.value = []
  } finally {
    loading.value = false
  }
}

const loadProducts = async () => {
  try {
    const params: any = { skip: 0, limit: 100 }

    // 如果有商品搜索关键词，添加到参数中
    if (productSearchKeyword.value) {
      params.q = productSearchKeyword.value
    }

    const response = await api.get('/api/v1/products/', { params })
    products.value = Array.isArray(response.data) ? response.data : []

    // 更新商品选择器选项
    productOptions.value = [
      { text: '全部商品', value: 'all' },
      ...products.value.map(product => ({
        text: product.title || product.short_name || `商品${product.id}`,
        value: String(product.id)
      }))
    ]
  } catch (error) {
    console.error('Failed to load products:', error)
  }
}

// 事件处理
const handleSearch = () => {
  loadNotes()
}

const handleProductSearch = () => {
  // 商品搜索时，同时更新商品选项和重新加载笔记
  loadProducts()
  loadNotes()
}

const onUsageStatusConfirm = ({ selectedValues }: any) => {
  usageFilter.value = selectedValues[0]
  usageFilterText.value = usageStatusOptions.find(opt => opt.value === selectedValues[0])?.text || '全部状态'
  showUsageStatusPicker.value = false
  loadNotes()
}

const onProductConfirm = ({ selectedValues }: any) => {
  productFilter.value = selectedValues[0]
  productFilterText.value = productOptions.value.find(opt => opt.value === selectedValues[0])?.text || '全部商品'
  showProductPicker.value = false
  loadNotes()
}

const createNote = () => {
  router.push('/notes/create')
}

const viewNote = (noteId: number) => {
  router.push(`/notes/${noteId}/detail`)
}

const editNote = (noteId: number) => {
  router.push(`/notes/${noteId}/edit`)
}

const toggleUsageStatus = async (note: any) => {
  try {
    const newStatus = note.usage_status === 'UNUSED' ? 'USED' : 'UNUSED'
    await api.put(`/api/v1/notes-download/${note.id}/usage-status`, {
      usage_status: newStatus
    })
    showToast('状态更新成功')
    loadNotes()
    loadStats()
  } catch (error) {
    console.error('Failed to update usage status:', error)
    showToast('状态更新失败')
  }
}

const deleteNote = async (noteId: number) => {
  try {
    await showConfirmDialog({
      title: '确认删除',
      message: '确定要删除这篇笔记吗？此操作不可恢复。',
    })
    
    await api.delete(`/api/v1/notes/${noteId}`)
    showToast('删除成功')
    loadNotes()
    loadStats()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('Failed to delete note:', error)
      showToast('删除失败')
    }
  }
}

// 页面初始化
onMounted(() => {
  loadProducts()
  loadStats()
  loadNotes()
})
</script>

<style scoped>
.note-management-page {
  padding: 16px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-header h1 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

.stats-section {
  margin-bottom: 20px;
}

.stat-card {
  background: white;
  padding: 16px;
  border-radius: 8px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #1989fa;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

.filter-section {
  background: white;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.notes-section {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.notes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
}

.note-card {
  border: 1px solid #eee;
  border-radius: 8px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fafafa;
  display: flex;
  flex-direction: column;
  height: 200px;
}

.note-card:hover {
  border-color: #1989fa;
  box-shadow: 0 4px 12px rgba(25, 137, 250, 0.15);
  transform: translateY(-2px);
}

.note-header {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 12px;
}

.note-badges {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  align-items: center;
}

.usage-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: 500;
}

.product-badge {
  font-size: 10px;
  opacity: 0.8;
  padding: 2px 6px;
  line-height: 1.2;
}

.note-title {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
  flex: 1;
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.note-content {
  flex: 1;
  margin-bottom: 8px;
}

.note-desc {
  margin: 0;
  font-size: 12px;
  color: #666;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.note-footer {
  margin-bottom: 8px;
}

.meta-time {
  font-size: 11px;
  color: #999;
}

.note-actions {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.note-actions .van-button {
  flex: 1;
  min-width: 50px;
  font-size: 11px;
  height: 24px;
  line-height: 22px;
}

/* 响应式设计 */
@media (min-width: 1200px) {
  .notes-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  }
}

@media (max-width: 768px) {
  .note-management-page {
    padding: 12px;
  }

  .page-header {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }

  .notes-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 12px;
  }

  .note-card {
    height: 160px;
    padding: 10px;
  }

  .note-title {
    font-size: 13px;
  }

  .note-desc {
    font-size: 11px;
    -webkit-line-clamp: 2;
  }

  .note-actions .van-button {
    min-width: 45px;
    font-size: 10px;
    height: 22px;
    line-height: 20px;
  }
}

@media (max-width: 480px) {
  .notes-grid {
    grid-template-columns: 1fr;
  }

  .note-card {
    height: 140px;
  }

  .note-actions {
    gap: 2px;
  }

  .note-actions .van-button {
    min-width: 40px;
    padding: 0 4px;
  }
}
</style>
