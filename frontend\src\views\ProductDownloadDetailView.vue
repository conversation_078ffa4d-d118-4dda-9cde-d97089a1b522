<template>
  <div class="product-detail-container">
    <div class="page-header">
      <van-button
        icon="arrow-left"
        @click="router.back()"
        class="back-btn"
      >
        返回
      </van-button>
      <h1 class="page-title">商品详情</h1>
      <div class="header-spacer"></div>
    </div>

    <div v-if="loading" class="loading-container">
      <van-loading size="24px">加载中...</van-loading>
    </div>

    <div v-else-if="product" class="product-content">
      <!-- 商品基本信息 -->
      <van-cell-group inset>
        <van-cell title="基本信息" />
        <van-cell title="商品标题" :value="product.title || product.short_name" />
        <van-cell title="商品简称" :value="product.short_name" />
        <van-cell v-if="product.price" title="价格" :value="`¥${product.price}`" />
        <van-cell title="笔记数量" :value="`${product.notes_count || 0}篇`" />
        <van-cell title="创建时间" :value="formatDate(product.created_at)" />
        <van-cell v-if="product.owner_username" title="归属用户" :value="product.owner_username" />
      </van-cell-group>

      <!-- 操作按钮 -->
      <van-cell-group inset>
        <van-cell title="操作" />
        <div class="action-buttons">
          <van-button
            type="primary"
            size="small"
            icon="edit"
            @click="editProduct"
          >
            编辑商品
          </van-button>

          <van-button
            type="success"
            size="small"
            icon="copy-o"
            @click="copyTitle"
          >
            复制标题
          </van-button>

          <van-button
            type="default"
            size="small"
            icon="description-o"
            @click="copyDescription"
          >
            复制描述
          </van-button>

          <van-button
            v-if="product.feature_analysis"
            type="warning"
            size="small"
            icon="notes-o"
            @click="copyFeatureAnalysis"
          >
            复制特点分析
          </van-button>

          <van-button
            v-if="allImages.length > 0"
            type="default"
            size="small"
            icon="down"
            @click="downloadAllImages"
          >
            下载所有图片
          </van-button>
        </div>
      </van-cell-group>

      <!-- 商品描述 -->
      <van-cell-group v-if="product.description" inset>
        <van-cell title="商品描述" />
        <div class="description-content">
          <div class="description-text">{{ product.description }}</div>
        </div>
      </van-cell-group>

      <!-- 特点分析 -->
      <van-cell-group v-if="product.feature_analysis" inset>
        <van-cell title="特点分析" />
        <div class="analysis-content">
          <div class="analysis-text">{{ product.feature_analysis }}</div>
        </div>
      </van-cell-group>

      <!-- 主图 -->
      <van-cell-group v-if="mainImages && mainImages.length > 0" inset>
        <van-cell title="商品主图" />
        <div class="images-section">
          <div class="images-grid">
            <div
              v-for="(image, index) in mainImages"
              :key="index"
              class="image-item"
            >
              <van-image
                :src="image"
                width="120"
                height="120"
                fit="cover"
                radius="8"
                @click="previewImages(mainImages, index)"
              />
              <van-button 
                size="mini" 
                type="primary"
                icon="down"
                @click="downloadImage(image, `main_${index + 1}`)"
                class="download-btn"
              >
                下载
              </van-button>
            </div>
          </div>
        </div>
      </van-cell-group>

      <!-- 详情图 -->
      <van-cell-group v-if="detailImages && detailImages.length > 0" inset>
        <van-cell title="商品详情图" />
        <div class="images-section">
          <div class="images-grid">
            <div
              v-for="(image, index) in detailImages"
              :key="index"
              class="image-item"
            >
              <van-image
                :src="image"
                width="120"
                height="120"
                fit="cover"
                radius="8"
                @click="previewImages(detailImages, index)"
              />
              <van-button 
                size="mini" 
                type="primary"
                icon="down"
                @click="downloadImage(image, `detail_${index + 1}`)"
                class="download-btn"
              >
                下载
              </van-button>
            </div>
          </div>
        </div>
      </van-cell-group>
    </div>

    <div v-else class="error-container">
      <van-empty description="商品不存在或已被删除" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showToast, showImagePreview } from 'vant'
import api from '@/utils/api'
import { downloadImageWithTimestamp, downloadMultipleImagesWithTimestamp, buildImageUrls } from '@/utils/downloadUtils'

const router = useRouter()
const route = useRoute()

const loading = ref(true)
const product = ref<any>(null)

// 计算属性：处理图片URL
const mainImages = computed(() => {
  if (!product.value?.main_images) return []
  return buildImageUrls(product.value.main_images)
})

const detailImages = computed(() => {
  if (!product.value?.detail_images) return []
  return buildImageUrls(product.value.detail_images)
})

const allImages = computed(() => {
  return [...mainImages.value, ...detailImages.value]
})

const loadProductDetail = async () => {
  try {
    loading.value = true
    const productId = route.params.id
    const response = await api.get(`/api/v1/products-download/${productId}/content`)
    product.value = response.data
  } catch (error) {
    console.error('Failed to load product detail:', error)
    showToast('加载商品详情失败')
  } finally {
    loading.value = false
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const editProduct = () => {
  if (product.value?.id) {
    router.push(`/products/${product.value.id}/edit`)
  }
}

// 降级复制函数，用于不支持现代剪贴板API的浏览器
const fallbackCopyToClipboard = (text: string) => {
  const textArea = document.createElement('textarea')
  textArea.value = text
  textArea.style.position = 'fixed'
  textArea.style.top = '0'
  textArea.style.left = '0'
  textArea.style.width = '2em'
  textArea.style.height = '2em'
  textArea.style.padding = '0'
  textArea.style.border = 'none'
  textArea.style.outline = 'none'
  textArea.style.boxShadow = 'none'
  textArea.style.background = 'transparent'
  textArea.style.opacity = '0'

  document.body.appendChild(textArea)
  textArea.focus()
  textArea.select()

  try {
    const successful = document.execCommand('copy')
    if (!successful) {
      throw new Error('document.execCommand("copy") failed')
    }
  } finally {
    document.body.removeChild(textArea)
  }
}

const copyTitle = async () => {
  const title = product.value.title || product.value.short_name
  try {
    if (navigator.clipboard && navigator.clipboard.writeText) {
      await navigator.clipboard.writeText(title)
      showToast('标题已复制到剪贴板')
    } else {
      fallbackCopyToClipboard(title)
      showToast('标题已复制到剪贴板')
    }
  } catch (error) {
    console.error('复制失败:', error)
    try {
      fallbackCopyToClipboard(title)
      showToast('标题已复制到剪贴板')
    } catch (fallbackError) {
      console.error('降级复制也失败:', fallbackError)
      showToast('复制失败，请手动复制')
    }
  }
}

const copyDescription = async () => {
  const description = product.value.description || '暂无描述'
  try {
    if (navigator.clipboard && navigator.clipboard.writeText) {
      await navigator.clipboard.writeText(description)
      showToast('描述已复制到剪贴板')
    } else {
      fallbackCopyToClipboard(description)
      showToast('描述已复制到剪贴板')
    }
  } catch (error) {
    console.error('复制失败:', error)
    try {
      fallbackCopyToClipboard(description)
      showToast('描述已复制到剪贴板')
    } catch (fallbackError) {
      console.error('降级复制也失败:', fallbackError)
      showToast('复制失败，请手动复制')
    }
  }
}

const copyFeatureAnalysis = async () => {
  const analysis = product.value.feature_analysis || ''
  try {
    if (navigator.clipboard && navigator.clipboard.writeText) {
      await navigator.clipboard.writeText(analysis)
      showToast('特点分析已复制到剪贴板')
    } else {
      fallbackCopyToClipboard(analysis)
      showToast('特点分析已复制到剪贴板')
    }
  } catch (error) {
    console.error('复制失败:', error)
    try {
      fallbackCopyToClipboard(analysis)
      showToast('特点分析已复制到剪贴板')
    } catch (fallbackError) {
      console.error('降级复制也失败:', fallbackError)
      showToast('复制失败，请手动复制')
    }
  }
}

const downloadImage = (imageUrl: string, filename: string) => {
  downloadImageWithTimestamp(imageUrl, filename, showToast)
}

const downloadAllImages = () => {
  if (allImages.value.length === 0) {
    showToast('没有可下载的图片')
    return
  }

  const baseNamePrefix = `product_${product.value.id}_image`
  downloadMultipleImagesWithTimestamp(allImages.value, baseNamePrefix, showToast)
}

const previewImages = (images: string[], startIndex: number = 0) => {
  showImagePreview({
    images,
    startPosition: startIndex,
    closeable: true
  })
}

onMounted(() => {
  loadProductDetail()
})
</script>

<style scoped>
.product-detail-container {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.loading-container, .error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.product-content {
  padding: 0 16px 16px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  padding: 16px;
}

.description-content, .analysis-content {
  padding: 16px;
}

.description-text, .analysis-text {
  line-height: 1.6;
  color: #323233;
  white-space: pre-wrap;
  word-break: break-word;
}

.images-section {
  padding: 16px;
}

.images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 16px;
}

.image-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.download-btn {
  width: 100%;
}
</style>
