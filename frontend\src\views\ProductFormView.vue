<template>
  <div class="page-container">
    <div class="page-header">
      <van-button
        icon="arrow-left"
        @click="router.back()"
        class="back-btn"
      >
        返回
      </van-button>
      <h1 class="page-title">{{ isEdit ? '编辑商品' : '新建商品' }}</h1>
      <div class="header-spacer"></div>
    </div>

    <div class="form-content">
      <van-form @submit="handleSubmit">
        <van-cell-group inset>
          <van-field
            v-model="form.short_name"
            name="short_name"
            label="商品简称"
            placeholder="请输入商品简称"
            :rules="[{ required: true, message: '请输入商品简称' }]"
          />
          <van-field
            v-model="form.title"
            name="title"
            label="商品标题"
            placeholder="请输入商品标题"
          />
          <van-field
            v-model="form.price"
            name="price"
            label="商品定价"
            placeholder="请输入商品定价"
          />
          <van-field
            v-model="form.description"
            name="description"
            label="商品介绍"
            type="textarea"
            placeholder="请输入商品介绍"
            rows="3"
          />
          <van-field
            v-model="form.feature_analysis"
            name="feature_analysis"
            label="特点分析"
            type="textarea"
            placeholder="请输入商品特点分析"
            rows="4"
          />

          <!-- 管理员可以选择商品归属用户 -->
          <van-field
            v-if="authStore.user && authStore.user.role === 'admin'"
            name="owner"
            label="归属用户"
            is-link
            readonly
            :value="selectedUserText"
            placeholder="请选择归属用户"
            @click="showUserPicker = true"
          />
        </van-cell-group>

        <!-- 文件上传区域 -->
        <van-cell-group inset>
          <FileUpload
            v-model="form.main_images"
            title="商品主图"
            :max-count="10"
            accept="image/*"
            upload-text="上传主图"
            type="image"
          />

          <FileUpload
            v-model="form.detail_images"
            title="商品详情图"
            :max-count="30"
            accept="image/*"
            upload-text="上传详情图"
            type="image"
          />
        </van-cell-group>

        <div class="submit-button">
          <van-button
            round
            block
            type="primary"
            native-type="submit"
            :loading="loading"
          >
            {{ isEdit ? '更新商品' : '创建商品' }}
          </van-button>
        </div>
      </van-form>
    </div>

    <!-- 用户选择器 -->
    <van-popup v-model:show="showUserPicker" position="bottom" :style="{ height: '50%' }">
      <van-picker
        :columns="[userOptions]"
        @confirm="onUserConfirm"
        @cancel="showUserPicker = false"
      />
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showToast } from 'vant'
import api from '@/utils/api'
import FileUpload from '@/components/FileUpload.vue'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

const loading = ref(false)
const isEdit = computed(() => !!route.params.id)

// 用户选择相关
const showUserPicker = ref(false)
const users = ref<any[]>([])
const selectedUserId = ref<number | null>(null)

const form = reactive({
  short_name: '',
  title: '',
  price: '',
  description: '',
  feature_analysis: '',
  main_images: [] as string[],
  detail_images: [] as string[],
  owner_id: null as number | null
})

// 计算属性
const userOptions = computed(() => {
  console.log('Computing userOptions, users:', users.value)
  const options = users.value.map(user => ({
    text: `${user.username} (${user.role === 'admin' ? '管理员' : '普通用户'})`,
    value: user.id
  }))
  console.log('User options:', options)
  return options
})

const selectedUserText = computed(() => {
  if (!selectedUserId.value) return ''
  const user = users.value.find(u => u.id === selectedUserId.value)
  return user ? `${user.username} (${user.role === 'admin' ? '管理员' : '普通用户'})` : ''
})

const loadProduct = async () => {
  if (!isEdit.value) return

  try {
    const response = await api.get(`/api/v1/products/${route.params.id}`)
    const product = response.data

    Object.assign(form, {
      short_name: product.short_name,
      title: product.title || '',
      price: product.price || '',
      description: product.description || '',
      feature_analysis: product.feature_analysis || '',
      main_images: product.main_images || [],
      detail_images: product.detail_images || []
    })

    // 设置选中的用户
    selectedUserId.value = product.owner_id
  } catch (error) {
    console.error('Failed to load product:', error)
    showToast('加载商品信息失败')
    router.back()
  }
}

const loadUsers = async () => {
  console.log('loadUsers called, isAdmin:', authStore.isAdmin, 'user:', authStore.user)

  if (!authStore.user || authStore.user.role !== 'admin') {
    console.log('User is not admin, skipping user list load')
    return
  }

  try {
    console.log('Loading users list...')
    const response = await api.get('/api/v1/products/users/list')
    users.value = response.data
    console.log('Users loaded successfully:', users.value)
  } catch (error) {
    console.error('Failed to load users:', error)
    showToast('加载用户列表失败')
  }
}

const onUserConfirm = (result: any) => {
  console.log('User confirmed - result:', result)

  // van-picker返回的结构：{selectedValues: Array, selectedOptions: Array, selectedIndexes: Array}
  if (result.selectedValues && result.selectedValues.length > 0) {
    selectedUserId.value = result.selectedValues[0]
    console.log('Selected user ID:', selectedUserId.value)
    console.log('Selected user text:', selectedUserText.value)
  } else if (result.selectedOptions && result.selectedOptions.length > 0) {
    selectedUserId.value = result.selectedOptions[0].value
    console.log('Selected user ID from options:', selectedUserId.value)
    console.log('Selected user text:', selectedUserText.value)
  }

  showUserPicker.value = false
}

const handleSubmit = async () => {
  loading.value = true

  try {
    const submitData = { ...form }

    // 如果是管理员且选择了用户，添加owner_id
    if (authStore.user && authStore.user.role === 'admin' && selectedUserId.value) {
      submitData.owner_id = selectedUserId.value
      console.log('Adding owner_id to submit data:', selectedUserId.value)
    } else {
      console.log('Not adding owner_id:', {
        isAdmin: authStore.user && authStore.user.role === 'admin',
        selectedUserId: selectedUserId.value,
        user: authStore.user
      })
    }

    if (isEdit.value) {
      await api.put(`/api/v1/products/${route.params.id}`, submitData)
      showToast('更新成功')
    } else {
      await api.post('/api/v1/products/', submitData)
      showToast('创建成功')
    }

    router.back()
  } catch (error) {
    console.error('Failed to save product:', error)
    showToast(isEdit.value ? '更新失败' : '创建失败')
  } finally {
    loading.value = false
  }
}

onMounted(async () => {
  // 确保用户信息已加载
  if (authStore.token && !authStore.user) {
    await authStore.getCurrentUser()
  }

  loadUsers()
  loadProduct()
})
</script>

<style scoped>
/* 表单特定样式 */
.form-content {
  padding: 0;
}

.submit-button {
  margin-top: 24px;
}
</style>
