<template>
  <div class="page-container">
    <!-- Header -->
    <div class="page-header">
      <h1 class="page-title">商品管理</h1>
      <van-button
        type="primary"
        icon="plus"
        @click="router.push('/products/create')"
        class="create-btn"
      >
        新建商品
      </van-button>
    </div>

    <!-- Search -->
    <div class="search-section">
      <van-search
        v-model="searchQuery"
        placeholder="搜索商品名称或标题"
        @search="handleSearch"
        @clear="handleSearch"
        shape="round"
        class="custom-search"
      />
    </div>

    <!-- Filters -->
    <div class="filter-section">
      <div class="filter-row">
        <div class="filter-item">
          <label class="filter-label">商品所有人:</label>
          <select v-model="selectedOwnerId" @change="onOwnerChange" class="filter-select">
            <option :value="null">全部用户</option>
            <option v-for="user in users" :key="user.id" :value="user.id">
              {{ user.username }}
            </option>
          </select>
        </div>
        <div class="filter-item filter-item-range">
          <label class="filter-label">待使用笔记数:</label>
          <div class="range-inputs">
            <input
              v-model="unusedNotesMin"
              type="number"
              placeholder="最小值"
              @input="onUnusedNotesChange"
              class="filter-input range-input"
              min="0"
            />
            <span class="range-separator">至</span>
            <input
              v-model="unusedNotesMax"
              type="number"
              placeholder="最大值"
              @input="onUnusedNotesChange"
              class="filter-input range-input"
              min="0"
            />
          </div>
        </div>
        <div class="filter-actions">
          <van-button type="default" size="small" @click="clearFilters">清除筛选</van-button>
        </div>
      </div>
    </div>
    
    <!-- Product List -->
    <div class="product-list">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="onLoad"
        >
          <div class="products-grid">
            <ProductManageCard
              v-for="product in products.filter(p => p && p.id)"
              :key="product.id"
              :product="product"
              @delete="handleDeleteProduct"
              @refresh="onRefresh"
            />
          </div>

          <van-empty
            v-if="!loading && products.length === 0"
            description="暂无商品数据"
          >
            <van-button type="primary" @click="router.push('/products/create')">
              创建第一个商品
            </van-button>
          </van-empty>
        </van-list>
      </van-pull-refresh>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { showConfirmDialog, showToast } from 'vant'
import api from '@/utils/api'
import ProductManageCard from '@/components/ProductManageCard.vue'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const searchQuery = ref('')
const refreshing = ref(false)
const loading = ref(false)
const finished = ref(false)
const products = ref<any[]>([])
const currentPage = ref(1)
const pageSize = ref(20)
const initialLoaded = ref(false)

// Filter related data
const users = ref<any[]>([])
const selectedOwnerId = ref<number | null>(null)
const unusedNotesMin = ref<number | null>(null)
const unusedNotesMax = ref<number | null>(null)

const loadProducts = async (reset = false) => {
  console.log('loadProducts called with reset:', reset)

  if (reset) {
    currentPage.value = 1
    finished.value = false
    products.value = []
  }

  try {
    const params: any = {
      skip: (currentPage.value - 1) * pageSize.value,
      limit: pageSize.value,
      q: searchQuery.value || undefined,
      sort_by: 'created_at',
      order: 'desc'
    }

    // Add filter parameters
    if (selectedOwnerId.value !== null) {
      params.owner_id = selectedOwnerId.value
    }

    if (unusedNotesMin.value !== null && unusedNotesMin.value !== undefined) {
      params.unused_notes_min = unusedNotesMin.value
    }

    if (unusedNotesMax.value !== null && unusedNotesMax.value !== undefined) {
      params.unused_notes_max = unusedNotesMax.value
    }

    const response = await api.get('/api/v1/products/', { params })

    const newProducts = response.data

    if (reset) {
      products.value = newProducts
    } else {
      products.value.push(...newProducts)
    }

    if (newProducts.length < pageSize.value) {
      finished.value = true
    } else {
      currentPage.value++
    }
  } catch (error) {
    console.error('Failed to load products:', error)
    showToast('加载商品失败')
  }
}

const onLoad = () => {
  // 防止初始加载时重复调用
  if (!initialLoaded.value) {
    return
  }

  loading.value = true
  loadProducts().finally(() => {
    loading.value = false
  })
}

const onRefresh = () => {
  refreshing.value = true
  initialLoaded.value = false
  loadProducts(true).finally(() => {
    refreshing.value = false
    initialLoaded.value = true
  })
}

const handleSearch = () => {
  initialLoaded.value = false
  loadProducts(true).finally(() => {
    initialLoaded.value = true
  })
}

// Filter methods
const loadUsers = async () => {
  if (!authStore.isAdmin) return

  try {
    const response = await api.get('/api/v1/products/users/list')
    users.value = response.data
  } catch (error) {
    console.error('Failed to load users:', error)
  }
}

const onOwnerChange = () => {
  // Reload products with new filter
  initialLoaded.value = false
  loadProducts(true).finally(() => {
    initialLoaded.value = true
  })
}

// 使用防抖来避免频繁请求
let debounceTimer: number | null = null

const onUnusedNotesChange = () => {
  if (debounceTimer) {
    clearTimeout(debounceTimer)
  }

  debounceTimer = setTimeout(() => {
    // Reload products with new filter
    initialLoaded.value = false
    loadProducts(true).finally(() => {
      initialLoaded.value = true
    })
  }, 500) // 500ms 防抖
}

const clearFilters = () => {
  selectedOwnerId.value = null
  unusedNotesMin.value = null
  unusedNotesMax.value = null

  // Reload products without filters
  initialLoaded.value = false
  loadProducts(true).finally(() => {
    initialLoaded.value = true
  })
}

const viewProductNotes = (productId: number) => {
  router.push(`/products/${productId}/notes`)
}

const editProduct = (productId: number) => {
  router.push(`/products/${productId}/edit`)
}

const handleDeleteProduct = async (productId: number) => {
  try {
    await api.delete(`/api/v1/products/${productId}`)
    showToast('删除成功')

    initialLoaded.value = false
    loadProducts(true).finally(() => {
      initialLoaded.value = true
    })
  } catch (error: any) {
    console.error('Failed to delete product:', error)
    showToast('删除失败')
  }
}

const deleteProduct = async (product: any) => {
  try {
    await showConfirmDialog({
      title: '确认删除',
      message: `确定要删除商品"${product.title || product.short_name}"吗？删除后相关笔记也会被删除。`,
    })

    await api.delete(`/api/v1/products/${product.id}`)
    showToast('删除成功')

    initialLoaded.value = false
    loadProducts(true).finally(() => {
      initialLoaded.value = true
    })
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('Failed to delete product:', error)
      showToast('删除失败')
    }
  }
}

onMounted(() => {
  console.log('ProductListView mounted')
  loadProducts(true).finally(() => {
    initialLoaded.value = true
    console.log('Initial load completed')
  })

  // Load users for filter if admin
  if (authStore.isAdmin) {
    console.log('Loading users for admin')
    loadUsers()
  }
})
</script>

<style scoped>
/* 页面特定样式 */

:deep(.custom-nav-bar .van-icon) {
  color: white;
}

.search-section {
  padding: 16px;
  background: transparent;
}

:deep(.custom-search) {
  background: white;
  border-radius: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.8);
}

:deep(.custom-search .van-search__content) {
  background: transparent;
  border-radius: 20px;
}

.product-list {
  padding: 0 16px;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .products-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .product-list {
    padding: 0 12px;
  }
}

@media (min-width: 1200px) {
  .products-grid {
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: 24px;
  }

  .product-list {
    padding: 0 24px;
  }
}

:deep(.van-card) {
  border-radius: 16px;
  margin-bottom: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.8);
  overflow: hidden;
}

:deep(.van-card__thumb) {
  border-radius: 12px;
  overflow: hidden;
}

:deep(.van-card__content) {
  padding: 16px;
}

:deep(.van-card__title) {
  font-weight: 600;
  color: #333;
  font-size: 16px;
}

:deep(.van-card__desc) {
  color: #666;
  margin-top: 4px;
}

.notes-count {
  color: #4ecdc4;
  font-size: 12px;
  font-weight: 500;
}

:deep(.van-card__footer) {
  display: flex;
  gap: 8px;
  padding: 12px 16px;
  background: #f8f9fa;
  border-top: 1px solid #eee;
}

:deep(.van-card__footer .van-button) {
  border-radius: 20px;
  font-weight: 500;
}

:deep(.van-empty) {
  padding: 60px 20px;
}

:deep(.van-empty__description) {
  color: #666;
  margin-bottom: 20px;
}

/* 加载和刷新样式 */
:deep(.van-pull-refresh) {
  min-height: calc(100vh - 140px);
}

:deep(.van-list__finished-text) {
  color: #999;
  font-size: 14px;
  padding: 20px;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .search-section {
    padding: 12px;
  }

  .product-list {
    padding: 0 12px;
  }

  :deep(.van-card) {
    margin-bottom: 12px;
  }

  :deep(.van-card__content) {
    padding: 12px;
  }

  :deep(.van-card__footer) {
    padding: 8px 12px;
  }

  .product-meta {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .notes-count {
    color: #1989fa;
    font-weight: 500;
  }

  .owner-info {
    color: #969799;
    font-size: 12px;
  }
}

/* Filter styles */
.filter-section {
  padding: 16px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
}

.filter-row {
  display: flex;
  gap: 16px;
  align-items: flex-end;
  flex-wrap: wrap;
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.filter-label {
  font-size: 12px;
  color: #646566;
  font-weight: 500;
}

.filter-select {
  padding: 8px 12px;
  border: 1px solid #dcdee0;
  border-radius: 4px;
  background: white;
  font-size: 14px;
  color: #323233;
  min-width: 120px;
  cursor: pointer;
}

.filter-select:focus {
  outline: none;
  border-color: #1989fa;
}

.filter-input {
  padding: 8px 12px;
  border: 1px solid #dcdee0;
  border-radius: 4px;
  background: white;
  font-size: 14px;
  color: #323233;
  width: 80px;
}

.filter-input:focus {
  outline: none;
  border-color: #1989fa;
}

/* 范围筛选特殊样式 */
.filter-item-range {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.range-inputs {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.range-input {
  width: 90px;
  flex-shrink: 0;
}

.range-separator {
  color: #646566;
  font-size: 14px;
  font-weight: 500;
  padding: 0 4px;
  white-space: nowrap;
}

.filter-actions {
  margin-top: 20px;
}
</style>
