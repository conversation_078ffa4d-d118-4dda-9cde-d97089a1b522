<template>
  <div class="products-download-container">
    <div class="page-header">
      <van-button
        icon="arrow-left"
        @click="router.back()"
        class="back-btn"
      >
        返回
      </van-button>
      <h1 class="page-title">商品下载</h1>
      <div class="header-spacer"></div>
    </div>

    <!-- 统计信息 -->
    <div class="stats-section">
      <van-cell-group inset>
        <van-cell title="统计信息" />
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-value">{{ stats.total_products }}</div>
            <div class="stat-label">总商品</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ stats.products_with_notes }}</div>
            <div class="stat-label">有笔记</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ stats.products_without_notes }}</div>
            <div class="stat-label">无笔记</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ stats.total_images }}</div>
            <div class="stat-label">总图片</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ stats.coverage_rate }}%</div>
            <div class="stat-label">笔记覆盖率</div>
          </div>
        </div>
      </van-cell-group>
    </div>

    <!-- 搜索 -->
    <div class="search-section">
      <van-search
        v-model="searchQuery"
        placeholder="搜索商品标题、描述或特点分析"
        @search="handleSearch"
        @clear="handleSearch"
      />
    </div>

    <!-- 商品列表 -->
    <div class="products-list">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="onLoad"
        >
          <div class="products-grid">
            <ProductPreviewCard
              v-for="product in products"
              :key="product.id"
              :product="product"
            />
          </div>

          <van-empty
            v-if="!loading && products.length === 0"
            description="暂无商品"
            image="search"
          />
        </van-list>
      </van-pull-refresh>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import api from '@/utils/api'
import ProductPreviewCard from '@/components/ProductPreviewCard.vue'

const router = useRouter()

// 响应式数据
const searchQuery = ref('')
const refreshing = ref(false)
const loading = ref(false)
const finished = ref(false)
const products = ref<any[]>([])
const stats = ref({
  total_products: 0,
  products_with_notes: 0,
  products_without_notes: 0,
  total_images: 0,
  coverage_rate: 0
})

// 分页参数
const currentPage = ref(1)
const pageSize = ref(20)
const isLoading = ref(false) // 添加加载状态，防止重复请求

// 去重函数
const deduplicateProducts = (productList: any[]) => {
  const seen = new Set()
  return productList.filter(product => {
    if (!product || !product.id) return false
    if (seen.has(product.id)) return false
    seen.add(product.id)
    return true
  })
}

// 加载商品列表
const loadProducts = async (reset = false) => {
  // 防止重复请求
  if (isLoading.value) {
    console.log('Already loading, skipping request')
    return
  }

  if (reset) {
    currentPage.value = 1
    finished.value = false
    products.value = []
  }

  isLoading.value = true

  try {
    const params: any = {
      skip: (currentPage.value - 1) * pageSize.value,
      limit: pageSize.value
    }

    if (searchQuery.value) {
      params.q = searchQuery.value
    }

    const response = await api.get('/api/v1/products-download/preview', { params })
    const newProducts = response.data

    if (reset) {
      // 重置时直接设置新数据（已去重）
      products.value = deduplicateProducts(newProducts)
    } else {
      // 追加时合并数据并去重
      const combinedProducts = [...products.value, ...newProducts]
      products.value = deduplicateProducts(combinedProducts)
    }

    if (newProducts.length < pageSize.value) {
      finished.value = true
    } else {
      currentPage.value++
    }
  } catch (error) {
    console.error('Failed to load products:', error)
    showToast('加载商品失败')
  } finally {
    isLoading.value = false
  }
}

// 加载统计信息
const loadStats = async () => {
  try {
    const response = await api.get('/api/v1/products-download/stats')
    stats.value = response.data
  } catch (error) {
    console.error('Failed to load stats:', error)
  }
}

// 事件处理
const onLoad = () => {
  // 如果已经在加载中，直接返回
  if (isLoading.value) {
    loading.value = false
    return
  }

  loading.value = true
  loadProducts().finally(() => {
    loading.value = false
  })
}

const onRefresh = () => {
  refreshing.value = true
  Promise.all([
    loadProducts(true),
    loadStats()
  ]).finally(() => {
    refreshing.value = false
  })
}

const handleSearch = () => {
  loadProducts(true)
}

onMounted(() => {
  loadStats()
  loadProducts(true)
})
</script>

<style scoped>
.products-download-container {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.stats-section {
  padding: 16px 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 12px;
  padding: 16px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 20px;
  font-weight: bold;
  color: #1989fa;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 11px;
  color: #969799;
}

.search-section {
  background: white;
  margin-bottom: 8px;
}

.products-list {
  padding: 0 16px;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .products-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .products-list {
    padding: 0 12px;
  }
}

@media (min-width: 1200px) {
  .products-grid {
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
  }

  .products-list {
    padding: 0 24px;
  }
}
</style>
