<template>
  <div class="profile-container">
    <div class="page-header">
      <h1 class="page-title">个人资料</h1>
    </div>
    
    <div class="profile-content">
      <van-cell-group inset>
        <van-cell
          title="用户名"
          :value="authStore.user?.username"
          icon="user-o"
        />
        <van-cell
          title="角色"
          :value="authStore.user?.role === 'admin' ? '管理员' : '普通用户'"
          icon="manager-o"
        />
        <van-cell
          title="登录状态"
          value="已登录"
          icon="success"
        />
      </van-cell-group>
      
      <van-cell-group inset>
        <van-cell
          title="修改密码"
          is-link
          icon="lock"
          @click="showToast('功能开发中')"
        />
        <van-cell
          title="系统设置"
          is-link
          icon="setting-o"
          @click="showToast('功能开发中')"
        />
      </van-cell-group>
      
      <div class="logout-button">
        <van-button
          block
          type="danger"
          @click="handleLogout"
        >
          退出登录
        </van-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { showToast, showConfirmDialog } from 'vant'

const router = useRouter()
const authStore = useAuthStore()

const handleLogout = async () => {
  try {
    await showConfirmDialog({
      title: '确认退出',
      message: '确定要退出登录吗？',
    })
    authStore.logout()
    router.push('/login')
  } catch {
    // User cancelled
  }
}
</script>

<style scoped>
.profile-container {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.profile-content {
  padding: 16px;
}

.logout-button {
  margin-top: 24px;
}
</style>
