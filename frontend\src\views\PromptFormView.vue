<template>
  <div class="prompt-form-container">
    <van-nav-bar
      :title="isEdit ? '编辑提示词' : '新建提示词'"
      left-text="返回"
      left-arrow
      @click-left="router.back()"
    />
    
    <div class="form-content">
      <van-form @submit="handleSubmit">
        <van-cell-group inset>
          <van-field
            v-model="form.title"
            name="title"
            label="提示词标题"
            placeholder="请输入提示词标题"
            :rules="[{ required: true, message: '请输入提示词标题' }]"
          />
          <van-field
            v-model="form.content"
            name="content"
            label="提示词内容"
            type="textarea"
            placeholder="请输入提示词内容，可以使用变量占位符如 {商品标题}、{商品特点分析} 等"
            rows="8"
            :rules="[{ required: true, message: '请输入提示词内容' }]"
          />
        </van-cell-group>
        
        <!-- 变量占位符提示 -->
        <van-cell-group inset>
          <van-cell title="可用变量占位符" icon="info-o">
            <template #default>
              <div class="variable-tips">
                <van-tag type="primary" size="medium">{商品标题}</van-tag>
                <van-tag type="primary" size="medium">{商品简称}</van-tag>
                <van-tag type="primary" size="medium">{商品定价}</van-tag>
                <van-tag type="primary" size="medium">{商品介绍}</van-tag>
                <van-tag type="primary" size="medium">{商品特点分析}</van-tag>
                <van-tag type="primary" size="medium">{笔记标题}</van-tag>
                <van-tag type="primary" size="medium">{笔记正文}</van-tag>
                <van-tag type="primary" size="medium">{笔记标签}</van-tag>
              </div>
            </template>
          </van-cell>
        </van-cell-group>
        
        <!-- 预览区域 -->
        <van-cell-group inset v-if="form.content">
          <van-cell title="内容预览" icon="eye-o">
            <template #default>
              <div class="content-preview">
                {{ form.content }}
              </div>
            </template>
          </van-cell>
        </van-cell-group>
        
        <div class="submit-button">
          <van-button
            round
            block
            type="primary"
            native-type="submit"
            :loading="loading"
          >
            {{ isEdit ? '更新提示词' : '创建提示词' }}
          </van-button>
        </div>
      </van-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showToast } from 'vant'
import api from '@/utils/api'

const router = useRouter()
const route = useRoute()

const loading = ref(false)
const isEdit = computed(() => !!route.params.id)

const form = reactive({
  title: '',
  content: ''
})

const loadPrompt = async () => {
  if (!isEdit.value) return
  
  try {
    const response = await api.get(`/api/v1/prompts/${route.params.id}`)
    const prompt = response.data
    
    Object.assign(form, {
      title: prompt.title,
      content: prompt.content
    })
  } catch (error) {
    console.error('Failed to load prompt:', error)
    showToast('加载提示词失败')
    router.back()
  }
}

const handleSubmit = async () => {
  loading.value = true

  try {
    const data = {
      ...form
    }
    
    if (isEdit.value) {
      await api.put(`/api/v1/prompts/${route.params.id}`, data)
      showToast('更新成功')
    } else {
      await api.post('/api/v1/prompts/', data)
      showToast('创建成功')
    }
    
    router.back()
  } catch (error) {
    console.error('Failed to save prompt:', error)
    showToast(isEdit.value ? '更新失败' : '创建失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadPrompt()
})
</script>

<style scoped>
.prompt-form-container {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.form-content {
  padding: 16px;
}

.variable-tips {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.content-preview {
  background: #f7f8fa;
  padding: 12px;
  border-radius: 8px;
  margin-top: 8px;
  line-height: 1.5;
  color: #646566;
  white-space: pre-wrap;
  max-height: 200px;
  overflow-y: auto;
}

.submit-button {
  margin-top: 24px;
}
</style>
