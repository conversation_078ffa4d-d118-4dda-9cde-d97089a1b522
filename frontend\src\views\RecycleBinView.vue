<template>
  <div class="recycle-bin-container">
    <van-nav-bar
      title="回收站"
      left-text="返回"
      left-arrow
      @click-left="router.back()"
    />
    
    <!-- 搜索栏 -->
    <div class="search-section">
      <van-search
        v-model="searchQuery"
        placeholder="搜索已删除的笔记"
        @search="loadDeletedNotes"
        @clear="loadDeletedNotes"
      />
    </div>
    
    <!-- 笔记列表 -->
    <div class="notes-list">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="onLoad"
        >
          <van-cell-group inset v-for="note in notes" :key="note.id">
            <van-cell
              :title="note.title"
              :label="`删除时间: ${formatDate(note.deleted_at)}`"
              :value="`来自: ${note.product?.short_name || '未知商品'}`"
            >
              <template #extra>
                <div class="note-actions">
                  <van-button 
                    size="small" 
                    type="success"
                    @click="restoreNote(note)"
                    :loading="note.restoring"
                  >
                    恢复
                  </van-button>
                  <van-button 
                    size="small" 
                    type="danger"
                    @click="confirmDelete(note)"
                    :loading="note.deleting"
                  >
                    彻底删除
                  </van-button>
                </div>
              </template>
            </van-cell>
            
            <!-- 笔记详情 -->
            <van-cell v-if="note.body">
              <div class="note-content">
                <p class="note-body">{{ note.body.substring(0, 200) }}{{ note.body.length > 200 ? '...' : '' }}</p>
                <div class="note-meta">
                  <span v-if="note.tags" class="tags">标签: {{ note.tags }}</span>
                  <span class="create-time">创建时间: {{ formatDate(note.created_at) }}</span>
                </div>
              </div>
            </van-cell>
          </van-cell-group>
          
          <van-empty
            v-if="notes.length === 0 && !loading"
            description="回收站为空"
          />
        </van-list>
      </van-pull-refresh>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showConfirmDialog } from 'vant'
import api from '@/utils/api'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

// 检查管理员权限
if (authStore.user?.role !== 'admin') {
  showToast('无权限访问')
  router.back()
}

const searchQuery = ref('')
const notes = ref<any[]>([])
const loading = ref(false)
const refreshing = ref(false)
const finished = ref(false)
const page = ref(1)
const pageSize = 20

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const loadDeletedNotes = async (isRefresh = false) => {
  if (isRefresh) {
    page.value = 1
    finished.value = false
    notes.value = []
  }
  
  if (loading.value || finished.value) return
  
  loading.value = true
  
  try {
    const params = {
      page: page.value,
      size: pageSize,
      q: searchQuery.value || undefined
    }
    
    const response = await api.get('/api/v1/recycle-bin/notes/', { params })
    const newNotes = response.data
    
    if (isRefresh) {
      notes.value = newNotes
    } else {
      notes.value.push(...newNotes)
    }
    
    if (newNotes.length < pageSize) {
      finished.value = true
    } else {
      page.value++
    }
  } catch (error) {
    console.error('Failed to load deleted notes:', error)
    showToast('加载失败')
  } finally {
    loading.value = false
    refreshing.value = false
  }
}

const onRefresh = () => {
  refreshing.value = true
  loadDeletedNotes(true)
}

const onLoad = () => {
  loadDeletedNotes()
}

const restoreNote = async (note: any) => {
  note.restoring = true
  
  try {
    await api.post(`/api/v1/recycle-bin/notes/${note.id}/restore`)
    showToast('恢复成功')
    
    // 从列表中移除
    const index = notes.value.findIndex(n => n.id === note.id)
    if (index > -1) {
      notes.value.splice(index, 1)
    }
  } catch (error) {
    console.error('Failed to restore note:', error)
    showToast('恢复失败')
  } finally {
    note.restoring = false
  }
}

const confirmDelete = async (note: any) => {
  try {
    await showConfirmDialog({
      title: '确认彻底删除',
      message: `确定要彻底删除笔记"${note.title}"吗？此操作不可恢复！`,
      confirmButtonText: '彻底删除',
      confirmButtonColor: '#ee0a24'
    })
    
    await deleteNotePermanently(note)
  } catch (error) {
    // 用户取消删除
  }
}

const deleteNotePermanently = async (note: any) => {
  note.deleting = true
  
  try {
    await api.delete(`/api/v1/recycle-bin/notes/${note.id}`)
    showToast('已彻底删除')
    
    // 从列表中移除
    const index = notes.value.findIndex(n => n.id === note.id)
    if (index > -1) {
      notes.value.splice(index, 1)
    }
  } catch (error) {
    console.error('Failed to delete note permanently:', error)
    showToast('删除失败')
  } finally {
    note.deleting = false
  }
}

onMounted(() => {
  loadDeletedNotes(true)
})
</script>

<style scoped>
.recycle-bin-container {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.search-section {
  padding: 16px;
  background: white;
}

.notes-list {
  padding: 0 16px;
}

.note-actions {
  display: flex;
  gap: 8px;
}

.note-content {
  padding: 12px 0;
}

.note-body {
  margin: 0 0 8px 0;
  color: #646566;
  line-height: 1.5;
}

.note-meta {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 12px;
  color: #969799;
}

.tags {
  color: #1989fa;
}

.create-time {
  color: #969799;
}
</style>
