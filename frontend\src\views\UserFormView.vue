<template>
  <div class="page-container">
    <div class="page-header">
      <van-button 
        icon="arrow-left" 
        @click="router.back()"
        class="back-btn"
      >
        返回
      </van-button>
      <h1 class="page-title">{{ isEdit ? '编辑用户' : '新建用户' }}</h1>
      <div class="header-spacer"></div>
    </div>

    <div class="form-content">
      <van-form @submit="handleSubmit">
        <van-cell-group inset>
          <van-field
            v-model="form.username"
            name="username"
            label="用户名"
            placeholder="请输入用户名"
            :rules="[{ required: true, message: '请输入用户名' }]"
            :disabled="isEdit"
          />
          
          <van-field
            v-model="form.password"
            name="password"
            type="password"
            label="密码"
            :placeholder="isEdit ? '留空则不修改密码' : '请输入密码'"
            :rules="isEdit ? [] : [{ required: true, message: '请输入密码' }]"
          />
          
          <van-field
            v-model="form.confirmPassword"
            name="confirmPassword"
            type="password"
            label="确认密码"
            :placeholder="isEdit ? '留空则不修改密码' : '请再次输入密码'"
            :rules="passwordRules"
          />
          
          <van-field name="role" label="用户角色">
            <template #input>
              <van-radio-group v-model="form.role" direction="horizontal">
                <van-radio name="user">普通用户</van-radio>
                <van-radio name="admin">管理员</van-radio>
              </van-radio-group>
            </template>
          </van-field>
        </van-cell-group>
        
        <!-- 编辑模式下的额外信息 -->
        <van-cell-group inset v-if="isEdit && userInfo">
          <van-cell title="用户ID" :value="userInfo.id" />
          <van-cell title="当前角色" :value="getRoleText(userInfo.role)" />
        </van-cell-group>
        
        <div class="submit-button">
          <van-button
            round
            block
            type="primary"
            native-type="submit"
            :loading="loading"
          >
            {{ isEdit ? '更新用户' : '创建用户' }}
          </van-button>
        </div>
      </van-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showToast } from 'vant'
import api from '@/utils/api'

const router = useRouter()
const route = useRoute()

const loading = ref(false)
const isEdit = computed(() => !!route.params.id)
const userInfo = ref<any>(null)

const form = reactive({
  username: '',
  password: '',
  confirmPassword: '',
  role: 'user'
})

const passwordRules = computed(() => {
  const rules = []
  
  if (!isEdit.value) {
    // 新建用户时密码必填
    rules.push({ required: true, message: '请再次输入密码' })
  }
  
  // 如果输入了密码，则需要验证确认密码
  if (form.password) {
    rules.push({
      validator: (value: string) => {
        if (value !== form.password) {
          return '两次输入的密码不一致'
        }
        return true
      }
    })
  }
  
  return rules
})

const getRoleText = (role: string) => {
  return role === 'admin' ? '管理员' : '普通用户'
}

const loadUser = async () => {
  if (!isEdit.value) return
  
  try {
    const response = await api.get(`/api/v1/admin/users/${route.params.id}`)
    const user = response.data
    userInfo.value = user
    
    Object.assign(form, {
      username: user.username,
      password: '',
      confirmPassword: '',
      role: user.role
    })
  } catch (error) {
    console.error('Failed to load user:', error)
    showToast('加载用户信息失败')
    router.back()
  }
}

const handleSubmit = async () => {
  // 验证密码
  if (form.password && form.password !== form.confirmPassword) {
    showToast('两次输入的密码不一致')
    return
  }
  
  loading.value = true
  
  try {
    const data: any = {
      username: form.username,
      role: form.role
    }
    
    // 只有在输入了密码时才包含密码字段
    if (form.password) {
      data.password = form.password
    }
    
    if (isEdit.value) {
      await api.put(`/api/v1/admin/users/${route.params.id}`, data)
      showToast('更新成功')
    } else {
      if (!form.password) {
        showToast('请输入密码')
        return
      }
      await api.post('/api/v1/admin/users/', data)
      showToast('创建成功')
    }
    
    router.back()
  } catch (error: any) {
    console.error('Failed to save user:', error)
    
    // 处理特定错误
    if (error.response?.status === 400) {
      const detail = error.response.data?.detail
      if (detail === 'Username already registered') {
        showToast('用户名已存在')
      } else if (detail === 'Cannot modify your own role') {
        showToast('不能修改自己的角色')
      } else {
        showToast(detail || '操作失败')
      }
    } else {
      showToast(isEdit.value ? '更新失败' : '创建失败')
    }
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadUser()
})
</script>

<style scoped>
.form-content {
  padding: 0;
}

.submit-button {
  margin-top: 24px;
}

.van-radio-group {
  display: flex;
  gap: 24px;
}

.van-radio {
  flex: none;
}
</style>
