<template>
  <div class="page-container">
    <div class="page-header">
      <h1 class="page-title">用户管理</h1>
      <van-button 
        type="primary" 
        icon="plus" 
        @click="router.push('/admin/users/create')"
        class="create-btn"
      >
        新建用户
      </van-button>
    </div>
    
    <!-- 搜索和筛选 -->
    <div class="search-section">
      <van-search
        v-model="searchQuery"
        placeholder="搜索用户名"
        @search="loadUsers"
        @clear="loadUsers"
        shape="round"
      />
      
      <div class="filter-section">
        <van-dropdown-menu>
          <van-dropdown-item 
            v-model="roleFilter" 
            :options="roleOptions"
            @change="loadUsers"
          />
          <van-dropdown-item 
            v-model="sortBy" 
            :options="sortOptions"
            @change="loadUsers"
          />
        </van-dropdown-menu>
      </div>
    </div>
    
    <!-- 用户列表 -->
    <div class="users-list">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="onLoad"
        >
          <van-cell-group inset v-for="user in users" :key="user.id">
            <van-cell
              :title="user.username"
              :label="`ID: ${user.id}`"
              :value="getRoleText(user.role)"
              is-link
              @click="viewUserDetails(user)"
            >
              <template #extra>
                <div class="user-actions">
                  <van-button 
                    size="small" 
                    type="primary"
                    @click.stop="editUser(user)"
                    icon="edit"
                  >
                    编辑
                  </van-button>
                  <van-button 
                    v-if="user.id !== authStore.user?.id"
                    size="small" 
                    type="danger"
                    @click.stop="confirmDelete(user)"
                    icon="delete"
                  >
                    删除
                  </van-button>
                </div>
              </template>
            </van-cell>
          </van-cell-group>
          
          <van-empty
            v-if="users.length === 0 && !loading"
            description="暂无用户"
          />
        </van-list>
      </van-pull-refresh>
    </div>
    
    <!-- 用户详情弹窗 -->
    <van-popup 
      v-model:show="showUserDetails" 
      position="bottom" 
      :style="{ height: '60%' }"
      round
    >
      <div class="user-details" v-if="selectedUser">
        <div class="details-header">
          <h3>用户详情</h3>
          <van-icon name="cross" @click="showUserDetails = false" />
        </div>
        
        <div class="details-content">
          <van-cell-group>
            <van-cell title="用户ID" :value="selectedUser.id" />
            <van-cell title="用户名" :value="selectedUser.username" />
            <van-cell title="角色" :value="getRoleText(selectedUser.role)" />
          </van-cell-group>
          
          <div class="stats-section" v-if="userStats">
            <h4>统计信息</h4>
            <van-cell-group>
              <van-cell title="商品数量" :value="userStats.products_count" />
              <van-cell title="笔记数量" :value="userStats.notes_count" />
            </van-cell-group>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showConfirmDialog } from 'vant'
import api from '@/utils/api'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const searchQuery = ref('')
const roleFilter = ref('')
const sortBy = ref('id')
const users = ref<any[]>([])
const loading = ref(false)
const refreshing = ref(false)
const finished = ref(false)
const page = ref(1)
const pageSize = 20

const showUserDetails = ref(false)
const selectedUser = ref<any>(null)
const userStats = ref<any>(null)

const roleOptions = [
  { text: '全部角色', value: '' },
  { text: '管理员', value: 'admin' },
  { text: '普通用户', value: 'user' }
]

const sortOptions = [
  { text: '按ID排序', value: 'id' },
  { text: '按用户名排序', value: 'username' },
  { text: '按角色排序', value: 'role' }
]

const getRoleText = (role: string) => {
  return role === 'admin' ? '管理员' : '普通用户'
}

const loadUsers = async (isRefresh = false) => {
  if (isRefresh) {
    page.value = 1
    finished.value = false
    users.value = []
  }
  
  if (loading.value || finished.value) return
  
  loading.value = true
  
  try {
    const params = {
      skip: (page.value - 1) * pageSize,
      limit: pageSize,
      q: searchQuery.value || undefined,
      role: roleFilter.value || undefined,
      sort_by: sortBy.value,
      order: 'desc'
    }
    
    const response = await api.get('/api/v1/admin/users/', { params })
    const newUsers = response.data
    
    if (isRefresh) {
      users.value = newUsers
    } else {
      users.value.push(...newUsers)
    }
    
    if (newUsers.length < pageSize) {
      finished.value = true
    } else {
      page.value++
    }
  } catch (error) {
    console.error('Failed to load users:', error)
    showToast('加载用户失败')
  } finally {
    loading.value = false
    refreshing.value = false
  }
}

const onRefresh = () => {
  refreshing.value = true
  loadUsers(true)
}

const onLoad = () => {
  loadUsers()
}

const viewUserDetails = async (user: any) => {
  selectedUser.value = user
  showUserDetails.value = true
  
  try {
    const response = await api.get(`/api/v1/admin/users/${user.id}/stats`)
    userStats.value = response.data
  } catch (error) {
    console.error('Failed to load user stats:', error)
  }
}

const editUser = (user: any) => {
  router.push(`/admin/users/${user.id}/edit`)
}

const confirmDelete = async (user: any) => {
  try {
    await showConfirmDialog({
      title: '确认删除',
      message: `确定要删除用户"${user.username}"吗？此操作不可恢复！`,
      confirmButtonText: '删除',
      confirmButtonColor: '#ee0a24'
    })
    
    await deleteUser(user)
  } catch (error) {
    // 用户取消删除
  }
}

const deleteUser = async (user: any) => {
  try {
    await api.delete(`/api/v1/admin/users/${user.id}`)
    showToast('删除成功')
    
    // 从列表中移除
    const index = users.value.findIndex(u => u.id === user.id)
    if (index > -1) {
      users.value.splice(index, 1)
    }
  } catch (error) {
    console.error('Failed to delete user:', error)
    showToast('删除失败')
  }
}

onMounted(() => {
  loadUsers(true)
})
</script>

<style scoped>
.search-section {
  margin-bottom: 20px;
}

.filter-section {
  margin-top: 12px;
}

.users-list {
  padding: 0;
}

.user-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.user-details {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebedf0;
}

.details-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.details-content {
  flex: 1;
  overflow-y: auto;
}

.stats-section {
  margin-top: 20px;
}

.stats-section h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 500;
  color: #323233;
}
</style>
