import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

// Get local IP from environment or use default
const LOCAL_IP = process.env.LOCAL_IP || '127.0.0.1'

console.log('🔧 Vite Debug Configuration')
console.log(`   Local IP: ${LOCAL_IP}`)
console.log(`   Frontend Host: 0.0.0.0:3000`)
console.log(`   Backend Target: http://${LOCAL_IP}:8000`)

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
  },
  server: {
    port: 3000,
    host: '0.0.0.0',
    cors: true,
    proxy: {
      '/api': {
        target: `http://${LOCAL_IP}:8000`,
        changeOrigin: true,
        secure: false,
        configure: (proxy, options) => {
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log(`🔄 Proxy Request: ${req.method} ${req.url} -> ${options.target}${req.url}`)
            console.log(`   Origin: ${req.headers.origin || 'no-origin'}`)
            console.log(`   Host: ${req.headers.host}`)
          })
          proxy.on('proxyRes', (proxyRes, req, res) => {
            console.log(`✅ Proxy Response: ${proxyRes.statusCode} for ${req.url}`)
            if (proxyRes.statusCode && proxyRes.statusCode >= 400) {
              console.log(`❌ Proxy Error Response: ${proxyRes.statusCode} ${proxyRes.statusMessage}`)
            }
          })
          proxy.on('error', (err, req, res) => {
            console.log(`❌ Proxy Error: ${err.message} for ${req.url}`)
          })
        }
      },
      '/uploads': {
        target: `http://${LOCAL_IP}:8000`,
        changeOrigin: true,
        secure: false,
        configure: (proxy, options) => {
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log(`📁 Upload Proxy: ${req.method} ${req.url} -> ${options.target}${req.url}`)
          })
          proxy.on('error', (err, req, res) => {
            console.log(`❌ Upload Proxy Error: ${err.message} for ${req.url}`)
          })
        }
      }
    }
  },
  define: {
    __VUE_OPTIONS_API__: true,
    __VUE_PROD_DEVTOOLS__: false,
  }
})
