@echo off
chcp 65001 >nul
title Network Debug Tool - XHS Notes Manager

echo.
echo ========================================
echo     Network Debug Tool
echo ========================================
echo.

:: Get local IP address
echo [1] IP Address Detection
echo ========================
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /c:"IPv4"') do (
    for /f "tokens=1" %%b in ("%%a") do (
        set LOCAL_IP=%%b
        echo Found IP: %%b
        goto :found_ip
    )
)

:found_ip
if "%LOCAL_IP%"=="" (
    echo [ERROR] Could not detect local IP address
    echo Full ipconfig output:
    ipconfig
    pause
    exit /b 1
)

echo Primary IP: %LOCAL_IP%
echo.

:: Show all network interfaces
echo [2] All Network Interfaces
echo ===========================
ipconfig | findstr /C:"IPv4"
echo.

:: Port availability check
echo [3] Port Availability Check
echo ============================
echo Checking port 3000...
netstat -an | findstr ":3000"
if %errorlevel% neq 0 echo Port 3000 is available

echo.
echo Checking port 8000...
netstat -an | findstr ":8000"
if %errorlevel% neq 0 echo Port 8000 is available
echo.

:: Firewall check
echo [4] Windows Firewall Status
echo ============================
netsh advfirewall show allprofiles state
echo.

:: Test connectivity
echo [5] Connectivity Tests
echo ======================
echo Testing localhost...
ping -n 2 127.0.0.1

echo.
echo Testing local IP...
ping -n 2 %LOCAL_IP%
echo.

:: Check current configuration
echo [6] Current Configuration
echo ==========================
if exist "backend\.env" (
    echo Backend CORS configuration:
    type "backend\.env" | findstr "ALLOWED_ORIGINS"
) else (
    echo Backend .env file not found
)

echo.
if exist "frontend\vite.config.ts" (
    echo Frontend proxy configuration:
    type "frontend\vite.config.ts" | findstr -A 2 -B 2 "target:"
) else (
    echo Frontend vite.config.ts file not found
)
echo.

:: Service status check
echo [7] Service Status Check
echo ========================
echo Checking if backend is running...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://127.0.0.1:8000/health' -TimeoutSec 3 -UseBasicParsing; Write-Host 'Backend (localhost): RUNNING - Status:' $response.StatusCode } catch { Write-Host 'Backend (localhost): NOT RUNNING -' $_.Exception.Message }"

powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://%LOCAL_IP%:8000/health' -TimeoutSec 3 -UseBasicParsing; Write-Host 'Backend (LAN): RUNNING - Status:' $response.StatusCode } catch { Write-Host 'Backend (LAN): NOT RUNNING -' $_.Exception.Message }"

echo.
echo Checking if frontend is running...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://127.0.0.1:3000' -TimeoutSec 3 -UseBasicParsing; Write-Host 'Frontend (localhost): RUNNING - Status:' $response.StatusCode } catch { Write-Host 'Frontend (localhost): NOT RUNNING -' $_.Exception.Message }"

powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://%LOCAL_IP%:3000' -TimeoutSec 3 -UseBasicParsing; Write-Host 'Frontend (LAN): RUNNING - Status:' $response.StatusCode } catch { Write-Host 'Frontend (LAN): NOT RUNNING -' $_.Exception.Message }"

echo.

:: Test API endpoints
echo [8] API Endpoint Tests
echo ======================
echo Testing login endpoint (localhost)...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://127.0.0.1:8000/api/v1/auth/token' -Method POST -ContentType 'application/x-www-form-urlencoded' -Body 'username=admin&password=admin123' -TimeoutSec 5 -UseBasicParsing; Write-Host 'Login (localhost): Status' $response.StatusCode } catch { Write-Host 'Login (localhost): FAILED -' $_.Exception.Message }"

echo.
echo Testing login endpoint (LAN)...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://%LOCAL_IP%:8000/api/v1/auth/token' -Method POST -ContentType 'application/x-www-form-urlencoded' -Body 'username=admin&password=admin123' -TimeoutSec 5 -UseBasicParsing; Write-Host 'Login (LAN): Status' $response.StatusCode } catch { Write-Host 'Login (LAN): FAILED -' $_.Exception.Message }"

echo.

:: Browser test URLs
echo [9] Test URLs for Manual Testing
echo ==================================
echo Copy these URLs to test in browser:
echo.
echo Frontend (localhost): http://localhost:3000
echo Frontend (LAN):       http://%LOCAL_IP%:3000
echo Backend API Docs:     http://%LOCAL_IP%:8000/docs
echo Backend Health:       http://%LOCAL_IP%:8000/health
echo.

:: Firewall rule suggestions
echo [10] Firewall Configuration Suggestions
echo =========================================
echo If LAN access fails, try these firewall commands (run as Administrator):
echo.
echo Allow inbound port 3000:
echo netsh advfirewall firewall add rule name="XHS Frontend" dir=in action=allow protocol=TCP localport=3000
echo.
echo Allow inbound port 8000:
echo netsh advfirewall firewall add rule name="XHS Backend" dir=in action=allow protocol=TCP localport=8000
echo.

:: Network troubleshooting steps
echo [11] Troubleshooting Steps
echo ==========================
echo 1. Ensure both devices are on the same network
echo 2. Check Windows Firewall settings
echo 3. Verify antivirus software isn't blocking connections
echo 4. Try disabling Windows Firewall temporarily for testing
echo 5. Check router/network configuration
echo 6. Ensure no VPN is interfering
echo.

echo [12] Common Issues and Solutions
echo ==================================
echo Issue: "Network Error" in browser
echo Solution: Check CORS configuration and firewall
echo.
echo Issue: "Connection refused"
echo Solution: Ensure services are bound to 0.0.0.0, not 127.0.0.1
echo.
echo Issue: "Timeout"
echo Solution: Check firewall rules and network connectivity
echo.

echo ========================================
echo Debug information collection complete!
echo ========================================
echo.
echo Please share this output when reporting issues.
echo.
pause
