@echo off
chcp 65001 >nul
title XHS Notes Manager - Restore Configuration

echo.
echo ========================================
echo  XHS Notes Manager - Restore Config
echo ========================================
echo.

:: Restore backend configuration
if exist "backend\.env.backup" (
    echo [RESTORE] Restoring backend/.env from backup...
    copy "backend\.env.backup" "backend\.env" >nul
    echo [SUCCESS] Backend configuration restored
) else (
    echo [WARNING] No backup found for backend/.env
)

:: Restore frontend configuration
if exist "frontend\vite.config.ts.backup" (
    echo [RESTORE] Restoring frontend/vite.config.ts from backup...
    copy "frontend\vite.config.ts.backup" "frontend\vite.config.ts" >nul
    echo [SUCCESS] Frontend configuration restored
) else (
    echo [WARNING] No backup found for frontend/vite.config.ts
)

echo.
echo [INFO] Configuration restored to localhost-only access
echo [INFO] Restart services to apply changes
echo.
pause
