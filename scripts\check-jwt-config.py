#!/usr/bin/env python3
"""
JWT Token配置检查脚本
用于检查当前JWT Token过期时间配置是否正确
"""

import os
import sys
from pathlib import Path

def check_code_config():
    """检查代码中的默认配置"""
    config_file = Path("backend/app/core/config.py")
    if not config_file.exists():
        print("❌ 未找到配置文件: backend/app/core/config.py")
        return None
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找access_token_expire_minutes配置
        for line in content.split('\n'):
            if 'access_token_expire_minutes:' in line and '=' in line:
                # 提取数值
                parts = line.split('=')
                if len(parts) >= 2:
                    value_part = parts[1].strip()
                    # 提取数字
                    import re
                    match = re.search(r'(\d+)', value_part)
                    if match:
                        minutes = int(match.group(1))
                        days = minutes / (24 * 60)
                        print(f"📋 代码默认配置: {minutes} 分钟 ({days:.1f} 天)")
                        return minutes
        
        print("⚠️ 未在代码中找到access_token_expire_minutes配置")
        return None
        
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return None

def check_env_file(env_path):
    """检查环境配置文件"""
    env_file = Path(env_path)
    if not env_file.exists():
        return None
    
    try:
        with open(env_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        for line in content.split('\n'):
            line = line.strip()
            if line.startswith('ACCESS_TOKEN_EXPIRE_MINUTES='):
                value = line.split('=')[1].strip()
                try:
                    minutes = int(value)
                    days = minutes / (24 * 60)
                    print(f"📋 {env_path}: {minutes} 分钟 ({days:.1f} 天)")
                    return minutes
                except ValueError:
                    print(f"⚠️ {env_path}: 配置值无效 ({value})")
                    return None
        
        print(f"⚠️ {env_path}: 未找到ACCESS_TOKEN_EXPIRE_MINUTES配置")
        return None
        
    except Exception as e:
        print(f"❌ 读取{env_path}失败: {e}")
        return None

def check_runtime_config():
    """检查运行时配置"""
    try:
        # 添加backend目录到Python路径
        backend_path = Path("backend").absolute()
        if str(backend_path) not in sys.path:
            sys.path.insert(0, str(backend_path))
        
        from app.core.config import settings
        minutes = settings.access_token_expire_minutes
        days = minutes / (24 * 60)
        print(f"🔧 运行时配置: {minutes} 分钟 ({days:.1f} 天)")
        return minutes
        
    except Exception as e:
        print(f"⚠️ 无法获取运行时配置: {e}")
        return None

def main():
    """主函数"""
    print("🔐 JWT Token配置检查")
    print("=" * 40)
    
    # 检查项目目录
    if not Path("backend").exists() or not Path("frontend").exists():
        print("❌ 请在项目根目录下运行此脚本")
        print(f"当前目录: {os.getcwd()}")
        print("期望目录结构: backend/ frontend/")
        return 1
    
    print("📍 检查各配置文件中的JWT Token过期时间设置...\n")
    
    # 检查代码默认配置
    print("1️⃣ 代码默认配置:")
    code_config = check_code_config()
    
    print("\n2️⃣ 环境配置文件:")
    # 检查环境配置文件
    env_configs = []
    for env_file in ["backend/.env.production", "backend/.env"]:
        config = check_env_file(env_file)
        if config is not None:
            env_configs.append((env_file, config))
    
    if not env_configs:
        print("⚠️ 未找到任何环境配置文件")
    
    print("\n3️⃣ 运行时配置:")
    runtime_config = check_runtime_config()
    
    # 分析结果
    print("\n" + "=" * 40)
    print("📊 配置分析结果:")
    
    target_minutes = 43200  # 30天
    target_days = 30
    
    all_correct = True
    
    if code_config is not None:
        if code_config == target_minutes:
            print("✅ 代码默认配置正确")
        else:
            print(f"❌ 代码默认配置需要更新: {code_config} → {target_minutes}")
            all_correct = False
    
    for env_file, config in env_configs:
        if config == target_minutes:
            print(f"✅ {env_file} 配置正确")
        else:
            print(f"❌ {env_file} 需要更新: {config} → {target_minutes}")
            all_correct = False
    
    if runtime_config is not None:
        if runtime_config == target_minutes:
            print("✅ 运行时配置正确")
        else:
            print(f"❌ 运行时配置需要更新: {runtime_config} → {target_minutes}")
            all_correct = False
    
    print("\n" + "=" * 40)
    
    if all_correct:
        print("🎉 所有配置都正确！")
        print(f"✅ JWT Token过期时间已设置为 {target_days} 天")
        print("✅ 用户可以1个月内无需重新登录")
    else:
        print("⚠️ 发现配置问题，需要修复")
        print("\n🔧 修复建议:")
        print("1. 运行修复脚本:")
        print("   Linux/Mac: ./scripts/fix-jwt-token-expiry.sh")
        print("   Windows:   scripts\\fix-jwt-token-expiry.bat")
        print("2. 或手动修改配置文件，将ACCESS_TOKEN_EXPIRE_MINUTES设置为43200")
        print("3. 重启后端服务使配置生效")
    
    print("\n💡 配置说明:")
    print(f"• 目标配置: {target_minutes} 分钟 = {target_days} 天")
    print("• 计算公式: 30天 × 24小时 × 60分钟 = 43200分钟")
    print("• 用户体验: 登录一次可使用1个月")
    
    print("\n⚠️ 安全提醒:")
    print("• 延长Token过期时间可能存在安全风险")
    print("• 建议定期更换SECRET_KEY")
    print("• 监控异常登录行为")
    print("• 考虑在敏感操作时要求重新验证")
    
    return 0 if all_correct else 1

if __name__ == "__main__":
    exit(main())
