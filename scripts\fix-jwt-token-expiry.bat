@echo off
setlocal enabledelayedexpansion

REM JWT Token过期时间修复脚本 (Windows版本)
REM 用于解决用户频繁需要重新登录的问题
REM 将JWT Token过期时间从30分钟延长到1个月（43200分钟）

echo.
echo 🔐 JWT Token过期时间修复脚本 (Windows版本)
echo ================================================
echo 此脚本将JWT Token过期时间从30分钟延长到1个月（43200分钟）
echo 解决用户频繁需要重新登录的问题
echo.

REM 检查项目目录
if not exist "backend" (
    echo [ERROR] 请在项目根目录下运行此脚本
    echo 当前目录: %CD%
    echo 期望目录结构: backend\ frontend\
    pause
    exit /b 1
)

if not exist "frontend" (
    echo [ERROR] 请在项目根目录下运行此脚本
    echo 当前目录: %CD%
    echo 期望目录结构: backend\ frontend\
    pause
    exit /b 1
)

echo [INFO] 检查当前JWT Token配置...

REM 检查代码中的默认配置
if exist "backend\app\core\config.py" (
    findstr /C:"access_token_expire_minutes: int = " "backend\app\core\config.py" > nul
    if !errorlevel! equ 0 (
        for /f "tokens=5" %%a in ('findstr /C:"access_token_expire_minutes: int = " "backend\app\core\config.py"') do (
            set current_default=%%a
        )
        echo [INFO] 代码默认配置: !current_default! 分钟
        if "!current_default!"=="43200" (
            echo [SUCCESS] ✅ 代码默认配置已正确设置为1个月
        ) else (
            echo [WARNING] ⚠️ 代码默认配置仍为 !current_default! 分钟
        )
    )
)

REM 检查生产环境配置文件
set config_ok=true

if exist "backend\.env.production" (
    echo [INFO] 检查配置文件: backend\.env.production
    findstr /C:"ACCESS_TOKEN_EXPIRE_MINUTES" "backend\.env.production" > nul
    if !errorlevel! equ 0 (
        for /f "tokens=2 delims==" %%a in ('findstr /C:"ACCESS_TOKEN_EXPIRE_MINUTES" "backend\.env.production"') do (
            set current_value=%%a
        )
        echo [INFO] 当前设置: !current_value! 分钟
        if "!current_value!"=="43200" (
            echo [SUCCESS] ✅ backend\.env.production 已正确配置
        ) else (
            echo [WARNING] ⚠️ backend\.env.production 需要更新 (当前: !current_value! 分钟)
            set config_ok=false
        )
    ) else (
        echo [WARNING] ⚠️ backend\.env.production 中未找到 ACCESS_TOKEN_EXPIRE_MINUTES 配置
        set config_ok=false
    )
)

if exist "backend\.env" (
    echo [INFO] 检查配置文件: backend\.env
    findstr /C:"ACCESS_TOKEN_EXPIRE_MINUTES" "backend\.env" > nul
    if !errorlevel! equ 0 (
        for /f "tokens=2 delims==" %%a in ('findstr /C:"ACCESS_TOKEN_EXPIRE_MINUTES" "backend\.env"') do (
            set current_value=%%a
        )
        echo [INFO] 当前设置: !current_value! 分钟
        if "!current_value!"=="43200" (
            echo [SUCCESS] ✅ backend\.env 已正确配置
        ) else (
            echo [WARNING] ⚠️ backend\.env 需要更新 (当前: !current_value! 分钟)
            set config_ok=false
        )
    ) else (
        echo [WARNING] ⚠️ backend\.env 中未找到 ACCESS_TOKEN_EXPIRE_MINUTES 配置
        set config_ok=false
    )
)

if "!config_ok!"=="true" (
    echo.
    echo [SUCCESS] 🎉 JWT Token配置已经正确，无需修改
    goto :verify_fix
)

echo.
echo [WARNING] 检测到JWT Token过期时间配置需要更新
set /p continue="是否继续修复？(y/N): "
if /i not "!continue!"=="y" (
    echo [INFO] 操作已取消
    pause
    exit /b 0
)

REM 更新配置文件
set updated=false

if exist "backend\.env.production" (
    echo [INFO] 更新配置文件: backend\.env.production
    
    REM 备份原文件
    set timestamp=%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%
    set timestamp=!timestamp: =0!
    copy "backend\.env.production" "backend\.env.production.backup.!timestamp!" > nul
    echo [SUCCESS] 已备份配置文件: backend\.env.production.backup.!timestamp!
    
    REM 更新ACCESS_TOKEN_EXPIRE_MINUTES
    findstr /C:"ACCESS_TOKEN_EXPIRE_MINUTES" "backend\.env.production" > nul
    if !errorlevel! equ 0 (
        REM 替换现有配置
        powershell -Command "(Get-Content 'backend\.env.production') -replace 'ACCESS_TOKEN_EXPIRE_MINUTES=.*', 'ACCESS_TOKEN_EXPIRE_MINUTES=43200' | Set-Content 'backend\.env.production'"
        echo [SUCCESS] ✅ 已更新 ACCESS_TOKEN_EXPIRE_MINUTES=43200
    ) else (
        REM 添加新配置
        echo ACCESS_TOKEN_EXPIRE_MINUTES=43200 >> "backend\.env.production"
        echo [SUCCESS] ✅ 已添加 ACCESS_TOKEN_EXPIRE_MINUTES=43200
    )
    set updated=true
)

if exist "backend\.env" (
    echo [INFO] 更新配置文件: backend\.env
    
    REM 备份原文件
    set timestamp=%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%
    set timestamp=!timestamp: =0!
    copy "backend\.env" "backend\.env.backup.!timestamp!" > nul
    echo [SUCCESS] 已备份配置文件: backend\.env.backup.!timestamp!
    
    REM 更新ACCESS_TOKEN_EXPIRE_MINUTES
    findstr /C:"ACCESS_TOKEN_EXPIRE_MINUTES" "backend\.env" > nul
    if !errorlevel! equ 0 (
        REM 替换现有配置
        powershell -Command "(Get-Content 'backend\.env') -replace 'ACCESS_TOKEN_EXPIRE_MINUTES=.*', 'ACCESS_TOKEN_EXPIRE_MINUTES=43200' | Set-Content 'backend\.env'"
        echo [SUCCESS] ✅ 已更新 ACCESS_TOKEN_EXPIRE_MINUTES=43200
    ) else (
        REM 添加新配置
        echo ACCESS_TOKEN_EXPIRE_MINUTES=43200 >> "backend\.env"
        echo [SUCCESS] ✅ 已添加 ACCESS_TOKEN_EXPIRE_MINUTES=43200
    )
    set updated=true
)

if "!updated!"=="false" (
    echo [ERROR] 未找到任何配置文件需要更新
    pause
    exit /b 1
)

echo.
echo [INFO] 配置文件更新完成，请重启后端服务以使配置生效

:verify_fix
echo.
echo [INFO] === JWT Token 配置信息 ===
echo 过期时间: 43200 分钟 (30天)
echo 计算公式: 30天 × 24小时 × 60分钟 = 43200分钟
echo.
echo [INFO] === 用户体验改善 ===
echo • 用户登录后1个月内无需重新登录
echo • 大大减少了频繁登录的困扰
echo • 提升了用户使用体验
echo.

echo [WARNING] === 安全提醒 ===
echo • JWT Token过期时间延长到1个月可能存在安全风险
echo • 建议在生产环境中考虑以下安全措施：
echo   - 定期更换SECRET_KEY
echo   - 监控异常登录行为
echo   - 考虑实现refresh token机制
echo   - 在敏感操作时要求重新验证
echo.

echo [SUCCESS] 🎉 JWT Token过期时间修复完成！
echo [INFO] 用户现在可以1个月内无需重新登录
echo.
echo 请重启后端服务以使配置生效：
echo   开发环境: 重新运行 python main.py
echo   生产环境: 重启相关服务
echo.
pause
