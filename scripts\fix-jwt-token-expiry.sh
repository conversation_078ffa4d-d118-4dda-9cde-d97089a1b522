#!/bin/bash

# JWT Token过期时间修复脚本
# 用于解决用户频繁需要重新登录的问题
# 将JWT Token过期时间从30分钟延长到1个月（43200分钟）

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_warning "检测到root用户，建议使用普通用户运行此脚本"
        read -p "是否继续？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

# 检查项目目录
check_project_dir() {
    if [[ ! -d "backend" ]] || [[ ! -d "frontend" ]]; then
        log_error "请在项目根目录下运行此脚本"
        log_info "当前目录: $(pwd)"
        log_info "期望目录结构: backend/ frontend/"
        exit 1
    fi
}

# 备份配置文件
backup_config() {
    local config_file="$1"
    if [[ -f "$config_file" ]]; then
        local backup_file="${config_file}.backup.$(date +%Y%m%d_%H%M%S)"
        cp "$config_file" "$backup_file"
        log_success "已备份配置文件: $backup_file"
    fi
}

# 检查当前JWT配置
check_current_config() {
    log_info "检查当前JWT Token配置..."
    
    # 检查代码中的默认配置
    if [[ -f "backend/app/core/config.py" ]]; then
        local current_default=$(grep -o "access_token_expire_minutes: int = [0-9]*" backend/app/core/config.py | grep -o "[0-9]*")
        if [[ -n "$current_default" ]]; then
            log_info "代码默认配置: $current_default 分钟"
            if [[ "$current_default" == "43200" ]]; then
                log_success "✅ 代码默认配置已正确设置为1个月"
            else
                log_warning "⚠️ 代码默认配置仍为 $current_default 分钟"
            fi
        fi
    fi
    
    # 检查生产环境配置文件
    local env_files=("backend/.env.production" "backend/.env")
    for env_file in "${env_files[@]}"; do
        if [[ -f "$env_file" ]]; then
            log_info "检查配置文件: $env_file"
            if grep -q "ACCESS_TOKEN_EXPIRE_MINUTES" "$env_file"; then
                local current_value=$(grep "ACCESS_TOKEN_EXPIRE_MINUTES" "$env_file" | cut -d'=' -f2 | tr -d ' ')
                log_info "当前设置: $current_value 分钟"
                if [[ "$current_value" == "43200" ]]; then
                    log_success "✅ $env_file 已正确配置"
                else
                    log_warning "⚠️ $env_file 需要更新 (当前: $current_value 分钟)"
                    return 1
                fi
            else
                log_warning "⚠️ $env_file 中未找到 ACCESS_TOKEN_EXPIRE_MINUTES 配置"
                return 1
            fi
        fi
    done
    
    return 0
}

# 更新配置文件
update_config_file() {
    local config_file="$1"
    
    if [[ ! -f "$config_file" ]]; then
        log_warning "配置文件不存在: $config_file"
        return 1
    fi
    
    log_info "更新配置文件: $config_file"
    
    # 备份原文件
    backup_config "$config_file"
    
    # 更新ACCESS_TOKEN_EXPIRE_MINUTES
    if grep -q "ACCESS_TOKEN_EXPIRE_MINUTES" "$config_file"; then
        # 替换现有配置
        sed -i 's/ACCESS_TOKEN_EXPIRE_MINUTES=.*/ACCESS_TOKEN_EXPIRE_MINUTES=43200/' "$config_file"
        log_success "✅ 已更新 ACCESS_TOKEN_EXPIRE_MINUTES=43200"
    else
        # 添加新配置（在ALGORITHM行后）
        if grep -q "ALGORITHM=" "$config_file"; then
            sed -i '/ALGORITHM=/a ACCESS_TOKEN_EXPIRE_MINUTES=43200' "$config_file"
            log_success "✅ 已添加 ACCESS_TOKEN_EXPIRE_MINUTES=43200"
        else
            # 添加到Security部分
            if grep -q "# Security" "$config_file"; then
                sed -i '/# Security/a ACCESS_TOKEN_EXPIRE_MINUTES=43200' "$config_file"
            else
                # 添加到文件末尾
                echo "" >> "$config_file"
                echo "# JWT Token Configuration" >> "$config_file"
                echo "ACCESS_TOKEN_EXPIRE_MINUTES=43200" >> "$config_file"
            fi
            log_success "✅ 已添加 ACCESS_TOKEN_EXPIRE_MINUTES=43200"
        fi
    fi
}

# 重启服务
restart_services() {
    log_info "重启相关服务..."
    
    # 检查是否有systemd服务
    if systemctl list-units --type=service | grep -q "xhs-backend"; then
        log_info "重启 xhs-backend 服务..."
        if sudo systemctl restart xhs-backend; then
            log_success "✅ xhs-backend 服务重启成功"
        else
            log_error "❌ xhs-backend 服务重启失败"
            return 1
        fi
    else
        log_warning "未找到 xhs-backend systemd 服务"
        log_info "请手动重启后端服务"
    fi
    
    # 检查服务状态
    sleep 2
    if systemctl is-active --quiet xhs-backend 2>/dev/null; then
        log_success "✅ 后端服务运行正常"
    else
        log_warning "⚠️ 请检查后端服务状态: sudo systemctl status xhs-backend"
    fi
}

# 验证修复效果
verify_fix() {
    log_info "验证修复效果..."
    
    # 等待服务启动
    sleep 3
    
    # 测试API健康检查
    if command -v curl >/dev/null 2>&1; then
        local health_url="http://127.0.0.1:8080/health"
        if curl -s "$health_url" >/dev/null; then
            log_success "✅ API服务响应正常"
        else
            log_warning "⚠️ API服务可能未完全启动，请稍后手动测试"
        fi
    fi
    
    # 显示配置信息
    echo
    log_info "=== JWT Token 配置信息 ==="
    echo "过期时间: 43200 分钟 (30天)"
    echo "计算公式: 30天 × 24小时 × 60分钟 = 43200分钟"
    echo
    log_info "=== 用户体验改善 ==="
    echo "• 用户登录后1个月内无需重新登录"
    echo "• 大大减少了频繁登录的困扰"
    echo "• 提升了用户使用体验"
    echo
}

# 显示安全提醒
show_security_notice() {
    echo
    log_warning "=== 安全提醒 ==="
    echo "• JWT Token过期时间延长到1个月可能存在安全风险"
    echo "• 建议在生产环境中考虑以下安全措施："
    echo "  - 定期更换SECRET_KEY"
    echo "  - 监控异常登录行为"
    echo "  - 考虑实现refresh token机制"
    echo "  - 在敏感操作时要求重新验证"
    echo
}

# 主函数
main() {
    echo "🔐 JWT Token过期时间修复脚本"
    echo "================================"
    echo "此脚本将JWT Token过期时间从30分钟延长到1个月（43200分钟）"
    echo "解决用户频繁需要重新登录的问题"
    echo
    
    # 检查环境
    check_root
    check_project_dir
    
    # 检查当前配置
    if check_current_config; then
        log_success "🎉 JWT Token配置已经正确，无需修改"
        verify_fix
        show_security_notice
        exit 0
    fi
    
    echo
    log_warning "检测到JWT Token过期时间配置需要更新"
    read -p "是否继续修复？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "操作已取消"
        exit 0
    fi
    
    # 更新配置文件
    local updated=false
    for env_file in "backend/.env.production" "backend/.env"; do
        if [[ -f "$env_file" ]]; then
            update_config_file "$env_file"
            updated=true
        fi
    done
    
    if [[ "$updated" == "false" ]]; then
        log_error "未找到任何配置文件需要更新"
        exit 1
    fi
    
    # 重启服务
    restart_services
    
    # 验证修复
    verify_fix
    
    # 安全提醒
    show_security_notice
    
    log_success "🎉 JWT Token过期时间修复完成！"
    log_info "用户现在可以1个月内无需重新登录"
}

# 运行主函数
main "$@"
