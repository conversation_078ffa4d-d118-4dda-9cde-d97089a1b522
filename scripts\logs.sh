#!/bin/bash

case "$1" in
    backend)
        echo "=== Backend Error Logs ==="
        sudo tail -f /home/<USER>/xhs_notes_manager/backend/logs/error.log
        ;;
    access)
        echo "=== Backend Access Logs ==="
        sudo tail -f /home/<USER>/xhs_notes_manager/backend/logs/access.log
        ;;
    nginx)
        echo "=== Nginx Error Logs ==="
        sudo tail -f /var/log/nginx/xhs-notes-manager.error.log
        ;;
    nginx-access)
        echo "=== Nginx Access Logs ==="
        sudo tail -f /var/log/nginx/xhs-notes-manager.access.log
        ;;
    mysql)
        echo "=== MySQL Error Logs ==="
        sudo tail -f /var/log/mysql/error.log
        ;;
    systemd)
        echo "=== Systemd Service Logs ==="
        sudo journalctl -u xhs-backend -f
        ;;
    *)
        echo "Usage: $0 {backend|access|nginx|nginx-access|mysql|systemd}"
        echo
        echo "Available log types:"
        echo "  backend      - Backend application error logs"
        echo "  access       - Backend application access logs"
        echo "  nginx        - Nginx error logs"
        echo "  nginx-access - Nginx access logs"
        echo "  mysql        - MySQL error logs"
        echo "  systemd      - Systemd service logs"
        ;;
esac
