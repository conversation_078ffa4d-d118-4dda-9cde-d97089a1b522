#!/bin/bash

# Ubuntu 24 部署前环境检查脚本
# 检查系统是否满足部署要求

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
PROJECT_DIR="/home/<USER>/xhs_notes_manager"
MIN_RAM_GB=4
MIN_DISK_GB=20

# 检查结果
CHECKS_PASSED=0
CHECKS_FAILED=0
WARNINGS=0

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[✓]${NC} $1"
    ((CHECKS_PASSED++))
}

log_warning() {
    echo -e "${YELLOW}[⚠]${NC} $1"
    ((WARNINGS++))
}

log_error() {
    echo -e "${RED}[✗]${NC} $1"
    ((CHECKS_FAILED++))
}

# 检查系统版本
check_system_version() {
    log_info "检查系统版本..."
    
    if grep -q "Ubuntu 24" /etc/os-release; then
        log_success "系统版本: Ubuntu 24.04 LTS"
    elif grep -q "Ubuntu" /etc/os-release; then
        VERSION=$(grep VERSION_ID /etc/os-release | cut -d'"' -f2)
        log_warning "系统版本: Ubuntu $VERSION (推荐使用 Ubuntu 24.04)"
    else
        log_error "不支持的操作系统，需要 Ubuntu 24.04 LTS"
    fi
}

# 检查用户权限
check_user_permissions() {
    log_info "检查用户权限..."
    
    if [[ $EUID -eq 0 ]]; then
        log_error "不应该使用 root 用户运行部署脚本"
    else
        log_success "当前用户: $USER (非root用户)"
    fi
    
    if sudo -n true 2>/dev/null; then
        log_success "sudo 权限可用"
    else
        log_error "需要 sudo 权限"
    fi
}

# 检查硬件资源
check_hardware() {
    log_info "检查硬件资源..."
    
    # 检查内存
    TOTAL_RAM_KB=$(grep MemTotal /proc/meminfo | awk '{print $2}')
    TOTAL_RAM_GB=$((TOTAL_RAM_KB / 1024 / 1024))
    
    if [ $TOTAL_RAM_GB -ge $MIN_RAM_GB ]; then
        log_success "内存: ${TOTAL_RAM_GB}GB (满足最低要求 ${MIN_RAM_GB}GB)"
    else
        log_error "内存不足: ${TOTAL_RAM_GB}GB (需要至少 ${MIN_RAM_GB}GB)"
    fi
    
    # 检查磁盘空间
    AVAILABLE_DISK_GB=$(df /home | tail -1 | awk '{print int($4/1024/1024)}')
    
    if [ $AVAILABLE_DISK_GB -ge $MIN_DISK_GB ]; then
        log_success "磁盘空间: ${AVAILABLE_DISK_GB}GB 可用 (满足最低要求 ${MIN_DISK_GB}GB)"
    else
        log_error "磁盘空间不足: ${AVAILABLE_DISK_GB}GB 可用 (需要至少 ${MIN_DISK_GB}GB)"
    fi
    
    # 检查CPU核心数
    CPU_CORES=$(nproc)
    if [ $CPU_CORES -ge 2 ]; then
        log_success "CPU核心数: $CPU_CORES (满足最低要求 2核)"
    else
        log_warning "CPU核心数: $CPU_CORES (推荐至少 2核)"
    fi
}

# 检查网络连接
check_network() {
    log_info "检查网络连接..."
    
    if ping -c 1 8.8.8.8 >/dev/null 2>&1; then
        log_success "互联网连接正常"
    else
        log_error "无法连接到互联网"
    fi
    
    if ping -c 1 archive.ubuntu.com >/dev/null 2>&1; then
        log_success "Ubuntu软件源连接正常"
    else
        log_warning "无法连接到Ubuntu软件源"
    fi
}

# 检查端口占用
check_ports() {
    log_info "检查端口占用..."
    
    PORTS=(80 443 3306 8000)
    
    for port in "${PORTS[@]}"; do
        if netstat -tuln 2>/dev/null | grep -q ":$port "; then
            log_warning "端口 $port 已被占用"
        else
            log_success "端口 $port 可用"
        fi
    done
}

# 检查必要的命令
check_commands() {
    log_info "检查必要的命令..."
    
    COMMANDS=(curl wget git)
    
    for cmd in "${COMMANDS[@]}"; do
        if command -v $cmd >/dev/null 2>&1; then
            log_success "命令 $cmd 可用"
        else
            log_error "命令 $cmd 未安装"
        fi
    done
}

# 检查项目目录
check_project_directory() {
    log_info "检查项目目录..."
    
    if [ -d "$PROJECT_DIR" ]; then
        log_success "项目目录存在: $PROJECT_DIR"
        
        # 检查关键文件
        if [ -f "$PROJECT_DIR/backend/requirements.txt" ]; then
            log_success "后端依赖文件存在"
        else
            log_error "后端依赖文件不存在: $PROJECT_DIR/backend/requirements.txt"
        fi
        
        if [ -f "$PROJECT_DIR/frontend/package.json" ]; then
            log_success "前端配置文件存在"
        else
            log_error "前端配置文件不存在: $PROJECT_DIR/frontend/package.json"
        fi
        
        # 检查目录权限
        if [ -w "$PROJECT_DIR" ]; then
            log_success "项目目录可写"
        else
            log_error "项目目录不可写"
        fi
    else
        log_error "项目目录不存在: $PROJECT_DIR"
        log_info "请先将项目代码复制到该目录"
    fi
}

# 检查现有服务
check_existing_services() {
    log_info "检查现有服务..."
    
    SERVICES=(nginx mysql apache2)
    
    for service in "${SERVICES[@]}"; do
        if systemctl is-active --quiet $service 2>/dev/null; then
            if [ "$service" = "apache2" ]; then
                log_warning "Apache2 正在运行，可能与 Nginx 冲突"
            else
                log_warning "服务 $service 已在运行"
            fi
        fi
    done
}

# 检查防火墙状态
check_firewall() {
    log_info "检查防火墙状态..."
    
    if command -v ufw >/dev/null 2>&1; then
        UFW_STATUS=$(sudo ufw status | head -1)
        if echo "$UFW_STATUS" | grep -q "inactive"; then
            log_success "UFW 防火墙已安装但未激活"
        else
            log_warning "UFW 防火墙已激活，部署时会自动配置规则"
        fi
    else
        log_warning "UFW 防火墙未安装，将在部署时安装"
    fi
}

# 检查 Python 版本
check_python() {
    log_info "检查 Python 版本..."
    
    if command -v python3 >/dev/null 2>&1; then
        PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
        PYTHON_MAJOR=$(echo $PYTHON_VERSION | cut -d'.' -f1)
        PYTHON_MINOR=$(echo $PYTHON_VERSION | cut -d'.' -f2)
        
        if [ $PYTHON_MAJOR -eq 3 ] && [ $PYTHON_MINOR -ge 10 ]; then
            log_success "Python 版本: $PYTHON_VERSION (满足要求 >= 3.10)"
        else
            log_warning "Python 版本: $PYTHON_VERSION (推荐 >= 3.12)"
        fi
    else
        log_error "Python3 未安装"
    fi
}

# 检查 Node.js 版本
check_nodejs() {
    log_info "检查 Node.js 版本..."
    
    if command -v node >/dev/null 2>&1; then
        NODE_VERSION=$(node --version | sed 's/v//')
        NODE_MAJOR=$(echo $NODE_VERSION | cut -d'.' -f1)
        
        if [ $NODE_MAJOR -ge 18 ]; then
            log_success "Node.js 版本: $NODE_VERSION (满足要求 >= 18)"
        else
            log_warning "Node.js 版本: $NODE_VERSION (推荐 >= 20)"
        fi
    else
        log_warning "Node.js 未安装，将在部署时安装"
    fi
}

# 生成检查报告
generate_report() {
    echo
    echo "=========================================="
    echo "           环境检查报告"
    echo "=========================================="
    echo
    echo "检查通过: $CHECKS_PASSED"
    echo "检查失败: $CHECKS_FAILED"
    echo "警告信息: $WARNINGS"
    echo
    
    if [ $CHECKS_FAILED -eq 0 ]; then
        echo -e "${GREEN}✓ 环境检查通过，可以开始部署${NC}"
        echo
        echo "运行以下命令开始自动部署："
        echo "  chmod +x scripts/ubuntu24-deploy.sh"
        echo "  ./scripts/ubuntu24-deploy.sh"
        return 0
    else
        echo -e "${RED}✗ 环境检查失败，请解决以上问题后重新检查${NC}"
        echo
        echo "常见问题解决方案："
        echo "1. 系统更新: sudo apt update && sudo apt upgrade -y"
        echo "2. 安装基础工具: sudo apt install -y curl wget git"
        echo "3. 检查磁盘空间: df -h"
        echo "4. 检查内存: free -h"
        echo "5. 停止冲突服务: sudo systemctl stop apache2"
        return 1
    fi
}

# 主函数
main() {
    echo "=========================================="
    echo "      Ubuntu 24 部署前环境检查"
    echo "      小红书笔记管理系统"
    echo "=========================================="
    echo
    
    check_system_version
    check_user_permissions
    check_hardware
    check_network
    check_ports
    check_commands
    check_project_directory
    check_existing_services
    check_firewall
    check_python
    check_nodejs
    
    generate_report
}

# 运行主函数
main "$@"
