#!/bin/bash
echo "Updating XHS Notes Manager..."

# 获取项目根目录
PROJECT_ROOT=$(cd "$(dirname "$0")/.." && pwd)
echo "Project directory: $PROJECT_ROOT"

# 重新构建前端
echo "Building frontend..."
cd "$PROJECT_ROOT/frontend"
npm run build

if [ $? -ne 0 ]; then
    echo "Frontend build failed!"
    exit 1
fi

echo "Frontend build completed successfully!"

# 重启后端服务
echo "Restarting backend service..."
sudo systemctl restart xhs-backend

# 重新加载 Nginx
echo "Reloading Nginx..."
sudo systemctl reload nginx

echo "All services updated successfully!"
echo "Please refresh your browser (Ctrl+Shift+R) to see the changes."
