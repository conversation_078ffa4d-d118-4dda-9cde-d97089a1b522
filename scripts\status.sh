#!/bin/bash
echo "=== XHS Notes Manager Status ==="
echo

echo "Backend Service:"
sudo systemctl status xhs-backend --no-pager -l

echo
echo "Nginx Service:"
sudo systemctl status nginx --no-pager -l

echo
echo "MySQL Service:"
sudo systemctl status mysql --no-pager -l

echo
echo "Disk Usage:"
df -h /home/<USER>/xhs_notes_manager

echo
echo "Memory Usage:"
free -h

echo
echo "Process Information:"
ps aux | grep -E "(gunicorn|nginx|mysql)" | grep -v grep
