#!/bin/bash

# Ubuntu 24 + Nginx + MySQL 自动部署脚本
# 小红书笔记管理系统生产环境部署

set -e  # 遇到错误立即退出

# 配置变量
PROJECT_DIR="/home/<USER>/xhs_notes_manager"
DB_NAME="xhsnote"
DB_USER="xhsnote_user"
DB_PASSWORD="dhwdw6789jhgffHGF"
SERVER_NAME="ben"
ADMIN_USER="admin"
ADMIN_PASSWORD="admin123"

# 端口配置 (避免与现有服务冲突)
FRONTEND_PORT="8080"    # Nginx前端端口 (替代默认80)
BACKEND_PORT="9000"     # FastAPI后端端口 (替代默认8000)
MYSQL_PORT="3306"       # MySQL端口 (保持默认)

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_error "请不要使用root用户运行此脚本"
        exit 1
    fi
}

# 检查系统版本
check_system() {
    log_info "检查系统版本..."
    if ! grep -q "Ubuntu 24" /etc/os-release; then
        log_warning "此脚本专为Ubuntu 24设计，当前系统可能不兼容"
        read -p "是否继续？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
    log_success "系统检查通过"
}

# 更新系统
update_system() {
    log_info "更新系统包..."
    sudo apt update && sudo apt upgrade -y
    sudo apt install -y curl wget git vim htop unzip software-properties-common
    log_success "系统更新完成"
}

# 安装Python
install_python() {
    log_info "安装Python 3.12..."
    sudo apt install -y python3 python3-pip python3-venv python3-dev
    python3 --version
    log_success "Python安装完成"
}

# 安装Node.js
install_nodejs() {
    log_info "安装Node.js 20..."
    curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
    sudo apt install -y nodejs
    node --version
    npm --version
    log_success "Node.js安装完成"
}

# 安装MySQL
install_mysql() {
    log_info "安装MySQL..."
    sudo apt install -y mysql-server mysql-client
    sudo systemctl start mysql
    sudo systemctl enable mysql
    log_success "MySQL安装完成"
}

# 配置MySQL
configure_mysql() {
    log_info "配置MySQL数据库..."
    
    # 创建数据库和用户
    sudo mysql -e "CREATE DATABASE IF NOT EXISTS ${DB_NAME} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
    sudo mysql -e "CREATE USER IF NOT EXISTS '${DB_USER}'@'localhost' IDENTIFIED BY '${DB_PASSWORD}';"
    sudo mysql -e "GRANT ALL PRIVILEGES ON ${DB_NAME}.* TO '${DB_USER}'@'localhost';"
    sudo mysql -e "FLUSH PRIVILEGES;"
    
    # 测试连接
    mysql -u ${DB_USER} -p${DB_PASSWORD} ${DB_NAME} -e "SELECT 1;" > /dev/null
    log_success "MySQL配置完成"
}

# 安装Nginx
install_nginx() {
    log_info "安装Nginx..."
    sudo apt install -y nginx
    sudo systemctl start nginx
    sudo systemctl enable nginx
    log_success "Nginx安装完成"
}

# 创建项目目录
create_project_dir() {
    log_info "创建项目目录..."
    sudo mkdir -p /home/<USER>
    sudo chown $USER:$USER /home/<USER>
    log_success "项目目录创建完成"
}

# 部署项目代码
deploy_code() {
    log_info "部署项目代码..."
    
    if [ ! -d "$PROJECT_DIR" ]; then
        log_error "项目目录不存在: $PROJECT_DIR"
        log_info "请先将项目代码复制到该目录"
        exit 1
    fi
    
    cd $PROJECT_DIR
    log_success "项目代码部署完成"
}

# 配置后端
configure_backend() {
    log_info "配置后端环境..."
    
    cd $PROJECT_DIR/backend
    
    # 创建虚拟环境
    python3 -m venv venv
    source venv/bin/activate
    
    # 安装依赖
    pip install --upgrade pip
    pip install -r requirements.txt
    pip install gunicorn uvicorn[standard] pymysql cryptography
    
    # 创建生产环境配置
    cat > .env.production << EOF
DATABASE_URL=mysql+pymysql://${DB_USER}:${DB_PASSWORD}@localhost:3306/${DB_NAME}
SECRET_KEY=$(openssl rand -hex 32)
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=43200
UPLOAD_DIR=${PROJECT_DIR}/backend/uploads
MAX_FILE_SIZE=524288000
ALLOWED_IMAGE_EXTENSIONS=jpg,jpeg,png,gif,webp
ALLOWED_VIDEO_EXTENSIONS=mp4,mov,avi
ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000,http://${SERVER_NAME},https://${SERVER_NAME}
DEBUG=false
LOG_LEVEL=INFO
ENVIRONMENT=production
EOF
    
    # 创建上传目录
    mkdir -p uploads
    
    # 创建日志目录
    mkdir -p logs
    
    # 初始化数据库
    export ENV_FILE=$PROJECT_DIR/backend/.env.production
    python init_db.py
    
    log_success "后端配置完成"
}

# 配置前端
configure_frontend() {
    log_info "配置前端环境..."
    
    cd $PROJECT_DIR/frontend
    
    # 安装依赖
    npm install
    
    # 创建生产环境配置
    cat > .env.production << EOF
VITE_API_BASE_URL=http://${SERVER_NAME}:${BACKEND_PORT}
VITE_APP_TITLE=小红书笔记管理系统
EOF
    
    # 构建生产版本
    npm run build
    
    log_success "前端配置完成"
}

# 配置Gunicorn
configure_gunicorn() {
    log_info "配置Gunicorn..."
    
    cat > $PROJECT_DIR/backend/gunicorn.conf.py << EOF
import multiprocessing

bind = "127.0.0.1:${BACKEND_PORT}"
backlog = 2048
workers = multiprocessing.cpu_count() * 2 + 1
worker_class = "uvicorn.workers.UvicornWorker"
worker_connections = 1000
timeout = 30
keepalive = 2
max_requests = 1000
max_requests_jitter = 100
accesslog = "/home/<USER>/xhs_notes_manager/backend/logs/access.log"
errorlog = "/home/<USER>/xhs_notes_manager/backend/logs/error.log"
loglevel = "info"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'
proc_name = "xhs_notes_manager"
daemon = False
pidfile = "/home/<USER>/xhs_notes_manager/backend/gunicorn.pid"
EOF
    
    log_success "Gunicorn配置完成"
}

# 创建systemd服务
create_systemd_service() {
    log_info "创建systemd服务..."
    
    sudo tee /etc/systemd/system/xhs-backend.service > /dev/null << EOF
[Unit]
Description=XHS Notes Manager Backend
After=network.target mysql.service
Requires=mysql.service

[Service]
Type=notify
User=$USER
Group=$USER
WorkingDirectory=$PROJECT_DIR/backend
Environment=PATH=$PROJECT_DIR/backend/venv/bin
Environment=ENV_FILE=$PROJECT_DIR/backend/.env.production
ExecStart=$PROJECT_DIR/backend/venv/bin/gunicorn -c gunicorn.conf.py app.main:app
ExecReload=/bin/kill -s HUP \$MAINPID
Restart=always
RestartSec=3
KillMode=mixed
TimeoutStopSec=5

[Install]
WantedBy=multi-user.target
EOF
    
    sudo systemctl daemon-reload
    sudo systemctl enable xhs-backend
    
    log_success "systemd服务创建完成"
}

# 配置Nginx
configure_nginx() {
    log_info "配置Nginx..."
    
    sudo tee /etc/nginx/sites-available/xhs-notes-manager > /dev/null << EOF
server {
    listen ${FRONTEND_PORT};
    server_name ${SERVER_NAME} localhost;
    
    client_max_body_size 10M;

    location / {
        root $PROJECT_DIR/frontend/dist;
        try_files \$uri \$uri/ /index.html;
        
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)\$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    location /api/ {
        proxy_pass http://127.0.0.1:${BACKEND_PORT};
        proxy_http_version 1.1;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_read_timeout 300;
        proxy_connect_timeout 300;
        proxy_send_timeout 300;
    }

    location /uploads/ {
        proxy_pass http://127.0.0.1:${BACKEND_PORT};
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    location /docs {
        proxy_pass http://127.0.0.1:${BACKEND_PORT};
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    location /health {
        proxy_pass http://127.0.0.1:${BACKEND_PORT};
        proxy_set_header Host \$host;
        access_log off;
    }

    access_log /var/log/nginx/xhs-notes-manager.access.log;
    error_log /var/log/nginx/xhs-notes-manager.error.log;
}
EOF
    
    sudo ln -sf /etc/nginx/sites-available/xhs-notes-manager /etc/nginx/sites-enabled/
    sudo rm -f /etc/nginx/sites-enabled/default
    sudo nginx -t
    sudo systemctl reload nginx
    
    log_success "Nginx配置完成"
}

# 配置防火墙
configure_firewall() {
    log_info "配置防火墙..."
    
    sudo ufw default deny incoming
    sudo ufw default allow outgoing
    sudo ufw allow ssh
    sudo ufw allow 'Nginx Full'
    sudo ufw allow ${FRONTEND_PORT}
    sudo ufw allow 443
    sudo ufw --force enable
    
    log_success "防火墙配置完成"
}

# 创建管理员账户
create_admin() {
    log_info "创建管理员账户..."
    
    cd $PROJECT_DIR/backend
    source venv/bin/activate
    export ENV_FILE=$PROJECT_DIR/backend/.env.production
    
    python reset_admin_password.py create $ADMIN_USER $ADMIN_PASSWORD
    
    log_success "管理员账户创建完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    sudo systemctl start xhs-backend
    sudo systemctl reload nginx
    
    # 等待服务启动
    sleep 5
    
    log_success "服务启动完成"
}

# 验证部署
verify_deployment() {
    log_info "验证部署..."
    
    # 检查服务状态
    if sudo systemctl is-active --quiet xhs-backend; then
        log_success "后端服务运行正常"
    else
        log_error "后端服务启动失败"
        return 1
    fi
    
    if sudo systemctl is-active --quiet nginx; then
        log_success "Nginx服务运行正常"
    else
        log_error "Nginx服务启动失败"
        return 1
    fi
    
    # 测试API
    if curl -s http://localhost:${FRONTEND_PORT}/health > /dev/null; then
        log_success "API健康检查通过"
    else
        log_error "API健康检查失败"
        return 1
    fi
    
    log_success "部署验证完成"
}

# 显示部署信息
show_deployment_info() {
    echo
    echo "=========================================="
    echo "         部署完成！"
    echo "=========================================="
    echo
    echo "访问地址："
    echo "  前端界面: http://${SERVER_NAME}:${FRONTEND_PORT}"
    echo "  API文档:  http://${SERVER_NAME}:${BACKEND_PORT}/docs"
    echo "  健康检查: http://${SERVER_NAME}:${FRONTEND_PORT}/health"
    echo
    echo "管理员账户："
    echo "  用户名: ${ADMIN_USER}"
    echo "  密码:   ${ADMIN_PASSWORD}"
    echo
    echo "管理脚本："
    echo "  启动:   sudo systemctl start xhs-backend"
    echo "  停止:   sudo systemctl stop xhs-backend"
    echo "  重启:   sudo systemctl restart xhs-backend"
    echo "  状态:   sudo systemctl status xhs-backend"
    echo
    echo "日志查看："
    echo "  后端日志: sudo journalctl -u xhs-backend -f"
    echo "  Nginx日志: sudo tail -f /var/log/nginx/xhs-notes-manager.error.log"
    echo
    echo "=========================================="
}

# 主函数
main() {
    echo "=========================================="
    echo "  Ubuntu 24 + Nginx + MySQL 自动部署"
    echo "  小红书笔记管理系统"
    echo "=========================================="
    echo
    
    check_root
    check_system
    
    log_info "开始自动部署..."
    
    update_system
    install_python
    install_nodejs
    install_mysql
    configure_mysql
    install_nginx
    create_project_dir
    deploy_code
    configure_backend
    configure_frontend
    configure_gunicorn
    create_systemd_service
    configure_nginx
    configure_firewall
    create_admin
    start_services
    verify_deployment
    
    show_deployment_info
    
    log_success "自动部署完成！"
}

# 运行主函数
main "$@"
