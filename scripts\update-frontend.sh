#!/bin/bash

# 前端更新脚本
# 用于生产环境前端代码更新

set -e

# 颜色输出
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 获取项目根目录
PROJECT_ROOT=$(cd "$(dirname "$0")/.." && pwd)

echo "=========================================="
echo "         前端更新脚本"
echo "=========================================="
echo

log_info "项目目录: $PROJECT_ROOT"

# 进入前端目录
cd "$PROJECT_ROOT/frontend"

log_info "重新构建前端..."
npm run build

log_success "前端构建完成"

# 重新加载Nginx
log_info "重新加载Nginx..."
sudo systemctl reload nginx

log_success "Nginx重新加载完成"

echo
echo "=========================================="
echo "         前端更新完成！"
echo "=========================================="
echo
echo "请刷新浏览器查看更新效果"
echo
