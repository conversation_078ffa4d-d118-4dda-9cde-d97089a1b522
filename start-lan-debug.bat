@echo off
chcp 65001 >nul
title XHS Notes Manager - LAN Access Setup (Debug Mode)

echo.
echo ========================================
echo   XHS Notes Manager - LAN Debug Setup
echo ========================================
echo.

:: Get local IP address
echo [DEBUG] Detecting local IP address...
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /c:"IPv4"') do (
    for /f "tokens=1" %%b in ("%%a") do (
        set LOCAL_IP=%%b
        echo [DEBUG] Found IP candidate: %%b
        goto :found_ip
    )
)

:found_ip
if "%LOCAL_IP%"=="" (
    echo [ERROR] Could not detect local IP address
    echo [DEBUG] Running full ipconfig output:
    ipconfig
    echo Please manually check your IP with: ipconfig
    pause
    exit /b 1
)

echo [INFO] Detected local IP: %LOCAL_IP%
echo.

:: Network diagnostics
echo [DEBUG] Running network diagnostics...
echo [DEBUG] Testing localhost connectivity...
ping -n 1 127.0.0.1 >nul 2>&1
if %errorlevel% equ 0 (
    echo [✓] Localhost ping successful
) else (
    echo [✗] Localhost ping failed
)

echo [DEBUG] Testing local IP connectivity...
ping -n 1 %LOCAL_IP% >nul 2>&1
if %errorlevel% equ 0 (
    echo [✓] Local IP ping successful
) else (
    echo [✗] Local IP ping failed
)

echo [DEBUG] Checking port availability...
netstat -an | findstr ":3000" >nul 2>&1
if %errorlevel% equ 0 (
    echo [WARNING] Port 3000 is already in use
    netstat -an | findstr ":3000"
) else (
    echo [✓] Port 3000 is available
)

netstat -an | findstr ":8000" >nul 2>&1
if %errorlevel% equ 0 (
    echo [WARNING] Port 8000 is already in use
    netstat -an | findstr ":8000"
) else (
    echo [✓] Port 8000 is available
)

echo.

:: Backup original configs
echo [DEBUG] Creating configuration backups...
if not exist "backend\.env.backup" (
    echo [BACKUP] Creating backup of backend/.env
    copy "backend\.env" "backend\.env.backup" >nul
    if %errorlevel% equ 0 (
        echo [✓] Backend config backed up
    ) else (
        echo [✗] Backend config backup failed
    )
) else (
    echo [INFO] Backend config backup already exists
)

if not exist "frontend\vite.config.ts.backup" (
    echo [BACKUP] Creating backup of frontend/vite.config.ts
    copy "frontend\vite.config.ts" "frontend\vite.config.ts.backup" >nul
    if %errorlevel% equ 0 (
        echo [✓] Frontend config backed up
    ) else (
        echo [✗] Frontend config backup failed
    )
) else (
    echo [INFO] Frontend config backup already exists
)

:: Update backend CORS configuration
echo [DEBUG] Updating backend CORS configuration...
echo [DEBUG] Original backend/.env content:
type "backend\.env" | findstr "ALLOWED_ORIGINS"

powershell -Command "(Get-Content 'backend\.env') -replace 'ALLOWED_ORIGINS=.*', 'ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000,http://%LOCAL_IP%:3000' | Set-Content 'backend\.env'"

echo [DEBUG] Updated backend/.env content:
type "backend\.env" | findstr "ALLOWED_ORIGINS"

:: Update frontend proxy configuration
echo [DEBUG] Updating frontend proxy configuration...
echo [DEBUG] Original frontend/vite.config.ts proxy content:
type "frontend\vite.config.ts" | findstr "target:"

powershell -Command "(Get-Content 'frontend\vite.config.ts') -replace 'target: ''http://127\.0\.0\.1:8000''', 'target: ''http://%LOCAL_IP%:8000''' | Set-Content 'frontend\vite.config.ts'"

echo [DEBUG] Updated frontend/vite.config.ts proxy content:
type "frontend\vite.config.ts" | findstr "target:"

echo [SUCCESS] Configuration updated for LAN access
echo.

:: Check dependencies
echo [DEBUG] Checking dependencies...
python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo [✓] Python is available
    python --version
) else (
    echo [✗] Python not found
)

node --version >nul 2>&1
if %errorlevel% equ 0 (
    echo [✓] Node.js is available
    node --version
) else (
    echo [✗] Node.js not found
)

:: Check if first run
if not exist "backend\xhs_notes.db" (
    echo [INFO] First run detected, initializing database...
    cd backend
    echo [STEP] Installing Python dependencies...
    pip install -r requirements.txt
    if %errorlevel% neq 0 (
        echo [ERROR] Python dependencies installation failed
        pause
        exit /b 1
    )

    echo [STEP] Initializing database...
    python init_db.py
    if %errorlevel% neq 0 (
        echo [ERROR] Database initialization failed
        pause
        exit /b 1
    )
    cd ..
    echo [SUCCESS] Database initialization completed
    echo.
)

:: Check frontend dependencies
if not exist "frontend\node_modules" (
    echo [INFO] Frontend dependencies not found, installing...
    cd frontend
    echo [STEP] Installing Node.js dependencies...
    npm install
    if %errorlevel% neq 0 (
        echo [ERROR] Node.js dependencies installation failed
        pause
        exit /b 1
    )
    cd ..
    echo [SUCCESS] Frontend dependencies installation completed
    echo.
)

echo [INFO] Starting services for LAN access...
echo.

:: Start backend service with debug
echo [START] Backend service (port: 8000) with debug logging...
start "Backend Service (Debug)" cmd /k "cd backend && echo [DEBUG] Starting backend on 0.0.0.0:8000 && python run.py"

:: Wait for backend startup
echo [WAIT] Backend service starting...
timeout /t 5 /nobreak >nul

:: Test backend connectivity
echo [DEBUG] Testing backend connectivity...
powershell -Command "try { Invoke-WebRequest -Uri 'http://127.0.0.1:8000/health' -TimeoutSec 5 -UseBasicParsing | Out-Null; Write-Host '[✓] Backend health check passed' } catch { Write-Host '[✗] Backend health check failed:' $_.Exception.Message }"

powershell -Command "try { Invoke-WebRequest -Uri 'http://%LOCAL_IP%:8000/health' -TimeoutSec 5 -UseBasicParsing | Out-Null; Write-Host '[✓] Backend LAN access check passed' } catch { Write-Host '[✗] Backend LAN access check failed:' $_.Exception.Message }"

:: Start frontend service with debug
echo [START] Frontend service (port: 3000) with debug logging...
start "Frontend Service (Debug)" cmd /k "cd frontend && echo [DEBUG] Starting frontend on 0.0.0.0:3000 && npm run dev"

:: Wait for frontend startup
echo [WAIT] Frontend service starting...
timeout /t 8 /nobreak >nul

:: Test frontend connectivity
echo [DEBUG] Testing frontend connectivity...
powershell -Command "try { Invoke-WebRequest -Uri 'http://127.0.0.1:3000' -TimeoutSec 5 -UseBasicParsing | Out-Null; Write-Host '[✓] Frontend localhost check passed' } catch { Write-Host '[✗] Frontend localhost check failed:' $_.Exception.Message }"

powershell -Command "try { Invoke-WebRequest -Uri 'http://%LOCAL_IP%:3000' -TimeoutSec 5 -UseBasicParsing | Out-Null; Write-Host '[✓] Frontend LAN access check passed' } catch { Write-Host '[✗] Frontend LAN access check failed:' $_.Exception.Message }"

echo.
echo ========================================
echo        Services Started for LAN!
echo ========================================
echo.
echo [ACCESS URLS]
echo Local Access:     http://localhost:3000
echo LAN Access:       http://%LOCAL_IP%:3000
echo Backend API:      http://%LOCAL_IP%:8000
echo API Docs:         http://%LOCAL_IP%:8000/docs
echo Health Check:     http://%LOCAL_IP%:8000/health
echo.
echo [DEBUG INFO]
echo Server IP:        %LOCAL_IP%
echo Frontend Port:    3000 (bound to 0.0.0.0)
echo Backend Port:     8000 (bound to 0.0.0.0)
echo CORS Origins:     localhost:3000, 127.0.0.1:3000, %LOCAL_IP%:3000
echo.
echo [DEFAULT ADMIN ACCOUNT]
echo Username: admin
echo Password: admin123
echo.
echo [TROUBLESHOOTING]
echo 1. Check Windows Firewall settings for ports 3000 and 8000
echo 2. Ensure both devices are on the same network
echo 3. Try accessing: http://%LOCAL_IP%:8000/docs directly
echo 4. Check the service windows for error messages
echo.
echo Press any key to continue...
pause >nul

echo.
echo [TIP] To restore original configuration, run:
echo       restore-config.bat
echo.
echo [TIP] To stop services, close the command windows
echo       or press Ctrl+C in the service windows
echo.
pause
