@echo off
title XHS Notes Manager - LAN Setup

echo ========================================
echo   XHS Notes Manager - LAN Setup
echo ========================================

:: Get local IP address
echo [INFO] Detecting IP addresses...
ipconfig | findstr "IPv4"

:: Set IP manually based on your network
:: You have two IPs: ********** and *************
:: Let's use the ************* for LAN access
set LOCAL_IP=*************

echo [INFO] Using IP: %LOCAL_IP%

:: Backup configs
if not exist "backend\.env.backup" (
    echo [BACKUP] Creating backup of backend/.env
    copy "backend\.env" "backend\.env.backup" >nul
)

if not exist "frontend\vite.config.ts.backup" (
    echo [BACKUP] Creating backup of frontend/vite.config.ts
    copy "frontend\vite.config.ts" "frontend\vite.config.ts.backup" >nul
)

:: Update backend CORS
echo [CONFIG] Updating backend CORS...
powershell -Command "(Get-Content 'backend\.env') -replace 'ALLOWED_ORIGINS=.*', 'ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000,http://%LOCAL_IP%:3000' | Set-Content 'backend\.env'"

:: Update frontend proxy
echo [CONFIG] Updating frontend proxy...
powershell -Command "(Get-Content 'frontend\vite.config.ts') -replace 'target: ''http://127\.0\.0\.1:8000''', 'target: ''http://%LOCAL_IP%:8000''' | Set-Content 'frontend\vite.config.ts'"

echo [SUCCESS] Configuration updated

:: Show current config
echo [DEBUG] Current backend CORS:
type "backend\.env" | findstr "ALLOWED_ORIGINS"

echo [DEBUG] Current frontend proxy:
type "frontend\vite.config.ts" | findstr "target:"

:: Add firewall rules
echo [FIREWALL] Adding firewall rules...
netsh advfirewall firewall add rule name="XHS Frontend" dir=in action=allow protocol=TCP localport=3000 >nul 2>&1
netsh advfirewall firewall add rule name="XHS Backend" dir=in action=allow protocol=TCP localport=8000 >nul 2>&1
echo [SUCCESS] Firewall rules added

:: Start backend
echo [START] Starting backend service...
start "Backend Service" cmd /k "cd backend && python run.py"

:: Wait
echo [WAIT] Waiting for backend startup...
timeout /t 5 /nobreak >nul

:: Test backend
echo [TEST] Testing backend...
powershell -Command "try { Invoke-WebRequest -Uri 'http://%LOCAL_IP%:8000/health' -TimeoutSec 5 -UseBasicParsing | Out-Null; Write-Host '[OK] Backend is running' } catch { Write-Host '[ERROR] Backend test failed:' $_.Exception.Message }"

:: Start frontend
echo [START] Starting frontend service...
start "Frontend Service" cmd /k "cd frontend && npm run dev"

:: Wait
echo [WAIT] Waiting for frontend startup...
timeout /t 8 /nobreak >nul

:: Test frontend
echo [TEST] Testing frontend...
powershell -Command "try { Invoke-WebRequest -Uri 'http://%LOCAL_IP%:3000' -TimeoutSec 5 -UseBasicParsing | Out-Null; Write-Host '[OK] Frontend is running' } catch { Write-Host '[ERROR] Frontend test failed:' $_.Exception.Message }"

echo ========================================
echo        Services Started!
echo ========================================
echo.
echo Local Access:     http://localhost:3000
echo LAN Access:       http://%LOCAL_IP%:3000
echo Backend API:      http://%LOCAL_IP%:8000
echo API Docs:         http://%LOCAL_IP%:8000/docs
echo Debug Page:       http://%LOCAL_IP%:3000/debug.html
echo.
echo Default Admin Account:
echo Username: admin
echo Password: admin123
echo.
echo [TROUBLESHOOTING]
echo 1. If LAN access fails, check Windows Firewall
echo 2. Try accessing backend directly: http://%LOCAL_IP%:8000/docs
echo 3. Check service windows for errors
echo.
pause
