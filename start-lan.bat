@echo off
chcp 65001 >nul
title XHS Notes Manager - LAN Access Setup

echo.
echo ========================================
echo   XHS Notes Manager - LAN Access Setup
echo ========================================
echo.

:: Get local IP address
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /c:"IPv4"') do (
    for /f "tokens=1" %%b in ("%%a") do (
        set LOCAL_IP=%%b
        goto :found_ip
    )
)

:found_ip
if "%LOCAL_IP%"=="" (
    echo [ERROR] Could not detect local IP address
    echo Please manually check your IP with: ipconfig
    pause
    exit /b 1
)

echo [INFO] Detected local IP: %LOCAL_IP%
echo.

:: Backup original configs
if not exist "backend\.env.backup" (
    echo [BACKUP] Creating backup of backend/.env
    copy "backend\.env" "backend\.env.backup" >nul
)

if not exist "frontend\vite.config.ts.backup" (
    echo [BACKUP] Creating backup of frontend/vite.config.ts
    copy "frontend\vite.config.ts" "frontend\vite.config.ts.backup" >nul
)

:: Update backend CORS configuration
echo [CONFIG] Updating backend CORS configuration...
powershell -Command "(Get-Content 'backend\.env') -replace 'ALLOWED_ORIGINS=.*', 'ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000,http://%LOCAL_IP%:3000' | Set-Content 'backend\.env'"

:: Update frontend proxy configuration
echo [CONFIG] Updating frontend proxy configuration...
powershell -Command "(Get-Content 'frontend\vite.config.ts') -replace 'target: ''http://127\.0\.0\.1:8000''', 'target: ''http://%LOCAL_IP%:8000''' | Set-Content 'frontend\vite.config.ts'"

echo [SUCCESS] Configuration updated for LAN access
echo.

:: Check if first run
if not exist "backend\xhs_notes.db" (
    echo [INFO] First run detected, initializing database...
    cd backend
    echo [STEP] Installing Python dependencies...
    pip install -r requirements.txt
    if %errorlevel% neq 0 (
        echo [ERROR] Python dependencies installation failed
        pause
        exit /b 1
    )

    echo [STEP] Initializing database...
    python init_db.py
    if %errorlevel% neq 0 (
        echo [ERROR] Database initialization failed
        pause
        exit /b 1
    )
    cd ..
    echo [SUCCESS] Database initialization completed
    echo.
)

:: Check frontend dependencies
if not exist "frontend\node_modules" (
    echo [INFO] Frontend dependencies not found, installing...
    cd frontend
    echo [STEP] Installing Node.js dependencies...
    npm install
    if %errorlevel% neq 0 (
        echo [ERROR] Node.js dependencies installation failed
        pause
        exit /b 1
    )
    cd ..
    echo [SUCCESS] Frontend dependencies installation completed
    echo.
)

echo [INFO] Starting services for LAN access...
echo.

:: Start backend service
echo [START] Backend service (port: 8000)
start "Backend Service" cmd /k "cd backend && python run.py"

:: Wait for backend startup
echo [WAIT] Backend service starting...
timeout /t 3 /nobreak >nul

:: Start frontend service
echo [START] Frontend service (port: 3000)
start "Frontend Service" cmd /k "cd frontend && npm run dev"

:: Wait for frontend startup
echo [WAIT] Frontend service starting...
timeout /t 5 /nobreak >nul

echo.
echo ========================================
echo        Services Started for LAN!
echo ========================================
echo.
echo Local Access:     http://localhost:3000
echo LAN Access:       http://%LOCAL_IP%:3000
echo Backend API:      http://%LOCAL_IP%:8000
echo API Docs:         http://%LOCAL_IP%:8000/docs
echo.
echo Default Admin Account:
echo Username: admin
echo Password: admin123
echo.
echo [TIP] Other devices on the network can access:
echo       http://%LOCAL_IP%:3000
echo.
echo Press any key to continue...
pause >nul

echo.
echo [TIP] To restore original configuration, run:
echo       restore-config.bat
echo.
echo [TIP] To stop services, close the command windows
echo       or press Ctrl+C in the service windows
echo.
pause
