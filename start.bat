@echo off
chcp 65001 >nul
title XHS Notes Manager - Quick Start

echo.
echo ========================================
echo    XHS Notes Manager - Quick Start
echo ========================================
echo.

:: Check Python installation
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Python not found, please install Python 3.11+
    echo Download: https://www.python.org/downloads/
    pause
    exit /b 1
)

:: Check Node.js installation
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Node.js not found, please install Node.js 18+
    echo Download: https://nodejs.org/
    pause
    exit /b 1
)

echo [INFO] Environment check passed
echo.

:: Check if first run
if not exist "backend\xhs_notes.db" (
    echo [INFO] First run detected, initializing database...
    cd backend
    echo [STEP] Installing Python dependencies...
    pip install -r requirements.txt
    if %errorlevel% neq 0 (
        echo [ERROR] Python dependencies installation failed
        pause
        exit /b 1
    )

    echo [STEP] Initializing database...
    python init_db.py
    if %errorlevel% neq 0 (
        echo [ERROR] Database initialization failed
        pause
        exit /b 1
    )
    cd ..
    echo [SUCCESS] Database initialization completed
    echo.
)

:: Check frontend dependencies
if not exist "frontend\node_modules" (
    echo [INFO] Frontend dependencies not found, installing...
    cd frontend
    echo [STEP] Installing Node.js dependencies...
    npm install
    if %errorlevel% neq 0 (
        echo [ERROR] Node.js dependencies installation failed
        pause
        exit /b 1
    )
    cd ..
    echo [SUCCESS] Frontend dependencies installation completed
    echo.
)

echo [INFO] Starting services...
echo.

:: Start backend service
echo [START] Backend service (port: 8000)
start "Backend Service" cmd /k "cd backend && python run.py"

:: Wait for backend startup
echo [WAIT] Backend service starting...
timeout /t 3 /nobreak >nul

:: Start frontend service
echo [START] Frontend service (port: 3000)
start "Frontend Service" cmd /k "cd frontend && npm run dev"

:: Wait for frontend startup
echo [WAIT] Frontend service starting...
timeout /t 5 /nobreak >nul

echo.
echo ========================================
echo           Services Started!
echo ========================================
echo.
echo Frontend App: http://localhost:3000
echo Backend API:  http://localhost:8000
echo API Docs:     http://localhost:8000/docs
echo.
echo Default Admin Account:
echo Username: admin
echo Password: admin123
echo.
echo Press any key to open frontend app...
pause >nul

:: Open browser
start http://localhost:3000

echo.
echo [TIP] To stop services, close the command windows
echo or press Ctrl+C in the service windows
echo.
pause
