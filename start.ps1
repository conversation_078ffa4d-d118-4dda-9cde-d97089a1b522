# PowerShell启动脚本
# 小红书笔记管理系统 - 一键启动

# 设置控制台编码为UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

# 设置窗口标题
$Host.UI.RawUI.WindowTitle = "小红书笔记管理系统 - 一键启动"

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   小红书笔记管理系统 - 一键启动" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 检查Python是否安装
Write-Host "[信息] 检查Python环境..." -ForegroundColor Blue
try {
    $pythonVersion = python --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "[成功] 检测到Python: $pythonVersion" -ForegroundColor Green
    } else {
        throw "Python未安装"
    }
} catch {
    Write-Host "[错误] 未检测到Python，请先安装Python 3.11+" -ForegroundColor Red
    Write-Host "下载地址: https://www.python.org/downloads/" -ForegroundColor Yellow
    Read-Host "按任意键退出"
    exit 1
}

# 检查Node.js是否安装
Write-Host "[信息] 检查Node.js环境..." -ForegroundColor Blue
try {
    $nodeVersion = node --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "[成功] 检测到Node.js: $nodeVersion" -ForegroundColor Green
    } else {
        throw "Node.js未安装"
    }
} catch {
    Write-Host "[错误] 未检测到Node.js，请先安装Node.js 18+" -ForegroundColor Red
    Write-Host "下载地址: https://nodejs.org/" -ForegroundColor Yellow
    Read-Host "按任意键退出"
    exit 1
}

Write-Host "[成功] 环境检查通过" -ForegroundColor Green
Write-Host ""

# 检查是否首次运行
if (!(Test-Path "backend\xhs_notes.db")) {
    Write-Host "[信息] 检测到首次运行，正在初始化数据库..." -ForegroundColor Blue
    
    Set-Location backend
    
    Write-Host "[步骤] 安装Python依赖..." -ForegroundColor Yellow
    try {
        pip install -r requirements.txt
        if ($LASTEXITCODE -ne 0) {
            throw "Python依赖安装失败"
        }
    } catch {
        Write-Host "[错误] Python依赖安装失败" -ForegroundColor Red
        Read-Host "按任意键退出"
        exit 1
    }
    
    Write-Host "[步骤] 初始化数据库..." -ForegroundColor Yellow
    try {
        python init_db.py
        if ($LASTEXITCODE -ne 0) {
            throw "数据库初始化失败"
        }
    } catch {
        Write-Host "[错误] 数据库初始化失败" -ForegroundColor Red
        Read-Host "按任意键退出"
        exit 1
    }
    
    Set-Location ..
    Write-Host "[成功] 数据库初始化完成" -ForegroundColor Green
    Write-Host ""
}

# 检查前端依赖
if (!(Test-Path "frontend\node_modules")) {
    Write-Host "[信息] 检测到前端依赖未安装，正在安装..." -ForegroundColor Blue
    
    Set-Location frontend
    
    Write-Host "[步骤] 安装Node.js依赖..." -ForegroundColor Yellow
    try {
        npm install
        if ($LASTEXITCODE -ne 0) {
            throw "Node.js依赖安装失败"
        }
    } catch {
        Write-Host "[错误] Node.js依赖安装失败" -ForegroundColor Red
        Read-Host "按任意键退出"
        exit 1
    }
    
    Set-Location ..
    Write-Host "[成功] 前端依赖安装完成" -ForegroundColor Green
    Write-Host ""
}

Write-Host "[信息] 正在启动服务..." -ForegroundColor Blue
Write-Host ""

# 启动后端服务
Write-Host "[启动] 后端服务 (端口: 8000)" -ForegroundColor Yellow
$backendJob = Start-Job -ScriptBlock {
    Set-Location $using:PWD\backend
    python run.py
}

# 等待后端启动
Write-Host "[等待] 后端服务启动中..." -ForegroundColor Blue
Start-Sleep -Seconds 3

# 启动前端服务
Write-Host "[启动] 前端服务 (端口: 3000)" -ForegroundColor Yellow
$frontendJob = Start-Job -ScriptBlock {
    Set-Location $using:PWD\frontend
    npm run dev
}

# 等待前端启动
Write-Host "[等待] 前端服务启动中..." -ForegroundColor Blue
Start-Sleep -Seconds 5

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "          服务启动完成！" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "前端应用: http://localhost:3000" -ForegroundColor Green
Write-Host "后端API:  http://localhost:8000" -ForegroundColor Green
Write-Host "API文档:  http://localhost:8000/docs" -ForegroundColor Green
Write-Host ""
Write-Host "默认管理员账户:" -ForegroundColor Yellow
Write-Host "用户名: admin" -ForegroundColor White
Write-Host "密码:   admin123" -ForegroundColor White
Write-Host ""

# 保存Job ID到文件
$backendJob.Id | Out-File -FilePath "logs\backend.job" -Encoding UTF8
$frontendJob.Id | Out-File -FilePath "logs\frontend.job" -Encoding UTF8

Write-Host "[提示] 按任意键打开前端应用..." -ForegroundColor Blue
Read-Host

# 打开浏览器
Start-Process "http://localhost:3000"

Write-Host ""
Write-Host "[提示] 要停止服务，请运行: .\stop.ps1" -ForegroundColor Yellow
Write-Host "或者手动停止PowerShell作业:" -ForegroundColor Yellow
Write-Host "  后端Job ID: $($backendJob.Id)" -ForegroundColor White
Write-Host "  前端Job ID: $($frontendJob.Id)" -ForegroundColor White
Write-Host ""
Read-Host "按任意键退出"
