#!/bin/bash

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

print_error() {
    echo -e "${RED}[错误]${NC} $1"
}

print_step() {
    echo -e "${YELLOW}[步骤]${NC} $1"
}

# 清屏并显示标题
clear
echo
echo "========================================"
echo "   小红书笔记管理系统 - 一键启动"
echo "========================================"
echo

# 检查Python是否安装
if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
    print_error "未检测到Python，请先安装Python 3.11+"
    echo "安装方法:"
    echo "  Ubuntu/Debian: sudo apt install python3 python3-pip"
    echo "  CentOS/RHEL:   sudo yum install python3 python3-pip"
    echo "  macOS:         brew install python3"
    exit 1
fi

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    print_error "未检测到Node.js，请先安装Node.js 18+"
    echo "安装方法:"
    echo "  Ubuntu/Debian: curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash - && sudo apt-get install -y nodejs"
    echo "  CentOS/RHEL:   curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash - && sudo yum install -y nodejs"
    echo "  macOS:         brew install node"
    exit 1
fi

# 检查npm是否安装
if ! command -v npm &> /dev/null; then
    print_error "未检测到npm，请确保Node.js正确安装"
    exit 1
fi

print_success "环境检查通过"
echo

# 确定Python命令
PYTHON_CMD="python3"
if ! command -v python3 &> /dev/null; then
    PYTHON_CMD="python"
fi

# 检查是否首次运行
if [ ! -f "backend/xhs_notes.db" ]; then
    print_info "检测到首次运行，正在初始化数据库..."
    
    cd backend || exit 1
    
    print_step "安装Python依赖..."
    if ! $PYTHON_CMD -m pip install -r requirements.txt; then
        print_error "Python依赖安装失败"
        exit 1
    fi
    
    print_step "初始化数据库..."
    if ! $PYTHON_CMD init_db.py; then
        print_error "数据库初始化失败"
        exit 1
    fi
    
    cd .. || exit 1
    print_success "数据库初始化完成"
    echo
fi

# 检查前端依赖
if [ ! -d "frontend/node_modules" ]; then
    print_info "检测到前端依赖未安装，正在安装..."
    
    cd frontend || exit 1
    
    print_step "安装Node.js依赖..."
    if ! npm install; then
        print_error "Node.js依赖安装失败"
        exit 1
    fi
    
    cd .. || exit 1
    print_success "前端依赖安装完成"
    echo
fi

print_info "正在启动服务..."
echo

# 创建日志目录
mkdir -p logs

# 启动后端服务
print_step "启动后端服务 (端口: 8000)"
cd backend || exit 1
nohup $PYTHON_CMD run.py > ../logs/backend.log 2>&1 &
BACKEND_PID=$!
cd .. || exit 1

# 等待后端启动
print_info "等待后端服务启动..."
sleep 3

# 检查后端是否启动成功
if ! kill -0 $BACKEND_PID 2>/dev/null; then
    print_error "后端服务启动失败，请检查日志: logs/backend.log"
    exit 1
fi

# 启动前端服务
print_step "启动前端服务 (端口: 3000)"
cd frontend || exit 1
nohup npm run dev > ../logs/frontend.log 2>&1 &
FRONTEND_PID=$!
cd .. || exit 1

# 等待前端启动
print_info "等待前端服务启动..."
sleep 5

# 检查前端是否启动成功
if ! kill -0 $FRONTEND_PID 2>/dev/null; then
    print_error "前端服务启动失败，请检查日志: logs/frontend.log"
    # 停止后端服务
    kill $BACKEND_PID 2>/dev/null
    exit 1
fi

# 保存PID到文件，方便后续停止
echo $BACKEND_PID > logs/backend.pid
echo $FRONTEND_PID > logs/frontend.pid

echo
echo "========================================"
echo "          服务启动完成！"
echo "========================================"
echo
echo "前端应用: http://localhost:3000"
echo "后端API:  http://localhost:8000"
echo "API文档:  http://localhost:8000/docs"
echo
echo "默认管理员账户:"
echo "用户名: admin"
echo "密码:   admin123"
echo
echo "日志文件:"
echo "后端日志: logs/backend.log"
echo "前端日志: logs/frontend.log"
echo
echo "要停止服务，请运行: ./stop.sh"
echo "或者手动停止进程:"
echo "  后端PID: $BACKEND_PID"
echo "  前端PID: $FRONTEND_PID"
echo

# 尝试打开浏览器（如果在桌面环境中）
if command -v xdg-open &> /dev/null; then
    print_info "正在打开浏览器..."
    xdg-open http://localhost:3000 &
elif command -v open &> /dev/null; then
    print_info "正在打开浏览器..."
    open http://localhost:3000 &
fi

print_success "启动完成！"
