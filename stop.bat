@echo off
chcp 65001 >nul
title XHS Notes Manager - Stop Services

echo.
echo ========================================
echo    XHS Notes Manager - Stop Services
echo ========================================
echo.

echo [INFO] Stopping all related services...
echo.

:: Stop Python processes (backend service)
echo [STEP] Stopping backend service...
taskkill /f /im python.exe >nul 2>&1
if %errorlevel% equ 0 (
    echo [SUCCESS] Backend service stopped
) else (
    echo [INFO] No running backend service found
)

:: Stop Node.js processes (frontend service)
echo [STEP] Stopping frontend service...
taskkill /f /im node.exe >nul 2>&1
if %errorlevel% equ 0 (
    echo [SUCCESS] Frontend service stopped
) else (
    echo [INFO] No running frontend service found
)

:: Stop possible cmd windows (if started with start command)
echo [STEP] Stopping related command windows...
taskkill /fi "windowtitle eq Backend Service*" /f >nul 2>&1
taskkill /fi "windowtitle eq Frontend Service*" /f >nul 2>&1

echo.
echo [SUCCESS] All services stopped
echo.
echo [TIP] If services are still running, please manually close
echo the command windows or end python.exe and node.exe processes
echo in Task Manager
echo.
pause
