# PowerShell停止脚本
# 小红书笔记管理系统 - 停止服务

# 设置控制台编码为UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

# 设置窗口标题
$Host.UI.RawUI.WindowTitle = "小红书笔记管理系统 - 停止服务"

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   小红书笔记管理系统 - 停止服务" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "[信息] 正在停止所有相关服务..." -ForegroundColor Blue
Write-Host ""

# 创建logs目录（如果不存在）
if (!(Test-Path "logs")) {
    New-Item -ItemType Directory -Path "logs" | Out-Null
}

# 停止PowerShell作业
if (Test-Path "logs\backend.job") {
    $backendJobId = Get-Content "logs\backend.job" -ErrorAction SilentlyContinue
    if ($backendJobId) {
        Write-Host "[步骤] 停止后端服务作业 (ID: $backendJobId)..." -ForegroundColor Yellow
        try {
            Stop-Job -Id $backendJobId -ErrorAction SilentlyContinue
            Remove-Job -Id $backendJobId -ErrorAction SilentlyContinue
            Write-Host "[成功] 后端服务作业已停止" -ForegroundColor Green
        } catch {
            Write-Host "[警告] 后端服务作业可能已经停止" -ForegroundColor Yellow
        }
    }
    Remove-Item "logs\backend.job" -ErrorAction SilentlyContinue
}

if (Test-Path "logs\frontend.job") {
    $frontendJobId = Get-Content "logs\frontend.job" -ErrorAction SilentlyContinue
    if ($frontendJobId) {
        Write-Host "[步骤] 停止前端服务作业 (ID: $frontendJobId)..." -ForegroundColor Yellow
        try {
            Stop-Job -Id $frontendJobId -ErrorAction SilentlyContinue
            Remove-Job -Id $frontendJobId -ErrorAction SilentlyContinue
            Write-Host "[成功] 前端服务作业已停止" -ForegroundColor Green
        } catch {
            Write-Host "[警告] 前端服务作业可能已经停止" -ForegroundColor Yellow
        }
    }
    Remove-Item "logs\frontend.job" -ErrorAction SilentlyContinue
}

# 停止Python进程（后端服务）
Write-Host "[步骤] 停止Python进程..." -ForegroundColor Yellow
try {
    $pythonProcesses = Get-Process -Name "python" -ErrorAction SilentlyContinue | Where-Object { $_.CommandLine -like "*run.py*" }
    if ($pythonProcesses) {
        $pythonProcesses | Stop-Process -Force
        Write-Host "[成功] Python后端进程已停止" -ForegroundColor Green
    } else {
        Write-Host "[信息] 未发现运行中的Python后端进程" -ForegroundColor Blue
    }
} catch {
    # 如果无法获取CommandLine，就停止所有python进程
    try {
        Get-Process -Name "python" -ErrorAction SilentlyContinue | Stop-Process -Force
        Write-Host "[成功] Python进程已停止" -ForegroundColor Green
    } catch {
        Write-Host "[信息] 未发现运行中的Python进程" -ForegroundColor Blue
    }
}

# 停止Node.js进程（前端服务）
Write-Host "[步骤] 停止Node.js进程..." -ForegroundColor Yellow
try {
    $nodeProcesses = Get-Process -Name "node" -ErrorAction SilentlyContinue
    if ($nodeProcesses) {
        $nodeProcesses | Stop-Process -Force
        Write-Host "[成功] Node.js前端进程已停止" -ForegroundColor Green
    } else {
        Write-Host "[信息] 未发现运行中的Node.js进程" -ForegroundColor Blue
    }
} catch {
    Write-Host "[信息] 未发现运行中的Node.js进程" -ForegroundColor Blue
}

# 停止可能占用端口的进程
Write-Host "[步骤] 检查端口占用..." -ForegroundColor Yellow

# 检查8000端口（后端）
try {
    $port8000 = netstat -ano | Select-String ":8000" | Select-String "LISTENING"
    if ($port8000) {
        $pid = ($port8000 -split '\s+')[-1]
        if ($pid -and $pid -ne "0") {
            Stop-Process -Id $pid -Force -ErrorAction SilentlyContinue
            Write-Host "[成功] 停止了占用8000端口的进程 (PID: $pid)" -ForegroundColor Green
        }
    }
} catch {
    # 忽略错误
}

# 检查3000端口（前端）
try {
    $port3000 = netstat -ano | Select-String ":3000" | Select-String "LISTENING"
    if ($port3000) {
        $pid = ($port3000 -split '\s+')[-1]
        if ($pid -and $pid -ne "0") {
            Stop-Process -Id $pid -Force -ErrorAction SilentlyContinue
            Write-Host "[成功] 停止了占用3000端口的进程 (PID: $pid)" -ForegroundColor Green
        }
    }
} catch {
    # 忽略错误
}

Write-Host ""
Write-Host "[成功] 所有服务已停止" -ForegroundColor Green
Write-Host ""
Write-Host "[提示] 如果服务仍在运行，请检查任务管理器中的进程" -ForegroundColor Yellow
Write-Host "或者重启PowerShell/命令提示符" -ForegroundColor Yellow
Write-Host ""
Read-Host "按任意键退出"
