#!/bin/bash

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

print_error() {
    echo -e "${RED}[错误]${NC} $1"
}

echo
echo "========================================"
echo "   小红书笔记管理系统 - 停止服务"
echo "========================================"
echo

# 检查PID文件是否存在
if [ -f "logs/backend.pid" ]; then
    BACKEND_PID=$(cat logs/backend.pid)
    if kill -0 $BACKEND_PID 2>/dev/null; then
        print_info "正在停止后端服务 (PID: $BACKEND_PID)..."
        kill $BACKEND_PID
        sleep 2
        if kill -0 $BACKEND_PID 2>/dev/null; then
            print_warning "强制停止后端服务..."
            kill -9 $BACKEND_PID
        fi
        print_success "后端服务已停止"
    else
        print_warning "后端服务已经停止"
    fi
    rm -f logs/backend.pid
else
    print_warning "未找到后端服务PID文件"
fi

if [ -f "logs/frontend.pid" ]; then
    FRONTEND_PID=$(cat logs/frontend.pid)
    if kill -0 $FRONTEND_PID 2>/dev/null; then
        print_info "正在停止前端服务 (PID: $FRONTEND_PID)..."
        kill $FRONTEND_PID
        sleep 2
        if kill -0 $FRONTEND_PID 2>/dev/null; then
            print_warning "强制停止前端服务..."
            kill -9 $FRONTEND_PID
        fi
        print_success "前端服务已停止"
    else
        print_warning "前端服务已经停止"
    fi
    rm -f logs/frontend.pid
else
    print_warning "未找到前端服务PID文件"
fi

# 额外检查并停止可能残留的进程
print_info "检查残留进程..."

# 查找并停止Python进程（后端）
PYTHON_PIDS=$(pgrep -f "python.*run.py" 2>/dev/null)
if [ ! -z "$PYTHON_PIDS" ]; then
    print_info "发现残留的后端进程，正在停止..."
    echo $PYTHON_PIDS | xargs kill 2>/dev/null
    sleep 1
    echo $PYTHON_PIDS | xargs kill -9 2>/dev/null
fi

# 查找并停止Node.js进程（前端）
NODE_PIDS=$(pgrep -f "node.*vite" 2>/dev/null)
if [ ! -z "$NODE_PIDS" ]; then
    print_info "发现残留的前端进程，正在停止..."
    echo $NODE_PIDS | xargs kill 2>/dev/null
    sleep 1
    echo $NODE_PIDS | xargs kill -9 2>/dev/null
fi

echo
print_success "所有服务已停止"
echo
