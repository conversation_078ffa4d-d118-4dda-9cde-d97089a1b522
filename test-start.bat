@echo off
echo Test script starting...
echo.

echo Current directory: %CD%
echo.

echo Checking Python...
python --version
if %errorlevel% neq 0 (
    echo Python check failed
    pause
    exit /b 1
) else (
    echo Python check passed
)
echo.

echo Checking Node.js...
node --version
if %errorlevel% neq 0 (
    echo Node.js check failed
    pause
    exit /b 1
) else (
    echo Node.js check passed
)
echo.

echo Checking file structure...
if exist "backend" (
    echo [OK] backend directory exists
) else (
    echo [ERROR] backend directory not found
)

if exist "frontend" (
    echo [OK] frontend directory exists
) else (
    echo [ERROR] frontend directory not found
)

if exist "backend\requirements.txt" (
    echo [OK] requirements.txt exists
) else (
    echo [ERROR] requirements.txt not found
)

if exist "frontend\package.json" (
    echo [OK] package.json exists
) else (
    echo [ERROR] package.json not found
)

echo.
echo Test script completed!
pause
