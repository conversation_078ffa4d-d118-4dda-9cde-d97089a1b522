#!/usr/bin/env python3
"""
测试修复后的功能
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.app.core.database import AsyncSessionLocal
from backend.app.models.user import UserRole
from backend.app.api.v1.endpoints.notes_download import get_notes_stats
from backend.app.api.v1.endpoints.products_download import get_products_stats


class MockUser:
    def __init__(self, role=UserRole.ADMIN):
        self.id = 1
        self.username = "admin"
        self.role = role


async def test_stats_apis():
    """测试统计API是否正常工作"""
    print("🧪 开始测试统计API...")
    
    async with AsyncSessionLocal() as db:
        mock_user = MockUser()
        
        try:
            # 测试笔记统计
            print("\n📊 测试笔记统计API...")
            notes_stats = await get_notes_stats(
                q=None,
                product_search=None,
                usage_status=None,
                product_id=None,
                db=db,
                current_user=mock_user
            )
            print("✅ 笔记统计API正常:")
            print(f"   总笔记数: {notes_stats['total_notes']}")
            print(f"   未使用笔记: {notes_stats['unused_notes']}")
            print(f"   已使用笔记: {notes_stats['used_notes']}")
            print(f"   使用率: {notes_stats['usage_rate']}%")
            
        except Exception as e:
            print(f"❌ 笔记统计API测试失败: {e}")
            import traceback
            traceback.print_exc()
        
        try:
            # 测试商品统计
            print("\n🛍️ 测试商品统计API...")
            products_stats = await get_products_stats(
                db=db,
                current_user=mock_user
            )
            print("✅ 商品统计API正常:")
            print(f"   总商品数: {products_stats['total_products']}")
            print(f"   有笔记商品: {products_stats['products_with_notes']}")
            print(f"   无笔记商品: {products_stats['products_without_notes']}")
            print(f"   笔记覆盖率: {products_stats['coverage_rate']}%")
            
        except Exception as e:
            print(f"❌ 商品统计API测试失败: {e}")
            import traceback
            traceback.print_exc()


def test_token_config():
    """测试JWT token配置"""
    print("\n🔐 测试JWT token配置...")
    
    try:
        from backend.app.core.config import settings
        print(f"✅ JWT token过期时间: {settings.access_token_expire_minutes} 分钟")
        
        # 计算天数
        days = settings.access_token_expire_minutes / (24 * 60)
        print(f"   相当于: {days:.1f} 天")
        
        if settings.access_token_expire_minutes == 43200:
            print("✅ Token过期时间已正确设置为1个月")
        else:
            print(f"⚠️ Token过期时间不是预期的43200分钟")
            
    except Exception as e:
        print(f"❌ JWT配置测试失败: {e}")


async def main():
    """主测试函数"""
    print("🚀 开始测试修复...")
    
    # 测试JWT配置
    test_token_config()
    
    # 测试统计API
    await test_stats_apis()
    
    print("\n🎉 测试完成!")


if __name__ == "__main__":
    asyncio.run(main())
