<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .image-preview {
            max-width: 200px;
            max-height: 200px;
            margin: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>🖼️ 图片功能测试页面</h1>
    
    <div class="test-section">
        <h2>📁 文件名时间戳测试</h2>
        <p>测试下载文件名是否包含时间戳</p>
        <button onclick="testTimestampFilename()">测试时间戳文件名生成</button>
        <div id="timestamp-result"></div>
    </div>

    <div class="test-section">
        <h2>🔗 图片URL构建测试</h2>
        <p>测试图片URL是否正确构建</p>
        <button onclick="testImageUrl()">测试图片URL构建</button>
        <div id="url-result"></div>
    </div>

    <div class="test-section">
        <h2>⬇️ 下载功能测试</h2>
        <p>测试图片下载功能（使用示例图片）</p>
        <button onclick="testDownload()">测试单个图片下载</button>
        <button onclick="testBatchDownload()">测试批量图片下载</button>
        <div id="download-result"></div>
    </div>

    <div class="test-section">
        <h2>🎯 API端点测试</h2>
        <p>测试后端API是否正常工作</p>
        <button onclick="testApiEndpoints()">测试API端点</button>
        <div id="api-result"></div>
    </div>

    <script>
        // 模拟前端下载工具函数
        const generateTimestampedFilename = (baseName, extension = 'jpg') => {
            const now = new Date()
            const timestamp = now.toISOString()
                .replace(/[:.]/g, '-')
                .replace('T', '_')
                .slice(0, 19)
            
            return `${baseName}_${timestamp}.${extension}`
        }

        const getFileExtensionFromUrl = (url) => {
            try {
                const cleanUrl = url.split('?')[0].split('#')[0]
                const parts = cleanUrl.split('.')
                if (parts.length > 1) {
                    return parts[parts.length - 1].toLowerCase()
                }
            } catch (error) {
                console.warn('Failed to extract extension from URL:', url, error)
            }
            return 'jpg'
        }

        const downloadImageWithTimestamp = (imageUrl, baseName, showToast) => {
            try {
                const extension = getFileExtensionFromUrl(imageUrl)
                const filename = generateTimestampedFilename(baseName, extension)
                
                const link = document.createElement('a')
                link.href = imageUrl
                link.download = filename
                link.target = '_blank'
                
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link)
                
                if (showToast) {
                    showToast(`开始下载: ${filename}`)
                }
                return filename
            } catch (error) {
                console.error('Download failed:', error)
                if (showToast) {
                    showToast('下载失败')
                }
                return null
            }
        }

        // 测试函数
        function testTimestampFilename() {
            const result = document.getElementById('timestamp-result')
            try {
                const filename1 = generateTimestampedFilename('test_image')
                const filename2 = generateTimestampedFilename('product_main', 'png')
                const filename3 = generateTimestampedFilename('note_cover', 'webp')
                
                result.innerHTML = `
                    <div class="test-result success">
                        <h4>✅ 时间戳文件名生成成功</h4>
                        <p><strong>示例1:</strong> ${filename1}</p>
                        <p><strong>示例2:</strong> ${filename2}</p>
                        <p><strong>示例3:</strong> ${filename3}</p>
                        <p><strong>格式:</strong> 基础名_YYYY-MM-DD_HH-MM-SS.扩展名</p>
                    </div>
                `
            } catch (error) {
                result.innerHTML = `
                    <div class="test-result error">
                        <h4>❌ 时间戳文件名生成失败</h4>
                        <p>错误: ${error.message}</p>
                    </div>
                `
            }
        }

        function testImageUrl() {
            const result = document.getElementById('url-result')
            try {
                // 模拟不同的文件路径
                const testPaths = [
                    'images/test.jpg',
                    '/uploads/images/test.jpg',
                    'http://localhost:3000/uploads/images/test.jpg'
                ]
                
                const baseUrl = window.location.origin
                const results = testPaths.map(path => {
                    let finalUrl
                    if (path.startsWith('http://') || path.startsWith('https://')) {
                        finalUrl = path
                    } else if (path.startsWith('/uploads/')) {
                        finalUrl = `${baseUrl}${path}`
                    } else {
                        finalUrl = `${baseUrl}/uploads/${path}`
                    }
                    return { original: path, final: finalUrl }
                })
                
                result.innerHTML = `
                    <div class="test-result success">
                        <h4>✅ 图片URL构建测试</h4>
                        ${results.map(r => `
                            <p><strong>原始路径:</strong> ${r.original}</p>
                            <p><strong>最终URL:</strong> ${r.final}</p>
                            <hr>
                        `).join('')}
                    </div>
                `
            } catch (error) {
                result.innerHTML = `
                    <div class="test-result error">
                        <h4>❌ URL构建测试失败</h4>
                        <p>错误: ${error.message}</p>
                    </div>
                `
            }
        }

        function testDownload() {
            const result = document.getElementById('download-result')
            try {
                // 使用一个示例图片URL（可以是任何公开的图片）
                const testImageUrl = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iIzAwN2JmZiIvPjx0ZXh0IHg9IjUwIiB5PSI1NSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+VGVzdDwvdGV4dD48L3N2Zz4='
                
                const filename = downloadImageWithTimestamp(testImageUrl, 'test_download', (msg) => {
                    console.log(msg)
                })
                
                result.innerHTML = `
                    <div class="test-result success">
                        <h4>✅ 单个图片下载测试</h4>
                        <p>生成的文件名: ${filename}</p>
                        <p>请检查浏览器下载文件夹</p>
                    </div>
                `
            } catch (error) {
                result.innerHTML = `
                    <div class="test-result error">
                        <h4>❌ 下载测试失败</h4>
                        <p>错误: ${error.message}</p>
                    </div>
                `
            }
        }

        function testBatchDownload() {
            const result = document.getElementById('download-result')
            try {
                const testImages = [
                    'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iIzAwN2JmZiIvPjx0ZXh0IHg9IjUwIiB5PSI1NSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+VGVzdDE8L3RleHQ+PC9zdmc+',
                    'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iIzI4YTc0NSIvPjx0ZXh0IHg9IjUwIiB5PSI1NSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+VGVzdDI8L3RleHQ+PC9zdmc+',
                    'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iI2RjMzU0NSIvPjx0ZXh0IHg9IjUwIiB5PSI1NSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+VGVzdDM8L3RleHQ+PC9zdmc+'
                ]
                
                testImages.forEach((imageUrl, index) => {
                    setTimeout(() => {
                        const baseName = `batch_test_${index + 1}`
                        downloadImageWithTimestamp(imageUrl, baseName, undefined)
                    }, index * 100)
                })
                
                result.innerHTML = `
                    <div class="test-result success">
                        <h4>✅ 批量图片下载测试</h4>
                        <p>开始下载 ${testImages.length} 张测试图片</p>
                        <p>请检查浏览器下载文件夹</p>
                    </div>
                `
            } catch (error) {
                result.innerHTML = `
                    <div class="test-result error">
                        <h4>❌ 批量下载测试失败</h4>
                        <p>错误: ${error.message}</p>
                    </div>
                `
            }
        }

        async function testApiEndpoints() {
            const result = document.getElementById('api-result')
            result.innerHTML = `
                <div class="test-result info">
                    <h4>🔄 正在测试API端点...</h4>
                </div>
            `
            
            try {
                const baseUrl = window.location.origin.replace(':3000', ':8000') // 假设后端在8000端口
                const endpoints = [
                    '/api/v1/notes-download/stats',
                    '/api/v1/products-download/stats'
                ]
                
                const results = []
                for (const endpoint of endpoints) {
                    try {
                        const response = await fetch(`${baseUrl}${endpoint}`, {
                            headers: {
                                'Authorization': `Bearer ${localStorage.getItem('token') || 'test-token'}`
                            }
                        })
                        results.push({
                            endpoint,
                            status: response.status,
                            success: response.ok
                        })
                    } catch (error) {
                        results.push({
                            endpoint,
                            status: 'Error',
                            success: false,
                            error: error.message
                        })
                    }
                }
                
                result.innerHTML = `
                    <div class="test-result ${results.every(r => r.success) ? 'success' : 'error'}">
                        <h4>${results.every(r => r.success) ? '✅' : '⚠️'} API端点测试结果</h4>
                        ${results.map(r => `
                            <p><strong>${r.endpoint}:</strong> 
                            ${r.success ? '✅' : '❌'} ${r.status}
                            ${r.error ? ` (${r.error})` : ''}
                            </p>
                        `).join('')}
                    </div>
                `
            } catch (error) {
                result.innerHTML = `
                    <div class="test-result error">
                        <h4>❌ API测试失败</h4>
                        <p>错误: ${error.message}</p>
                    </div>
                `
            }
        }

        // 页面加载时显示环境信息
        window.onload = function() {
            console.log('🚀 图片功能测试页面已加载')
            console.log('当前环境:', {
                origin: window.location.origin,
                userAgent: navigator.userAgent,
                timestamp: new Date().toISOString()
            })
        }
    </script>
</body>
</html>
