#!/usr/bin/env python3
"""
测试启动脚本的功能
检查服务是否正常启动
"""
import requests
import time
import sys
import subprocess
import os
from pathlib import Path

def print_status(message, status="INFO"):
    colors = {
        "INFO": "\033[94m",
        "SUCCESS": "\033[92m", 
        "WARNING": "\033[93m",
        "ERROR": "\033[91m",
        "END": "\033[0m"
    }
    print(f"{colors.get(status, '')}{status}: {message}{colors['END']}")

def check_service(url, service_name, timeout=30):
    """检查服务是否启动"""
    print_status(f"检查{service_name}服务...")
    
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print_status(f"{service_name}服务正常运行", "SUCCESS")
                return True
        except requests.exceptions.RequestException:
            pass
        
        time.sleep(2)
    
    print_status(f"{service_name}服务启动失败或超时", "ERROR")
    return False

def check_api_endpoints():
    """检查主要API端点"""
    endpoints = [
        ("http://localhost:8000/docs", "API文档"),
        ("http://localhost:8000/api/v1/auth/test", "认证测试"),
    ]
    
    for url, name in endpoints:
        try:
            response = requests.get(url, timeout=5)
            if response.status_code in [200, 404, 422]:  # 404和422也是正常的，说明服务在运行
                print_status(f"{name} - 可访问", "SUCCESS")
            else:
                print_status(f"{name} - 状态码: {response.status_code}", "WARNING")
        except requests.exceptions.RequestException as e:
            print_status(f"{name} - 连接失败: {e}", "ERROR")

def check_files():
    """检查必要文件是否存在"""
    files_to_check = [
        "start.bat",
        "start.sh", 
        "stop.bat",
        "stop.sh",
        "QUICK_START.md",
        "backend/requirements.txt",
        "frontend/package.json"
    ]
    
    print_status("检查启动脚本文件...")
    all_exist = True
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print_status(f"✓ {file_path}", "SUCCESS")
        else:
            print_status(f"✗ {file_path} 不存在", "ERROR")
            all_exist = False
    
    return all_exist

def main():
    print("=" * 50)
    print("小红书笔记管理系统 - 启动测试")
    print("=" * 50)
    print()
    
    # 检查文件
    if not check_files():
        print_status("启动脚本文件检查失败", "ERROR")
        return False
    
    print()
    print_status("开始检查服务状态...")
    print_status("请确保已经运行了启动脚本", "WARNING")
    print()
    
    # 检查后端服务
    backend_ok = check_service("http://localhost:8000", "后端")
    
    # 检查前端服务
    frontend_ok = check_service("http://localhost:3000", "前端")
    
    if backend_ok and frontend_ok:
        print()
        print_status("所有服务正常运行！", "SUCCESS")
        
        # 检查API端点
        print()
        print_status("检查API端点...")
        check_api_endpoints()
        
        print()
        print_status("测试完成！可以开始使用系统了。", "SUCCESS")
        print_status("前端地址: http://localhost:3000", "INFO")
        print_status("后端API: http://localhost:8000/docs", "INFO")
        return True
    else:
        print()
        print_status("服务启动不完整，请检查启动脚本输出", "ERROR")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print_status("\n测试被用户中断", "WARNING")
        sys.exit(1)
    except Exception as e:
        print_status(f"测试过程中发生错误: {e}", "ERROR")
        sys.exit(1)
